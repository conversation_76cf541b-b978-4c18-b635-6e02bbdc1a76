﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Pages.Email;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Details
{
    [Inject]
    private IEmailDeliveryService Service { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public string DeliveryId { get; set; }

    private DeliveryDetailedData Delivery { get; set; }

    private bool PageLoading { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await RefreshPageAsync();
    }

    private async Task RefreshPageAsync()
    {
        PageLoading = true;

        try
        {
            Delivery = await Service.GetDetailsAsync(Customer, Guid.Parse(DeliveryId));
            PageLoading = false;
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }

        await InvokeAsync(StateHasChanged);
    }
}
