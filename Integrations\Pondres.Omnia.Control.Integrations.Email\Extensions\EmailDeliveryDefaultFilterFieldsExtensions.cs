﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Email.Models;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Extensions
{
    public static class EmailDeliveryDefaultFilterFieldsExtensions
    {
        public static DeliveryDefaultFilter ToApiFilter(this EmailDeliveryDefaultFilterFields fields, string continuationToken, string customer) =>
            new()
            {
                OrderId = !string.IsNullOrEmpty(fields.OrderId) ? Guid.Parse(fields.OrderId) : null,
                CreatedFromDate = fields.CreatedDateTime.GetFromDate(),
                CreatedToDate = fields.CreatedDateTime.GetToDate(),
                Customer = customer,
                CustomerReference = fields.CustomerReference,
                DeliveryId = !string.IsNullOrEmpty(fields.DeliveryId) ? Guid.Parse(fields.DeliveryId) : null,
                Flow = fields.Flow.ToDropdownInvariantValue(),
                StateTypeName = fields.StateTypeName.ToDropdownInvariantValue(),
                MaxPageSize = fields.PageSize,
                ContinuationToken = continuationToken
            };
    }
}
