﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Models;

public class NotificationTraceDefaultFilterFields : FilterFieldsBase<NotificationTraceDefaultFilterFields>
{
    public string Customer { get; set; }
    public string EventType { get; set; } = ControlConstants.DefaultDropdownValue;
    public string Status { get; set; } = ControlConstants.DefaultDropdownValue;
    public string OrderId { get; set; }
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
}
