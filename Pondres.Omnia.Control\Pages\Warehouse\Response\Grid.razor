﻿@page "/WMS/Responses"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Response
@using Pondres.Omnia.Control.Integrations.Warehouse.Models
@using Pondres.Omnia.Warehouse.Contracts.Response

@inherits CustomerFilterGridPageBase<WarehouseResponseListItem, IWarehouseResponseFilterContext>

<PageHeader Title="WMS Responses" />
<PageBody>
    <div class="container-fluid">
        <ResponsesGridFilter FilterChanged="OnFilterChangedAsync" />

        <ResponsesGrid Responses="GridItems" Loading="LoadingGridItems" OnRefresh="OnRefresh" />
    </div>
</PageBody>