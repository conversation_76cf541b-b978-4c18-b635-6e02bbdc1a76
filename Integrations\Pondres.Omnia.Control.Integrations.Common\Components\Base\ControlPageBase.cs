﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Routing;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.ViewModels;
using System.ComponentModel;

namespace Pondres.Omnia.Control.Integrations.Common.Components
{
    public abstract class ControlPageBase : NotifyPropertyChanged, IDisposable
    {
        [Inject]
        public NavigationManager NavigationManager { get; set; }

        [Inject]
        public AuthenticationStateProvider AuthenticationStateProvider { get; set; }

        public string CurrentUser { get; private set; }

        protected ControlPageBase()
        {
            PropertyChanged -= OnPropertyChanged;
            PropertyChanged += OnPropertyChanged;
        }

        protected override async Task OnInitializedAsync()
        {
            NavigationManager.LocationChanged -= OnLocationChanged;
            NavigationManager.LocationChanged += OnLocationChanged;

            var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            CurrentUser = state.GetUsername();

            await base.OnInitializedAsync();
        }

        /// <summary>
        /// Method that runs when the page has been navigated to. Use with caution.
        /// </summary>
        /// <param name="sender">The sender.</param>
        /// <param name="e">The <see cref="LocationChangedEventArgs"/> instance containing the event data.</param>
        protected virtual void OnLocationChanged(object sender, LocationChangedEventArgs e)
        { }

        protected virtual void OnPropertyChanged(object sender, PropertyChangedEventArgs e)
        { }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        { }
    }
}