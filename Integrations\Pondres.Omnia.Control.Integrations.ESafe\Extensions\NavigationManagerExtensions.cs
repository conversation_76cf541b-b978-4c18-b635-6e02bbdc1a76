﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.ESafe.Models;

namespace Pondres.Omnia.Control.Integrations.ESafe.Extensions;

public static class NavigationManagerExtensions
{
    public static void NavigateToESafeDeliveryListWithDefaultFilter(this NavigationManager navigationManager, ESafeDeliveryDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"ESafe/Deliveries?{FilterHelper.ConvertToQueryParameter(filter)}");

    public static void NavigateToESafeDeliveryListWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"ESafe/Deliveries?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");
}
