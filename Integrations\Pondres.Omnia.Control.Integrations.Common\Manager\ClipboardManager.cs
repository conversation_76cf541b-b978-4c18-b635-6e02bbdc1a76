﻿using Microsoft.JSInterop;

namespace Pondres.Omnia.Control.Integrations.Common.Manager;

public class ClipboardManager : IClipboardManager
{
    private readonly IJSRuntime javascriptRuntime;

    public ClipboardManager(IJSRuntime javascriptRuntime) =>
        this.javascriptRuntime = javascriptRuntime;

    public async Task CopyToClipboardAsync(string value) =>
        await javascriptRuntime.InvokeVoidAsync("navigator.clipboard.writeText", value);
}
