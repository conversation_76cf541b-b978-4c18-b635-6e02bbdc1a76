﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;

public partial class DocumentArticlesGrid
{
    [Parameter]
    public List<string> ArticleCodes { get; set; }

    private static string WhiteRowColor =>
        "table-success-light";

    private Task<PagedResultModel<string>> GetGridItemsResultAsync() =>
        Task.FromResult(new PagedResultModel<string>() { Items = ArticleCodes });
}
