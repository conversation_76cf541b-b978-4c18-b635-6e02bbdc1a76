﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Extensions;

public static class WarehouseDocumentDefaultFilterFieldsExtensions
{
    public static WarehouseDocumentDefaultFilter ToApiFilter(this WarehouseDocumentDefaultFilterFields fields, string continuationToken) =>
        new()
        {
            OrderId = fields.OrderId.ToNullableGuid(),
            DocumentReference = fields.DocumentReference,
            CreatedOnFrom = fields.CreatedDateTime.GetFromDate(),
            CreatedOnTo = fields.CreatedDateTime.GetToDate(),
            StatusCode = fields.StatusCode.ToNullableInt(),
            FileName = fields.FileName,
            MaxPageSize = fields.PageSize,
            ContinuationToken = continuationToken
        };
}
