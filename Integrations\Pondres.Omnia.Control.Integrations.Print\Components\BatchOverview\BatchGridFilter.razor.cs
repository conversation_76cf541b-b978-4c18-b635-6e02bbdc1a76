﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.BatchOverview
{
    public partial class BatchGridFilter
    {
        [Inject]
        private IApiFilterManager FilterManager { get; set; }

        [Inject]
        private PrintBatchDefaultFilterContext Filter { get; set; }

        [Parameter]
        public EventCallback<IPrintBatchFilterContext> FilterChanged { get; set; }

        protected override async Task OnInitializedAsync()
        {
            FilterManager.SetFilters(Filter);
            await FilterManager.SetInitialFilterFromUriAsync();

            if (FilterChanged.HasDelegate)
                await FilterChanged.InvokeAsync(Filter);
        }

        private async void OnFilterChanged(IPrintBatchFilterContext filter)
        {
            await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);

            if (FilterChanged.HasDelegate)
                await FilterChanged.InvokeAsync(filter);
        }
    }
}
