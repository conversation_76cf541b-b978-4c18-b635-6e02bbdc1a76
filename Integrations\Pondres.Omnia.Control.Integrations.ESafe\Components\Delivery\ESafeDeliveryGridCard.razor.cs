﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.ESafe.Services;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Components.Delivery;

public partial class ESafeDeliveryGridCard
{
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private IESafeDeliveryService ESafeService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public Guid OrderId { get; set; }

    private void NavigateToDelivery(Guid deliveryId) =>
        NavigationManager.NavigateTo($"ESafe/{Customer}/{deliveryId}");

    public async Task<PagedResultModel<DeliveryListItem>> GetNextItemsAsync(string continuationToken) =>
        await ESafeService.GetPagedListAsync(CreateFilter(continuationToken));

    private DeliveryListFilter CreateFilter(string continuationToken) =>
        new() { OrderId = OrderId, Customer = Customer, ContinuationToken = continuationToken }; 
}
