﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Models;

public class SecureMailDeliveryDefaultFilterFields : FilterFieldsBase<SecureMailDeliveryDefaultFilterFields>
{
    public string Customer { get; set; }
    public string CustomerReference { get; set; }
    public string OrderId { get; set; }
    public string Flow { get; set; } = ControlConstants.DefaultDropdownValue;
    public string StateTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    public string ResultTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    public string Connector { get; set; }
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
}
