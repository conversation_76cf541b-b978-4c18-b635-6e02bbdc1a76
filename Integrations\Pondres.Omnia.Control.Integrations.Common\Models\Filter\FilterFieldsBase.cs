﻿using Pondres.Omnia.Control.Integrations.Common.Constants;

namespace Pondres.Omnia.Control.Integrations.Common.Models.Filter;

public abstract class FilterFieldsBase<TFilterFields>
{
    public int PageSize { get; set; } = ControlConstants.DefaultPageSize;

    public void Merge(TFilterFields filterFields)
    {
        if (filterFields == null)
            return;

        var properties = filterFields.GetType().GetProperties().ToList();

        foreach (var property in properties)
            property.SetValue(this, property.GetValue(filterFields));
    }
}
