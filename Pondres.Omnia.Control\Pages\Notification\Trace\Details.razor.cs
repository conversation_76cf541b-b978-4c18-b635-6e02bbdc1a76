using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Pondres.Omnia.Control.Integrations.Common.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Pages.Notification.Trace;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Details : ControlPageBase
{
    [Inject]
    private INotificationTraceService NotificationTraceService { get; set; }

    [Inject]
    private IFileService FileService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }

    [Parameter]
    public string TraceId { get; set; }

    [Parameter]
    public string Customer { get; set; }

    private NotificationTraceDetails Trace { get; set; }
    private bool InvalidResponse { get; set; } = false;
    private bool LoadingTraces { get; set; } = true;
    private FileDisplayModal FileDetailsModal { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await StartLoadingAsync();
                Trace = await NotificationTraceService.GetTraceDetailsAsync(TraceId, Customer);
            }
            catch (Exception ex)
            {
                InvalidResponse = true;
                ToastService.ShowError(ex.Message);
            }

            await StopLoadingAsync();
        }
    }

    private async Task RefreshPageAsync()
    {
        LoadingTraces = true;
        try
        {
            Trace = await NotificationTraceService.GetTraceDetailsAsync(TraceId, Customer);
        }
        catch (Exception ex)
        {
            InvalidResponse = true;
            ToastService.ShowError(ex.Message);
        }

        LoadingTraces = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task StartLoadingAsync()
    {
        LoadingTraces = true;
        await InvokeAsync(StateHasChanged);
    }

    private async Task StopLoadingAsync()
    {
        LoadingTraces = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task OpenFileModalAsync(string filePath)
    {
        try
        {
            var file = await GetFileDetailsAsync(filePath);
            await FileDetailsModal.OpenAsync(file);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }

    private async Task<FileModel> GetFileDetailsAsync(string filePath)
    {
        return await FileService.GetFileAsync(Customer, CurrentUser, Trace.FilesContainerName, filePath);
    }

    private async Task DownloadFileAsync(string filePath)
    {
        var file = await GetFileDetailsAsync(filePath);
        using var fileStream = GetFileStream(file.Contents);
        using var streamRef = new DotNetStreamReference(stream: fileStream);
        var fileName = file.FilePath;
        if (fileName.Contains("source"))
            fileName = fileName.Replace("source", "notification");

        await JSRuntime.InvokeVoidAsync("downloadFileFromStream", fileName, streamRef);
    }

    private static Stream GetFileStream(string stringContents)
    {
        var stream = new MemoryStream();

        var writer = new StreamWriter(stream);
        writer.Write(stringContents);
        writer.Flush();

        stream.Position = 0;

        return stream;
    }
}