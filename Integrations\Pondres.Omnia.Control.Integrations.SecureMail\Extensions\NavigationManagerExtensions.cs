﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Extensions;
public static class NavigationManagerExtensions
{
    public static void NavigateToSecureMailDeliveryListWithDefaultFilter(this NavigationManager navigationManager, SecureMailDeliveryDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"SecureMail/Deliveries?{FilterHelper.ConvertToQueryParameter(filter)}");

    public static void NavigateToSecureMailDeliveryListWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"SecureMail/Deliveries?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");
}
