﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.OrderHub.Contracts.Api.Order

<GridCard Title="Order Rejection Triggers"
          GetRawColorFunc="(trigger) => trigger.GetStatusColorStyle()"
          GetItemsAsync="(_) => Task.FromResult(Order.RejectionTriggers.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Type name</th>
        <th>Received on</th>
    </TableHeaderContent>
    <TableRowContent Context="trigger">
        <td>@trigger.TypeName</td>
        <td>@trigger.ReceivedOn?.LocalDateTime</td>
    </TableRowContent>
</GridCard>


