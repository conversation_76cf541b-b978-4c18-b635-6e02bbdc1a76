﻿@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Pondres.Omnia.Control.Integrations.Common.Components.Form
@using Pondres.Omnia.Control.Integrations.Common.Constants

<EditForm EditContext="@EditContext">
    <div class="form-row">
        <InputMultiLineDialog @bind-Value="Filter.Fields.CustomerReferences" FieldName="Customer References" ItemName="reference" />
        <InputMultiLineDialog @bind-Value="Filter.Fields.DeliveryIds" FieldName="Delivery Ids" ItemName="deliveryid" />
    </div>
    
    <div class="pt-2">
        <div class="float-right">
            <label class="col-form-label ml-3">Page size</label>
            <InputSelect class="form-control" style="width: unset; display: unset;" @bind-Value="Filter.Fields.PageSize">
                @foreach (var pageSize in ControlConstants.PageSizes)
                {
                    <option value="@pageSize">@pageSize</option>
                }
            </InputSelect>

            <button class="btn btn-outline-primary ml-1" @onclick="ClearFilterAsync"><i class="oi oi-reload mr-2"></i>Reset</button>
        </div>
    </div>
</EditForm>