﻿using Pondres.Omnia.Intersolve.Contracts.Configuration;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Extensions
{
    public static class ConfigurationExtensions
    {
        public static string GetCustomerFromJson(this string json) =>
            JsonSerializer.Deserialize<Configuration>(json).Customer;

        public static CreateConfiguration ToCreateDto(this string json) =>
            JsonSerializer.Deserialize<CreateConfiguration>(json);

        public static UpdateConfiguration ToUpdateDto(this string json) =>
            JsonSerializer.Deserialize<UpdateConfiguration>(json);

        public static string ToJson(this Configuration configuration, JsonSerializerOptions options) =>
            JsonSerializer.Serialize(configuration, options);

        public static string ToJson(this CreateConfiguration configuration, JsonSerializerOptions options) =>
            JsonSerializer.Serialize(configuration, options);

        public static string ToJson(this UpdateConfiguration configuration, JsonSerializerOptions options) =>
            JsonSerializer.Serialize(configuration, options);
    }
}
