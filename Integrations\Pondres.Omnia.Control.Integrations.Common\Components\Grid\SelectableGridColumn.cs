﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Grid;

public class SelectableGridColumn : GridColumn
{
    [Parameter]
    public EventCallback<SelectableGridColumn> SelectionChanged { get; set; }

    [Parameter]
    public RenderFragment TitleFragment { get; set; }

    [Parameter]
    public bool IsEnabled { get; set; } = true;

    public bool IsSelected { get; private set; }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();

        if (HeaderTemplate == null)
            HeaderTemplate = GetHeaderTemplate();

        if (!IsEnabled)
            Id = $"{Id}_Disabled";
    }

    private RenderFragment GetHeaderTemplate()
    {
        void headerTemplate(RenderTreeBuilder builder)
        {
            builder.OpenElement(1, "label");
            builder.AddAttribute(1, "class", "k-checkbox-label");
            builder.OpenComponent(2, typeof(TelerikCheckBox<bool>));

            if (!IsEnabled)
                builder.AddAttribute(2, "Enabled", IsEnabled);
            else
                builder.AddAttribute(2, "ValueChanged", EventCallback.Factory.Create<bool>(this, CheckChangedAsync));

            builder.CloseComponent();

            if (TitleFragment == null)
                builder.AddContent(3, Title);
            else
                builder.AddContent(3, TitleFragment);

            builder.CloseElement();
        }

        return headerTemplate;
    }

    private async Task CheckChangedAsync(bool isSelected)
    {
        if (IsSelected != isSelected)
        {
            IsSelected = isSelected;

            await SelectionChanged.InvokeAsync(this);
        }
    }
}
