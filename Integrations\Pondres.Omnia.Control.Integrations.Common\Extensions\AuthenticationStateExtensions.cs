﻿using Microsoft.AspNetCore.Components.Authorization;

namespace Pondres.Omnia.Control.Integrations.Common.Extensions;

public static class AuthenticationStateExtensions
{
    public static string GetUsername(this AuthenticationState authenticationState)
    {
        return authenticationState.User.Claims.SingleOrDefault(x => x.Type == "name")?.Value ??
               authenticationState.User.Identity.Name;
    }
}