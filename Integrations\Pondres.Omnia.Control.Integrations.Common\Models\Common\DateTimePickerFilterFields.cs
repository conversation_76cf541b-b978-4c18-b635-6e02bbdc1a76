﻿namespace Pondres.Omnia.Control.Integrations.Common.Models.Common;

public class DateTimePickerFilterFields
{
    public DateTimeOffset? FromDate { get; set; }
    public DateTimeOffset? FromTime { get; set; }

    public DateTimeOffset? ToDate { get; set; }
    public DateTimeOffset? ToTime { get; set; }

    public DateTimeOffset? GetFromDate()
    {
        if (FromTime.HasValue)
            FromDate = FromDate.HasValue ? CombineDateAndTime(FromDate.Value, FromTime.Value) : FromTime;
        else
            FromTime = FromDate.HasValue ? FromDate.Value.Date : null;

        return FromDate;
    }

    public DateTimeOffset? GetToDate()
    {
        if (ToTime.HasValue)
            ToDate = ToDate.HasValue ? CombineDateAndTime(ToDate.Value, ToTime.Value) : ToTime;
        else
            ToTime = ToDate.HasValue ? ToDate.Value.Date : null;

        return ToDate;
    }

    public bool HasValues() =>
        FromDate.HasValue &&
        FromTime.HasValue &&
        ToDate.HasValue &&
        ToTime.HasValue;

    private static DateTime CombineDateAndTime(DateTimeOffset date, DateTimeOffset time)
    {
        return date.Date + time.TimeOfDay;
    }

    public static DateTimePickerFilterFields Today()
    {
        var now = DateTimeOffset.Now;

        return new DateTimePickerFilterFields
        {
            FromDate = now.Date,
            FromTime = now.Date,
            ToDate = now.AddDays(1).Date,
            ToTime = now.AddDays(1).Date
        };
    }


}
