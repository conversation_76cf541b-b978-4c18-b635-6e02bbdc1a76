using Microsoft.AspNetCore.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;

public partial class TabPanelHeader<TDataType>
{
    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public string NavigationType { get; set; } = "tabs";
    [CascadingParameter]
    TabPanel<TDataType> Panel { get; set; }

    public bool TitleHasBeenSet() => !string.IsNullOrWhiteSpace(Title);
}
