﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Form;

<ListFilterInputTemplate Stretch="false" LabelText="@GetLabelFrom()">
    <EditForm class="row" EditContext="EditContext">
        <div class="col-8 pr-1">
            <InputDate class="form-control border-1 small" placeholder="From Date" @bind-Value="Value.FromDate" />
        </div>
        <div class="col-4 pl-1">
            <InputTime class="form-control border-1 small" @bind-Value="Value.FromTime" />
        </div>
    </EditForm>
</ListFilterInputTemplate>

<ListFilterInputTemplate Stretch="false" LabelText="@GetLabelTo()">
    <EditForm class="row" EditContext="EditContext">
        <div class="col-8 pr-1">
            <InputDate class="form-control border-1 small" placeholder="To Date" @bind-Value="Value.ToDate" />
        </div>
        <div class="col-4 pl-1">
            <InputTime class="form-control border-1 small" @bind-Value="Value.ToTime" />
        </div>
    </EditForm>
</ListFilterInputTemplate>


