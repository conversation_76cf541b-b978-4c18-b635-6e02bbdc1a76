﻿using Pondres.Omnia.NotificationHub.Contracts.Api.Definition;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Services;

public interface INotificationDefinitionService
{
    Task<List<NotificationDefinitionModel>> GetDefinitionsListAsync(string customer);

    Task<string> GetDefinitionAsync(string name, string type);

    Task<string> GetDefinitionSchemaAsync(string type, string customer);

    Task UpdateDefinitionAsync(UpsertNotificationDefinitionModel updateModel);

    Task CreateDefinitionAsync(UpsertNotificationDefinitionModel createModel);
}
