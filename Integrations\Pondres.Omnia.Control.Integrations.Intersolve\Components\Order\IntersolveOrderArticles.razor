﻿@using System.Text.Json
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid

<GridCard Title="Articles"
          GetRawColorFunc="(order) => string.Empty"
          GetItemsAsync="GetNextItemsAsync">
    <TableHeaderContent>
        <th>Code</th>
        <th>Value</th>
        <th>ExtItemReference</th>
    </TableHeaderContent>
    <TableRowContent>
        <td>@context.Code</td>
        <td>@context.Value</td>
        <td>@context.ExtItemReference</td>
    </TableRowContent>
</GridCard>