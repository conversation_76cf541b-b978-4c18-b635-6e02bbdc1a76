﻿@using Pondres.Omnia.OrderHub.Contracts.Order

@if (Order != null && Order.Status.StateType != OrderStateType.Completed)
{
    <TelerikCard>
        <CardHeader>
            <CardTitle>
                <h6 class="m-0 font-weight-bold text-primary pt-2">Active status </h6>
                <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="LoadActiveStatusAsync">Load active Status</TelerikButton>
            </CardTitle>
        </CardHeader>
        <CardBody>
            <div hidden="@(OrderActiveStatus == null)">
                <pre class="p-4 m-0">@OrderActiveStatus</pre>
            </div>
        </CardBody>
    </TelerikCard>
}


