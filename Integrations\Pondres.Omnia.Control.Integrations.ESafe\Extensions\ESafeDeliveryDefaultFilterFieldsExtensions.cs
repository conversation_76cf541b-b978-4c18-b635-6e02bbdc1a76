﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.ESafe.Models;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Extensions;

public static class ESafeDeliveryDefaultFilterFieldsExtensions
{
    public static DeliveryListFilter ToApiFilter(this ESafeDeliveryDefaultFilterFields fields, string continuationToken, string customer) =>
        new()
        {
            Customer = customer,
            ContinuationToken = continuationToken,
            OrderId = fields.OrderId.ToNullableGuid(),
            CreatedToDate = fields.CreatedDateTime.GetToDate(),
            CreatedFromDate = fields.CreatedDateTime.GetFromDate(),
            Flow = fields.Flow.ToDropdownInvariantValue(),
            StateTypeName = fields.StateTypeName.ToDropdownInvariantValue(),
            ResultTypeName = fields.ResultTypeName.ToDropdownInvariantValue(),
            MaxPageSize = fields.PageSize,
            Connector = fields.Connector,
            CustomerReference = fields.CustomerReference
        };
}
