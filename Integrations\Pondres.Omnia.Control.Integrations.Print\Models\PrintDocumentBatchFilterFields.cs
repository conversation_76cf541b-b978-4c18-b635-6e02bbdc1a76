﻿using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintDocumentBatchFilterFields : FilterFieldsBase<PrintDocumentBatchFilterFields>
{
    public List<string> OrderIds { get; set; } = new List<string>();
    public List<string> References { get; set; } = new List<string>();
    public List<string> OrderReferences { get; set; } = new List<string>();
    public List<string> BarCodes { get; set; } = new List<string>();
    public List<string> GordNumbers { get; set; } = new List<string>();
    public List<int> TaskNumbers { get; set; } = new List<int>();
    public List<int> SequenceIds { get; set; } = new List<int>();
}
