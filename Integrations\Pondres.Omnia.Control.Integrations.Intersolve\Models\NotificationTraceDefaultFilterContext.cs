﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;
using Pondres.Omnia.Control.Integrations.Intersolve.Extensions;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Models;

public class IntersolveOrderDefaultFilterContext(
    NavigationManager navigationManager,
    IntersolveServiceClient client) :
        QueryStringFilterContext<IntersolveOrderListItem, IntersolveOrderDefaultFilterFields>(navigationManager),
        IIntersolveOrderFilterContext
{
    private readonly IntersolveServiceClient client = client;

    public override string FilterTypeName => FilterType.Default.ToString();

    protected override async Task<PagedResultModel<IntersolveOrderListItem>> GetNextResultSetAsync(
        string nextContinuationToken,
        string currentCustomer)
    {
        var results = await client.OrderPagedListAsync(ToApiFilter(nextContinuationToken, currentCustomer));

        return new PagedResultModel<IntersolveOrderListItem>()
        {
            Items = [.. results.Items],
            ContinuationToken = results.ContinuationToken,
        };
    }

    protected override void NavigateToFilter() =>
        navigationManager.NavigateTo($"Intersolve/Orders?{FilterHelper.ConvertToQueryParameter(Fields)}");

    public IntersolveOrderListFilter ToApiFilter(
        string nextContinuationToken, 
        string currentCustomer) =>
        new()
        {
            Customer = currentCustomer,
            ContinuationToken = nextContinuationToken,
            Status = Fields.Status.ToDropdownInvariantValue(),
            CreatedFromDate = Fields.CreatedDateTime.GetFromDate(),
            CreatedToDate = Fields.CreatedDateTime.GetToDate(),
            OrderId = Fields.OrderId,
            CustomerReference = Fields.CustomerReference,
            MaxPageSize = Fields.PageSize,
            IntersolveOrderId = Fields.IntersolveOrderId,
            ProductOwnerNr = Fields.ProductOwnerNr
        };
}
