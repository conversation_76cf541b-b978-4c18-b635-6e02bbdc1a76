﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions;

public static class PrintDocumentDefaultFilterFieldsExtensions
{
    public static PrintDocumentListFilter ToApiFilter(this PrintDocumentDefaultFilterFields fields, string continuationToken) =>
        new()
        {
            Customer = fields.Customer,
            DocumentId = fields.DocumentId,
            CustomerDocumentReference = fields.CustomerDocumentReference,
            CreatedFromDate = fields.CreatedDateTime.GetFromDate(),
            CreatedToDate = fields.CreatedDateTime.GetToDate(),
            MaxPageSize = fields.PageSize,
            OrderId = fields.OrderId.ToNullableGuid(),
            BundleId = fields.BundleId.ToNullableGuid(),
            BatchName = fields.BatchName,
            GordNumber = fields.GordNumber,
            TaskNumber = GetTaskNumberInt(fields.TaskNumber),
            SequenceId = GetSequenceIdInt(fields.SequenceId),
            ContinuationToken = continuationToken,
            BarCode = fields.BarCode
        };

    private static int GetTaskNumberInt(string taskNumberString)
    {
        if (!string.IsNullOrWhiteSpace(taskNumberString))
            return int.Parse(taskNumberString);
        return 0;
    }

    private static int GetSequenceIdInt(string sequenceIdString)
    {
        if (!string.IsNullOrWhiteSpace(sequenceIdString))
            return int.Parse(sequenceIdString);
        return 0;
    }

}
