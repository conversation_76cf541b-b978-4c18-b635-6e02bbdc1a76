﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.ViewModels.Batches
{
    public interface IBatchesOverviewViewModel
    {
        List<DGCodeBatch> DGCodeBatches { get; set; }
        Task<List<ActionListButtonResult>> ConfirmPrintAsync(IEnumerable<string> printBundleIds, string currentUser);
        Task<Stream> GetPrintInstructionsPDFAsync(IEnumerable<string> printBundleIds);
        Task<Stream> GetBatchInstructionsPDFAsync(IEnumerable<string> printBundleIds);
        Task<Stream> GetCombinedPrintInstructionsPDFAsync(IEnumerable<string> printBundleIds);
    }
}
