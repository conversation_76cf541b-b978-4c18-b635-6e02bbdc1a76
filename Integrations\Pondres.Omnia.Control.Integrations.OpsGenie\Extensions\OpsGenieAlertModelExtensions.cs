﻿using Pondres.Omnia.Control.Integrations.OpsGenie.Models;

namespace Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;

public static class OpsGenieAlertModelExtensions
{
    public static AlertsViewModel ToAlertsViewModel(this OpsGenieAlertModel model) =>
        new()
        {
            NotAcknowledged = model.Data.Count(x => !x.Acknowledged),
            P1 = model.Data.ToViewModel("P1"),
            P2 = model.Data.ToViewModel("P2"),
            P3 = model.Data.ToViewModel("P3"),
            P4 = model.Data.ToViewModel("P4"),
            P5 = model.Data.ToViewModel("P5")
        };

    public static AlertsViewModel ToAlertsViewModel(this OpsGenieAlertModel model, OpsGenieScheduleModel scheduleModel)
    {
        var viewModel = model.ToAlertsViewModel();
        viewModel.Scheduled = scheduleModel.ToScheduledOnCallName();
        return viewModel;
    }
}
