using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Services;

public class PrintService : IPrintService
{
    private readonly PrintServiceClient printServiceClient;

    public PrintService(PrintServiceClient printServiceClient)
    {
        this.printServiceClient = printServiceClient;
    }

    public async Task<PrintBundleTotalsResult> GetPrintBundleTotalsAsync(PrintDashboardFilter filter)
    {
        var apiFilter = filter.ToApiFilter();

        var totalsResult = await printServiceClient.StatsBundleTotalsForDateRangeAsync(null, null, apiFilter);

        return totalsResult;
    }

    public async Task<PrintBundleReleaseResponse> ReleasePrintBundleAsync(Guid bundleId, string username, PrintBundleOptions bundleOptions, DateTimeOffset releaseTime) =>
        await printServiceClient.BundleReleaseAsync("", "",
            new PrintBundleReleaseRequest
            {
                Username = username,
                PrintBundleId = bundleId,
                BundleOptions = bundleOptions,
                ReleaseDateTime = releaseTime
            });

    public async Task ConfirmPrintBundleAsync(string bundleId, string username)
    {
        var body = new PrintBundlePrintConfirmCommand
        {
            PrintBundleId = Guid.Parse(bundleId),
            Username = username
        };

        await printServiceClient.BundlePrintconfirmAsync(null, null, body);
    }

    public async Task CompletePrintBundleAsync(string bundleId, string username)
    {
        var body = new PrintBundleCompletedConfirmCommand
        {
            PrintBundleId = Guid.Parse(bundleId),
            Username = username
        };

        await printServiceClient.BundleCompletedconfirmAsync(null, null, body);
    }

    public async Task ContinuePrintBundleAsync(string bundleId, bool force, string username)
    {
        var body = new PrintBundleContinueCommand
        {
            Force = force,
            PrintBundleId = Guid.Parse(bundleId),
            Username = username
        };

        await printServiceClient.BundleContinueAsync(null, null, body);
    }

    public async Task CancelPrintBundleAsync(string bundleId, bool force, bool reprint, string username)
    {
        var body = new PrintBundleCancelCommand
        {
            Force = force,
            Reprint = reprint,
            PrintBundleId = Guid.Parse(bundleId),
            Username = username
        };

        await printServiceClient.BundleCancelAsync(null, null, body);
    }

    public async Task ReprintSelectedPrintDocumentsAsync(List<Guid> orderIds, string customer, string username)
    {
        List<PrintDocumentReprintRequest> commands = new();

        foreach (var orderId in orderIds)
        {
            commands.Add(new PrintDocumentReprintRequest
            {
                OrderId = orderId,
                Customer = customer,
                Username = username
            });
        }

        await printServiceClient.DocumentCancelListAsync(null, null, commands);
    }

    public async Task RemoveDocumentPrintBundleAsync(string bundleId, string documentId)
    {
        var body = new PrintBundleRemoveDocumentCommand
        {
            PrintBundleId = Guid.Parse(bundleId),
            DocumentId = documentId
        };

        await printServiceClient.BundleRemoveDocumentAsync("", "", body);
    }

    public async Task<PagedResultModel<PrintDocumentListItem>> GetAllDocumentsByDefaultFilterAsync(PrintDocumentListFilter filter)
    {
        var result = await printServiceClient.DocumentPagedListAsync(null, null, filter);

        return new PagedResultModel<PrintDocumentListItem>
        {
            Items = result.Items.ToList(),
            ContinuationToken = result.ContinuationToken
        };
    }

    public async Task<PagedResultModel<PrintDocumentListItem>> GetAllDocumentsByBatchFilterAsync(PrintDocumentBatchListFilter filter)
    {
        var result = await printServiceClient.DocumentPagedBatchListAsync(null, null, filter);

        return new PagedResultModel<PrintDocumentListItem>
        {
            Items = result.Items.ToList(),
            ContinuationToken = result.ContinuationToken
        };
    }

    public async Task<PagedResultModel<PrintDocumentListItem>> GetAllDocumentsForOrderIdAsync(Guid orderId, string continuationToken) =>
        await GetAllDocumentsByDefaultFilterAsync(new PrintDocumentListFilter
        {
            OrderId = orderId,
            ContinuationToken = continuationToken,
            MaxPageSize = ControlConstants.DefaultPageSize
        });

    public async Task<PagedResultModel<DGCodeBatch>> GetBatchesAsync(PrintBundleDefaultFilter filter)
    {
        var response = await printServiceClient.BundleBatchListAsync(null, null, filter);

        return new PagedResultModel<DGCodeBatch>
        {
            Items = response.Items.ToList(),
            ContinuationToken = response.ContinuationToken
        };
    }

    public async Task<PagedResultModel<PrintBundleListItem>> GetAllBundlesByBatchFilterAsync(PrintBundleBatchFilter filter)
    {
        var response = await printServiceClient.BundleBundleBatchListAsync(null, null, filter);

        return new PagedResultModel<PrintBundleListItem>
        {
            Items = response.Items.ToList(),
            ContinuationToken = response.ContinuationToken
        };
    }

    public async Task<PagedResultModel<PrintBundleListItem>> GetAllBundlesByDefaultFilterAsync(PrintBundleDefaultFilter filter)
    {
        var response = await printServiceClient.BundleListAsync(null, null, filter);

        return new PagedResultModel<PrintBundleListItem>
        {
            Items = response.Items.ToList(),
            ContinuationToken = response.ContinuationToken
        };
    }

    public async Task<PagedResultModel<PrintBundleListItem>> GetPrintBundlesByIdsAsync(IEnumerable<string> printBundleIds)
    {
        var response = await printServiceClient.BundleListByIdsAsync(null, null, printBundleIds);

        return new PagedResultModel<PrintBundleListItem>
        {
            Items = response.Items.ToList(),
            ContinuationToken = response.ContinuationToken
        };
    }

    public async Task<List<ReleaseSchedules>> GetReleaseSchedulesAsync()
    {
        var releaseSchedules = await printServiceClient.ReleaseSchedulesGetAllAsync(null, null);
        return releaseSchedules.ToList();
    }

    public async Task<ReleaseScheduleResult> CreateReleaseSchedulesAsync(ReleaseSchedules schedule)
    {
        var result = await printServiceClient.ReleaseSchedulesCreateAsync(null, null, schedule);
        return result;
    }

    public async Task<ReleaseScheduleResult> UpdateReleaseSchedulesAsync(ReleaseSchedules schedules)
    {
        var result = await printServiceClient.ReleaseSchedulesUpdateAsync(null, null, schedules);
        return result;
    }

    public async Task DeleteReleaseSchedulesAsync(string scheduleId)
    {
        await printServiceClient.ReleaseSchedulesDeleteAsync(null, null, scheduleId);
    }

    public async Task<PrintBundleRawDetails> GetRawBundleDetailsAsync(string id) => await printServiceClient.BundleRawAsync(Guid.Parse(id), null, null);

    public async Task ScanPrintBundleAsync(string bundleId, string userName)
    {
        var bundles = await GetPrintBundlesByIdsAsync([bundleId]);

        var theBundle = bundles.Items.SingleOrDefault();

        if (theBundle == null)
        {
            throw new BundleNotFoundException("Bundle not found");
        }

        if (theBundle.Status.Status != MapPrintBundleState.WaitingForScan)
        {
            throw new BundleInWrongStateException(theBundle.Status.Status);
        }

        await printServiceClient.BundleScannedconfirmAsync(null, null, new PrintBundleScannedConfirmCommand
        {
            PrintBundleId = Guid.Parse(bundleId),
            Username = userName
        });

    }

    public async Task<PrintBundleDetails> GetPrintBundleDetailsAsync(Guid bundleId)
    {
        return await printServiceClient.BundleDetailsAsync(bundleId, null, null);
    }
}

public class BundleInWrongStateException(MapPrintBundleState currentState) : Exception
{
    public MapPrintBundleState CurrentState { get; set; } = currentState;
}

public class BundleNotFoundException(string message) : Exception(message);