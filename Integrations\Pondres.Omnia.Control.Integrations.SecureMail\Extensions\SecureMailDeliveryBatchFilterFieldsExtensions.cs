﻿using Pondres.Omnia.Control.Integrations.SecureMail.Models;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Extensions;

public static class SecureMailDeliveryBatchFilterFieldsExtensions
{
    public static DeliveryBatchSelectionFilter ToApiFilter(this SecureMailDeliveryBatchFilterFields fields, string continuationToken, string currentCustomer) => new()
    {
        Customer = currentCustomer,
        ContinuationToken = continuationToken,
        CustomerReferences = fields.CustomerReferences,
        MaxPageSize = fields.PageSize,
        Connectors = fields.Connectors
    };
}
