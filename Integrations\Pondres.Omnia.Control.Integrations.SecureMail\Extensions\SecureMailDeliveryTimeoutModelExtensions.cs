﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Extensions;

public static class SecureMailDeliveryTimeoutModelExtensions
{
    public static PagedResultModel<SecureMailDeliveryTimeoutModel> ToPagedResultModel(this List<SecureMailDeliveryTimeoutModel> timeouts) =>
        new() { Items = timeouts.OrderBy(x => x.ExpectedOn).ToList() };
}
