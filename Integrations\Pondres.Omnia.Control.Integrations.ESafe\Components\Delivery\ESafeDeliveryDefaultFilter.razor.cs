﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.ESafe.Models;

namespace Pondres.Omnia.Control.Integrations.ESafe.Components.Delivery;

public partial class ESafeDeliveryDefaultFilter
{
    [Parameter]
    public ESafeDeliveryDefaultFilterContext Filter { get; set; }

    [Parameter]
    public EventCallback<ESafeDeliveryDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    private async Task OnFilterChangedAsync()
    {
        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }
}