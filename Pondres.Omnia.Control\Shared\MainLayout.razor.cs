using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;

namespace Pondres.Omnia.Control.Shared
{
    public partial class MainLayout : LayoutComponentBase
    {
        [Inject]
        private InfiniteScrollHelper infiniteScrollHelper { get; set; }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
                await infiniteScrollHelper.RegisterInteropAsync();
        }
    }
}