using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Response;

public partial class ResponseLogGridItem
{
    [Parameter]
    public WarehouseResponseLog Log { get; set; }

    private bool DetailsExpanded { get; set; } = false;
    private void ToggleDetailsExpanded() => DetailsExpanded = !DetailsExpanded;
    private string GetData() => Log.Data ?? "No data";
}
