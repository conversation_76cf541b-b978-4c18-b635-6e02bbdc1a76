﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Extensions;

public static class NavigationManagerExtensions
{
    public static void NavigateToWarehouseDocumentListWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"WMS/Documents?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");

    public static void NavigateToWarehouseDocumentListWithDefaultFilter(this NavigationManager navigationManager, WarehouseDocumentDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"WMS/Documents?FilterType={FilterType.Default}&{FilterHelper.ConvertToQueryParameter(filter)}");

    public static void NavigateToWarehouseResponseListWithDefaultFilter(this NavigationManager navigationManager, WarehouseResponseDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"WMS/Responses?{FilterHelper.ConvertToQueryParameter(filter)}");
}