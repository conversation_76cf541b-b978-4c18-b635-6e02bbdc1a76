﻿using Microsoft.AspNetCore.Components.Authorization;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.ViewModels;
using Pondres.Omnia.Control.Integrations.Print.Services;
using Pondres.Omnia.Control.Extensions;
using Pondres.Omnia.Control.Integrations.Print;
using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.ViewModels.Batches
{
    public class BatchesOverviewViewModel : ViewModelBase, IBatchesOverviewViewModel
    {
        private IPrintService PrintService { get; set; }
        private IScalerPrintService ScalerPrintService { get; set; }
        public AuthenticationStateProvider AuthenticationStateProvider { get; set; }
        private EnvironmentSettings EnvironmentSettings { get; set; }
        public List<DGCodeBatch> DGCodeBatches { get; set; } = new List<DGCodeBatch>();

        public BatchesOverviewViewModel(IPrintService printService, IScalerPrintService scalerPrintService, AuthenticationStateProvider authenticationStateProvider, EnvironmentSettings environmentSettings)
        {
            PrintService = printService;
            ScalerPrintService = scalerPrintService;
            AuthenticationStateProvider = authenticationStateProvider;
            EnvironmentSettings = environmentSettings;
        }

        public async Task<List<ActionListButtonResult>> ConfirmPrintAsync(IEnumerable<string> printBundleIds, string currentUser)
        {
            var result = new List<ActionListButtonResult>();

            foreach (var bundleId in printBundleIds)
            {
                try
                {
                    await PrintService.ConfirmPrintBundleAsync(bundleId, currentUser);
                    result.Add(new() { Key = bundleId, IsSuccessful = true });
                }
                catch (Exception exception)
                {
                    result.Add(new() { Key = bundleId, FailedResultMessage = exception.Message });
                }
            }

            return result;
        }

        public async Task<Stream> GetPrintInstructionsPDFAsync(IEnumerable<string> printBundleIds)
        {
            var printBundlesForInstructions = await GetPrintBundleForInstructionsAsync(printBundleIds);
            var instructionsPDF = await ScalerPrintService.GetPrintInstructionsPDFAsync(printBundlesForInstructions);
            return instructionsPDF;
        }

        public async Task<Stream> GetBatchInstructionsPDFAsync(IEnumerable<string> printBundleIds)
        {
            var printBundlesForInstructions = await GetPrintBundleForInstructionsAsync(printBundleIds);
            var instructionsPDF = await ScalerPrintService.GetBatchInstructionsPDFAsync(printBundlesForInstructions);
            return instructionsPDF;
        }

        public async Task<Stream> GetCombinedPrintInstructionsPDFAsync(IEnumerable<string> printBundleIds)
        {
            var printBundlesForInstructions = await GetPrintBundleForInstructionsAsync(printBundleIds);
            var instructionsPDF = await ScalerPrintService.GetCombinedPrintInstructionsPDFAsync(printBundlesForInstructions);
            return instructionsPDF;
        }

        private async Task<List<PrintBundleForInstructions>> GetPrintBundleForInstructionsAsync(IEnumerable<string> printBundleIds)
        {
            var printBundles = await PrintService.GetPrintBundlesByIdsAsync(printBundleIds);
            var printBundlesForInstructions = printBundles.Items.Select(x => new PrintBundleForInstructions
            {
                BatchName = x.BatchName,
                Customer = x.Customer,
                GordNumber = x.GordNumber,
                Metadata = x.Metadata,
                PrintBundleId = x.Id,
                PrintFileLocation = x.ToPrintFolderPath(EnvironmentSettings.PrintFolderPath),
                PrintFiles = x.Files.ToList(),
                TaskNumber = x.TaskNumber,
                Sheets = x.Sheets,
                Documents = x.Documents,
                TotalPageCount = x.TotalPageCount
            });

            return printBundlesForInstructions.ToList();
        }
    }
}
