﻿@if (Disabled)
{
    <button class="btn btn-@ThemeColor @StyleClass mx-1" disabled>@ButtonText</button>
}
else
{
    @if (!Executing)
    {
        <button class="btn btn-@ThemeColor @StyleClass mx-1" @onclick="OpenModalAsync" @onclick:stopPropagation="true">@ButtonText</button>
    }

    else
    {
        <button class="btn btn-@ThemeColor @StyleClass mx-1" disabled>
            <span class="spinner-border spinner-border-sm"></span><span class="pl-2">@ButtonText</span>
        </button>
    }
}

@if (ShowModal)
{
    <div class="modal modal-open cursor-auto" @onclick:stopPropagation="true">
        <div class="modal-dialog modal-lg @ModalStyleClass" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    @Title
                </div>
                <div class="modal-body">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                @if (ActionFailed)
                                {
                                    <div class="alert alert-danger" role="alert">
                                        @ActionFailedMessage
                                    </div>
                                }

                                @ChildContent
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @onclick="CloseModalAsync">Close</button>
                    @if (!ActionFailed && !IsCompleted)
                    {
                        @if (!Executing)
                        {
                            <button class="btn btn-@ThemeColor" @onclick="SubmitModalAsync">@ButtonText</button>
                        }
                        else
                        {
                            <button class="btn btn-@ThemeColor" disabled>
                                <span class="spinner-border spinner-border-sm"></span><span class="pl-2">@ButtonText</span>
                            </button>
                        }
                    }
                </div>
                @if (ActionResults.Any())
                {
                    <div class="modal-body p-0">
                        <div class="container p-0">
                            <div class="row px-3">
                                <div class="col-12 p-0 overflow-auto" style="max-height: 30vh;">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Id</th>
                                                <th>Message</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in ActionResults)
                                            {
                                                <tr class="@GetRowColor(item.IsSuccessful)">
                                                    <td>@item.Key</td>
                                                    <td>@(item.IsSuccessful ? item.ResultMessage : item.FailedResultMessage)</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}