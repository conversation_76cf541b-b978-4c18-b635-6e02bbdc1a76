﻿using Pondres.Omnia.Email.Contracts.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Extensions
{
    public static class EmailDeliveryStateTypeExtensions
    {
        public static string GetStatusString(this EmailDeliveryStateType state, EmailDeliveryResultType result, bool? trackStatus = default) =>
            $"{state} ({result}){GetTrackStatusSuffix(trackStatus)}";

        public static string GetStatusColorStyle(this EmailDeliveryStateType state) => state switch
        {
            EmailDeliveryStateType.Active => "table-info",
            EmailDeliveryStateType.Completed => "table-success-light",
            EmailDeliveryStateType.Warning => "table-warning",
            _ => "table-light"
        };

        private static string GetTrackStatusSuffix(bool? trackStatus = default) =>
            trackStatus.HasValue && !trackStatus.Value ? " - no tracking" : "";
    }
}
