﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.NotificationHub.Contracts.Api.Definition
@using Pondres.Omnia.NotificationHub.Contracts.Internal.Model
@using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration
@using Telerik.SvgIcons

@inherits SelectableGridBase<NotificationDefinitionModel>

<DefinitionEditModal @ref="DefinitionEditModal" OnSubmit="OnEditModalSubmittedAsync" />

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <h6 class="m-0 font-weight-bold text-primary pt-2">Notification definitions</h6>
            <div class="float-right">
                <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Add Definition
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.Api.ToString()))">@NotificationDefinitionType.Api</a>
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.Email.ToString()))">@NotificationDefinitionType.Email</a>
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.BlobStorage.ToString()))">@NotificationDefinitionType.BlobStorage</a>
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.AzureServiceBus.ToString()))">@NotificationDefinitionType.AzureServiceBus</a>
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.AwsSns.ToString()))">@NotificationDefinitionType.AwsSns</a>
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.AwsS3.ToString()))">@NotificationDefinitionType.AwsS3</a>
                    <a class="dropdown-item" @onclick="@(() => OpenNewDefinitionModalAsync(NotificationDefinitionType.SecureddMail.ToString()))">@NotificationDefinitionType.SecureddMail</a>
                </div>
            </div>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <TelerikGrid Data="@MappingContext.Definitions" @ref="DataGrid" Class="grid-no-scroll"
                     SelectedItemsChanged="@(async (IEnumerable<NotificationDefinitionModel> items) => await InvokeAsync(StateHasChanged))"
                     SelectionMode="GridSelectionMode.Multiple">
            <GridExport>
                <GridCsvExport FileName="NotificationDefinitions" OnBeforeExport="@OnBeforeCsvExport" />
            </GridExport>
            <GridToolBarTemplate>
                <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export to selected CSV" : "Export to CSV")</GridCommandButton>
                <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
            </GridToolBarTemplate>
            <GridColumns>
                <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" />
                <SelectableGridColumn Field="Name" Title="Name" SelectionChanged="@OnColumnSelect" />
                <SelectableGridColumn Field="Type" Title="Type" SelectionChanged="@OnColumnSelect" />
                <GridCommandColumn Width="90px" Context="definition">
                    <GridCommandButton Title="Edit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@EditDefinitionAsync">Edit</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
        </TelerikGrid>
    </CardBody>
</TelerikCard>