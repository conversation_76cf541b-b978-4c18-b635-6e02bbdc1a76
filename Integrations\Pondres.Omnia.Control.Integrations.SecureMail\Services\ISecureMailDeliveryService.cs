﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Services;

public interface ISecureMailDeliveryService
{
    Task<PagedResultModel<DeliveryListItem>> GetAllDeliveriesByFilterAsync(DeliveryListFilter filter);
    Task<PagedResultModel<DeliveryListItem>> GetAllDeliveriesByFilterAsync(DeliveryBatchSelectionFilter filter);
    Task<DeliveryDetails> GetDeliveryDetailsAsync(string customer, Guid deliveryId);
}
