﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.ESafe.Models;

namespace Pondres.Omnia.Control.Integrations.ESafe.Components.Delivery;

public partial class ESafeDeliveryGridFilter
{
    [Inject]
    private IApiFilterManager FilterManager { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    [Inject]
    private ESafeDeliveryDefaultFilterContext DefaultFilter { get; set; }

    [Inject]
    private ESafeDeliveryBatchFilterContext BatchFilter { get; set; }

    [Parameter]
    public EventCallback<IESafeDeliveryFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilt<PERSON>, BatchFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<IESafeDeliveryFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);
        await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(IESafeDeliveryFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);
        await FilterChanged.InvokeAsync(filter);
    }
}