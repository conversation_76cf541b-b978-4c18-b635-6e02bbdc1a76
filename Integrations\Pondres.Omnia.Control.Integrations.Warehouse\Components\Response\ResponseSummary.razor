﻿@using static Telerik.Blazor.ThemeConstants.Button

<ActionModal @ref="FileReprocessModal" T="string" Context="filename" OnSubmit="ReprocessFileASync" OnClose="OnReprocess"
             Title="Reprocess file?" ButtonType="@ThemeColor.Primary" ButtonText="Reprocess">
    You are about to reprocess <b>@filename</b>, Continue?
</ActionModal>

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <h6 class="m-0 font-weight-bold text-primary pt-2">File details</h6>
            <div class="float-right">
                <AuthorizeView Roles="ControlContributor, ControlOwner">
                    <button class="btn btn-primary" @onclick="() => FileReprocessModal.OpenModal(Filename)">Reprocess</button>
                </AuthorizeView>
                <button class="btn btn-primary pl-2" @onclick="OpenFileModalAsync">View</button>
            </div>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table mb-0">
            <tbody>
                <tr>
                    <th>File name</th>
                    <th>@Filename</th>
                </tr>
                <tr>
                    <th>Message type</th>
                    <th>@Response.MessageType</th>
                </tr>
                <tr>
                    <th>Response Id</th>
                    <td>@Response.ResponseId</td>
                </tr>
                <tr>
                    <th>State</th>
                    <td>@Response.StateTypeName</td>
                </tr>
                <tr>
                    <th>Result</th>
                    <td>@Response.ResultTypeName</td>
                </tr>
                <tr>
                    <th>Attempt count</th>
                    <td>@Response.AttemptCount</td>
                </tr>
                <tr>
                    <th>Created on</th>
                    <td>@Response.CreatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>Updated on</th>
                    <td>@Response.UpdatedOn.LocalDateTime</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>

<FileDisplayModal @ref="FileDetailsModal" />