using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Dashboard;

public partial class DashboardFilter
{
    [Parameter]
    public EventCallback<PrintDashboardFilter> FilterChanged { get; set; }

    [Parameter]
    public PrintDashboardFilter Filter { get; set; }

    private EditContext EditContext;

    private async Task ClearFilterAsync()
    {
        Filter = new PrintDashboardFilter();
        await SubmitFilterAsync();
    }

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
        EditContext.OnFieldChanged += OnFieldChanged;
    }

    private async Task SubmitFilterAsync()
    {
        await FilterChanged.InvokeAsync(Filter);
    }

    private async void OnFieldChanged(object sender, FieldChangedEventArgs e)
    {
        EditContext.MarkAsUnmodified();
        await SubmitFilterAsync();
    }
}
