using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Components.Bundle;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.ViewModels.Batches;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Print.Batches
{
    [Authorize(Roles = "PrintReader, PrintContributor, ControlOwner")]
    public partial class BatchesOverview : CustomerFilterGridPageBase<DGCodeBatch, IPrintBatchFilterContext>
    {
        [Inject]
        private IBatchesOverviewViewModel BatchOverviewViewModel { get; set; }

        [Inject]
        private IJSRuntime JS { get; set; }

        public bool Loading { get; set; }
        private PrintBundlePrintConfirmationModal PrintBundlePrintConfirmationDialog { get; set; }
        private PrintBundleWithPrintertypePrintConfirmationModal PrintBundleWithPrintertypePrintConfirmationDialog { get; set; }

        private void OnPrintBundleConfirmationCommand(GridCommandEventArgs e)
        {
            var batchDetails = e.Item as DGCodeBatchDetails;
            PrintBundleWithPrintertypePrintConfirmationDialog.ShowDialog(batchDetails);
        }

        private void OnPrintBatchConfirmationCommand(GridCommandEventArgs e)
        {
            var batch = e.Item as DGCodeBatch;
            PrintBundlePrintConfirmationDialog.ShowDialog(batch.PrintBundles);
        }

        private async Task PrintInstructionsCommandAsync(GridCommandEventArgs e)
        {
            var batch = e.Item as DGCodeBatch;
            var printBundleIds = batch.PrintBundles.Select(x => x.Id);
            var fileName = $"{batch.BatchName}-{batch.Customer}-Print instructions";
            var result = await BatchOverviewViewModel.GetPrintInstructionsPDFAsync(printBundleIds);
            await DownloadPdfFromStreamAsync(result, fileName);
        }

        private async Task BatchInstructionsCommandAsync(GridCommandEventArgs e)
        {
            var batch = e.Item as DGCodeBatch;
            var printBundleIds = batch.PrintBundles.Select(x => x.Id);
            var fileName = $"{batch.BatchName}-{batch.Customer}-Batch instructions";
            var result = await BatchOverviewViewModel.GetBatchInstructionsPDFAsync(printBundleIds);
            await DownloadPdfFromStreamAsync(result, fileName);
        }

        private async Task CombinedPrintInstructionsCommandAsync(GridCommandEventArgs e)
        {
            var batch = e.Item as DGCodeBatch;
            var printBundleIds = batch.PrintBundles.Select(x => x.Id);
            var fileName = $"{batch.BatchName}-{batch.Customer}-Combined print instructions";
            var result = await BatchOverviewViewModel.GetCombinedPrintInstructionsPDFAsync(printBundleIds);
            await DownloadPdfFromStreamAsync(result, fileName);
        }

        private async Task DownloadPdfFromStreamAsync(Stream stream, string fileName)
        {
            using var streamRef = new DotNetStreamReference(stream);
            await JS.InvokeVoidAsync("downloadFileFromStream", fileName + ".pdf", streamRef);
        }

        private async Task ConfirmPrintBatchAsync()
        {
            var items = PrintBundlePrintConfirmationDialog.Model;
            var result = await BatchOverviewViewModel.ConfirmPrintAsync(items.Select(bundle => bundle.Id), CurrentUser);

            await PrintBundlePrintConfirmationDialog.SetResultAsync(result);

            await LoadGridItemsAsync(refresh: true);
        }

        private async Task ConfirmPrintBundlesAsync()
        {
            var items = PrintBundleWithPrintertypePrintConfirmationDialog.Model;
            var result = await BatchOverviewViewModel.ConfirmPrintAsync(items.PrintBundleIds, CurrentUser);

            await PrintBundleWithPrintertypePrintConfirmationDialog.SetResultAsync(result);

            await LoadGridItemsAsync(refresh: true);
        }
    }
}
