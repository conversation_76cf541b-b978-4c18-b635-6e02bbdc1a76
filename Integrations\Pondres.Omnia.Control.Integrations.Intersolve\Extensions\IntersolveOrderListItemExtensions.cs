﻿using Pondres.Omnia.Control.Integrations.Intersolve.Client;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class IntersolveOrderListItemExtensions
{
    public static string GetStatusColorStyle(this IntersolveOrderListItem order) =>
        order.Status.ToStatusColorClass();

    public static string ToStatusColorClass(this string status) =>
        status switch
        {
            // We got no enum or error states for this lol
            "Created" or "Found" => "table-primary",
            "Acknowledged" => "table-success-light",
            _ => "table-light"
        };
}
