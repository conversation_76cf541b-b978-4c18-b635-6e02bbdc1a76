﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Services;

public interface IWarehouseDocumentService
{
    Task<WarehouseDocumentInformation> GetDocumentDetailsAsync(string customer, string documentId);
    Task<PagedResultModel<WarehouseDocumentListItem>> GetDocumentsByDefaultFilterAsync(WarehouseDocumentDefaultFilter filter);
    Task<PagedResultModel<WarehouseDocumentListItem>> GetDocumentsByBatchFilterAsync(WarehouseDocumentBatchFilter filter);
    Task<PagedResultModel<WarehouseDocumentListItem>> GetDocumentsForOrderIdAsync(Guid orderId, string continuationToken);
}
