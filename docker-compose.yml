version: '3.4'

services:
  pondres.omnia.control:
    image: ${DOCKER_REGISTRY-}pondresomniacontrol
    build:
      context: .
      dockerfile: Pondres.Omnia.Control/Dockerfile
    environment:
      - RedisConnectionString=redis
      - AzureAd__Instance=https://login.microsoftonline.com/
      - AzureAd__Domain=pondresnl.onmicrosoft.com
      - AzureAd__TenantId=53f82d5e-60e6-4fbc-86cc-db2834eb64a3
      - AzureAd__ClientId=65a74430-49e4-404f-a646-187a8def66f1
      - AzureAd__CallbackPath=/signin-oidc
      - AzureAd__SignedOutCallbackPath=/signout-callback-oidc
      - AzureSignalRConnectionString=${AzureSignalRConnectionString}
      - OrderServiceUri=https://api-omnia-test.pondres.nl/
      - OrderServiceAuthToken=${TestServiceAuthToken}
      - ESafeServiceUri=https://api-omnia-test.pondres.nl/
      - ESafeServiceAuthToken=${TestServiceAuthToken}
      - EmailServiceUri=https://api-omnia-test.pondres.nl/
      - EmailServiceAuthToken=${TestServiceAuthToken}
      - PrintServiceUri=https://api-omnia-test.pondres.nl/print/
      - PrintServiceAuthToken=${TestServiceAuthToken}
      - ReportingAuthToken=${TestServiceAuthToken}
      - NotificationServiceUri=https://api-omnia-test.pondres.nl/
      - NotificationServiceAuthToken=${TestServiceAuthToken}
      - WarehouseServiceUri=https://api-omnia-test.pondres.nl/
      - WarehouseServiceAuthToken=${TestServiceAuthToken}
      - SecureMailServiceUri=https://api-omnia-test.pondres.nl/
      - SecureMailServiceAuthToken=${TestServiceAuthToken}
      - DemoServiceUri=https://demo-omnia-test.pondres.nl/
      - DemoServiceAuthToken=${TestServiceAuthToken}
      - CustomerServiceUri=https://api-omnia-test.pondres.nl/
      - CustomerServiceAuthToken=${TestServiceAuthToken}
      - WebToOmniaServiceUri=https://api-omnia-test.pondres.nl/
      - WebToOmniaServiceAuthToken=${WebToOmniaServiceAuthToken}
      - IntersolveServiceUri=https://api-omnia-test.pondres.nl/
      - IntersolveServiceAuthToken=${IntersolveServiceAuthToken}
      - OpsGenieAlertsApiKeyReadOnly=${OpsGenieAlertsKey}
      - OpsGenieScheduleApiKeyReadOnly=${OpsGenieScheduleKey}
      - OpsGeniePlatformFilterId=${OpsGeniePlatformFilterId}
      - OpsGenieCustomerFilterId=${OpsGenieCustomerFilterId}
      - OpsGenieScheduleFilterId=${OpsGenieScheduleFilterId}
      - DataSeedDisabled=false
      - PONDRESNETWORK=${PONDRESNETWORK}
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}
      - ScalerPrintInstructionsUri=${ScalerPrintInstructionsUri}
      - ScalerPrintInstructionsAuthToken=${ScalerPrintInstructionsAuthToken}

  redis:
    image: redis:6-alpine
    command: ["redis-server", "--appendonly", "yes"]
    ports:
    - "6379:6379"