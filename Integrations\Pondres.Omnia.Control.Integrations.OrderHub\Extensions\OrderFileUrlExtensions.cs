﻿using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderFileUrlExtensions
{
    private static readonly string[] fileTypes = new[] { ".pdf", ".tno" };

    public static bool DownloadOnly(this OrderFileUrl orderFileUrl) =>
        fileTypes.Any(fileType => Path.GetExtension(orderFileUrl?.FullPath).ToLower() == fileType);
}
