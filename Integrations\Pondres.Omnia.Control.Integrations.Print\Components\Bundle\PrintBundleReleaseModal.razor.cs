﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Bundle
{
    public partial class PrintBundleReleaseModal : ModalBase
    {
        public IEnumerable<PrintBundleListItem> Model { get; private set; }

        public IEnumerable<ActionListButtonResult> Result { get; private set; }

        public bool HasResult => Result?.Any() ?? false;

        public bool ConfirmButtonEnabled { get; set; } = true;

        public TelerikDialog Dialog { get; set; }

        [Parameter]
        public EventCallback OnSaveAction { get; set; }

        [Parameter]
        public EventCallback OnCloseAction { get; set; }

        public List<PrintBundleMode?> PrintBundleModes { get; set; } = new List<PrintBundleMode?>();

        public PrintBundleMode? SelectedPrintBundleMode { get; set; } = PrintBundleMode.EmptyGroup;

        public static string GetRowColor(bool success) => success ? "table-success-light" : "table-danger";

        public void ShowDialog(IEnumerable<PrintBundleListItem> model)
        {
            ConfirmButtonEnabled = true;
            Model = model;

            if (model.All(x => x.AvailablePrintBundleModes.Contains(PrintBundleMode.EmptyGroup)))
                PrintBundleModes.Add(PrintBundleMode.EmptyGroup);

            if (model.All(x => x.AvailablePrintBundleModes.Contains(PrintBundleMode.MaxFullSheets)))
                PrintBundleModes.Add(PrintBundleMode.MaxFullSheets);

            IsVisible = true;
        }

        public async Task OnSubmitModalAsync()
        {
            ConfirmButtonEnabled = false;

            await InvokeAsync(StateHasChanged);
            Dialog.Refresh();

            if (OnSaveAction.HasDelegate)
                await OnSaveAction.InvokeAsync();

            if (CloseOnSubmit)
                await CloseDialogAsync();
        }

        public async Task CloseDialogAsync()
        {
            if (OnCloseAction.HasDelegate)
                await OnCloseAction.InvokeAsync();

            Model = null;
            Result = null;
            IsVisible = false;
            ConfirmButtonEnabled = true;
        }

        public async Task SetResultAsync(List<ActionListButtonResult> result)
        {
            Result = result;

            await InvokeAsync(StateHasChanged);

            Dialog.Refresh();
        }
    }
}
