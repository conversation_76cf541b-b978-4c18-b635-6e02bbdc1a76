﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;
using Pondres.Omnia.NotificationHub.Contracts.Output;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class NotificationTraceOutputExtensions
{
    public static PagedResultModel<NotificationTraceOutput> ToPagedResultModel(this List<NotificationTraceOutput> timeouts) =>
        new() { Items = timeouts.OrderBy(x => x.InitiatedOn).ToList() };

    public static bool HasActionOfType(this NotificationTraceOutput output, NotificationTraceOutputAction action) =>
        output.Actions.Any(x => x == action.ToString());

    public static string GetOutputStatusColorStyle(this NotificationTraceOutput output) =>
        (Enum.TryParse<NotificationOutputStateType>(output.StateTypeName, out var result) ? result : NotificationOutputStateType.None) switch
        {
            NotificationOutputStateType.Active => "table-primary",
            NotificationOutputStateType.Completed => "table-success-light",
            NotificationOutputStateType.Failed => "table-danger",
            _ => "table-light"
        };
}
