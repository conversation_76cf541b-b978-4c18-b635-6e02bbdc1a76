﻿@page "/WMS/Responses/{FileName}/Details"

@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Response
@using Pondres.Omnia.Warehouse.Contracts.Response

<PageHeader Title="@($"Response details")" />
<PageBody>
    <div class="container-fluid row">
        <LoadingSpinner Show="LoadingResponses"/>
        @if (InvalidResponse)
        {
            <div class="alert alert-danger" role="alert">
                Could not retrieve response details for <b>@Filename</b>
            </div>
        }
        
        @if (!LoadingResponses && !InvalidResponse)
        {
            <div class="col-xl-6 col-12">
                <ResponseSummary Response="Response" Filename="@Filename" />

                <ResponseProcessResult Results="Response.ProcessResults"/>
            </div>

            <div class="col-xl-6 col-12">
                <ResponseLogGrid Logs="Response.Logs"/>
            </div>
        }
    </div>
</PageBody>