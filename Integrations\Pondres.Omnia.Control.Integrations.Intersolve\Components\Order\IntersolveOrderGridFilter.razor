﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab


<TabPanel TDataType="IIntersolveOrderFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IIntersolveOrderFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IIntersolveOrderFilterContext">

                <TabPanelTab TDataType="IIntersolveOrderFilterContext" Name="Default" Data="defaultFilter" Selected="FilterManager.CurrentFilter == defaultFilter">
                    <Template>
                        <IntersolveOrderDefaultFilter Filter="defaultFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>

            </TabPanelTabs>
        </div>
    </div>
</TabPanel>


