using Microsoft.AspNetCore.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Filters;

public partial class ListFilterInputTemplate : IDisposable
{
    private const int mediumScreenFieldWidth = 4;
    private const int largeScreenFieldWidth = 3;

    [CascadingParameter]
    private ListFilterRow Row { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public string LabelText { get; set; }

    [Parameter]
    public bool Stretch { get; set; } = true;

    [Parameter]
    public string Class { get; set; }

    private string GetWidthDimensions() =>
        Stretch ? $"col-xxl-{GetWidthForLargeScreen()} col-lg-{GetWidthForLargeScreen()} col-md-{GetWidthForMediumScreen()}" : $"col-xxl-{largeScreenFieldWidth} col-lg-{largeScreenFieldWidth} col-md-{mediumScreenFieldWidth}";

    private int GetWidthForLargeScreen() =>
        GetWidthForScreenSize(largeScreenFieldWidth);

    private int GetWidthForMediumScreen() =>
        GetWidthForScreenSize(mediumScreenFieldWidth);

    private int GetWidthForScreenSize(int fieldWidthForScreenSize) =>
        Math.Max(12 / Row.Inputs.Count, fieldWidthForScreenSize);

    protected override void OnInitialized()
    {
        Row.Inputs.Add(this);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing && (Row.Inputs?.Any() ?? false))
            Row.Inputs.Remove(this);
    }
}