﻿using Pondres.Omnia.Control.Integrations.Email.Models;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Extensions;

public static class EmailDeliveryBatchFilterFieldsExtensions
{
    public static DeliveryBatchFilter ToApiFilter(this EmailDeliveryBatchFilterFields fields, string continuationToken, string customer) => 
        new()
        {
            Customer = customer,
            ContinuationToken = continuationToken,
            CustomerReferences = fields.CustomerReferences,
            MaxPageSize = fields.PageSize,
            DeliveryIds = fields.DeliveryIds
        };
}
