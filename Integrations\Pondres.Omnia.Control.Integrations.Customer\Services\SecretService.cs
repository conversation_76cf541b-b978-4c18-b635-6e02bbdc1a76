﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Customer.Contracts.Api;

namespace Pondres.Omnia.Control.Integrations.Customer.Services;

public class SecretService : BaseApiService, ISecretService
{
    public SecretService(HttpClient httpClient)
        : base(httpClient)
    { }

    public async Task<List<VaultSecretListItem>> GetSecretsAsync(string customerId) =>
        await GetAsync<List<VaultSecretListItem>>($"customer/vault/list?customerId={customerId}");

    public async Task CreateSecretAsync(CreateOrUpdateSecret model) =>
        await PostAsync(model, $"customer/vault/create");

    public async Task UpdateSecretAsync(CreateOrUpdateSecret model) =>
        await PostAsync(model, $"customer/vault/update");
}