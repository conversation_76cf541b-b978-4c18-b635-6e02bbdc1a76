using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Constants;
using Pondres.Omnia.Control.Integrations.Customer.Models;

namespace Pondres.Omnia.Control.Integrations.Customer.Components
{
    public partial class CustomerSelection : ComponentBase
    {
        [Inject]
        public ILocalStorageService LocalStorageService { get; private set; }

        [Parameter]
        public IEnumerable<CustomerModel> Customers { get; set; }

        [Parameter]
        public string SelectedCustomerId { get; set; }

        [Parameter]
        public EventCallback<string> CustomerChanged { get; set; }

        public bool ShowTestCustomers { get; set; } = false;

        private IEnumerable<CustomerModel> FilteredCustomers => ShowTestCustomers 
            ? Customers?.Where(x => IsTestCustomer(x.Id))
            : Customers?.Where(x => !IsTestCustomer(x.Id));

        private async Task OnValueChangedAsync(string customerId)
        {
            if (CustomerChanged.HasDelegate)
                await CustomerChanged.InvokeAsync(customerId);
        }
        
        private async Task OnShowTestCustomersChangedAsync(bool value)
        {
            ShowTestCustomers = value;
            
            if (!FilteredCustomers.Any(c => c.Id == SelectedCustomerId))
                await OnValueChangedAsync(FilteredCustomers.FirstOrDefault().Id);
            
            StateHasChanged();
        }

        private static bool IsTestCustomer(string customerId) =>
            customerId.EndsWith(CustomerConstants.TestPostFix, StringComparison.InvariantCultureIgnoreCase) || CustomerConstants.TestCustomers.Contains(customerId);

        protected override void OnParametersSet()
        {
            if (SelectedCustomerId != null)
                ShowTestCustomers = IsTestCustomer(SelectedCustomerId);
        }
    }
}
