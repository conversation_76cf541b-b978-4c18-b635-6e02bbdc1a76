using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Pages.Order;

public partial class Files : ComponentBase
{
    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    [Inject]
    private IFileService FileService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    private IOrderService OrderService { get; set; }

    [Parameter]
    public string CurrentUser { get; set; }

    [Parameter]
    public OrderFullInformation Order { get; set; }

    private OrderFilesResult FilesList { get; set; }

    private FileModel FileDetails { get; set; }

    private FileDisplayModal FileDetailsModal { get; set; }

    private bool RetrieveFilesFailed { get; set; } = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                FilesList = await OrderService.GetFilesAsync(Order.Customer, Guid.Parse(Order.OrderId));
            }
            catch (Exception ex)
            {
                RetrieveFilesFailed = true;
                ToastService.ShowError(ex.Message);
            }

            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task OpenFileModalAsync(string url)
    {
        try
        {
            await GetFileDetailsAsync(url);
            await FileDetailsModal.OpenAsync(FileDetails);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }

    private async Task DownloadFileAsync(string url, bool downloadOnly = false)
    {
        await GetFileDetailsAsync(url);

        if (!downloadOnly)
        {
            using (var fileStream = GetFileStream(FileDetails.Contents, FileDetails.FileName))
            using (var streamRef = new DotNetStreamReference(stream: fileStream))
            {
                var fileName = FileDetails.FileName;
                await JSRuntime.InvokeVoidAsync("downloadFileFromStream", fileName, streamRef);
            }
        }
        else
            await JSRuntime.InvokeAsync<object>("open", FileDetails.Contents, "_blank");
    }

    private static MemoryStream GetFileStream(string contents, string fileName)
    {
        var stream = new MemoryStream();

        if (fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
        {
            var bytes = Convert.FromBase64String(contents);
            stream.Write(bytes, 0, bytes.Length);
        }
        else
        {
            var writer = new StreamWriter(stream);
            writer.Write(contents);
            writer.Flush();
        }

        stream.Position = 0;
        return stream;
    }

    private async Task GetFileDetailsAsync(string url)
    {
        FileDetails = await FileService.GetFileAsync(Order.Customer, CurrentUser, FilesList.ContainerName, url);
    }
}