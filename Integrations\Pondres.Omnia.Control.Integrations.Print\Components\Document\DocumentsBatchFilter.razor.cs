using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Document;

public partial class DocumentsBatchFilter
{
    [Parameter]
    public EventCallback<PrintDocumentBatchFilterContext> FilterChanged { get; set; }

    [Parameter]
    public PrintDocumentBatchFilterContext Filter { get; set; }

    private EditContext EditContext;
    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
        EditContext.OnFieldChanged += FilterForm_OnFieldChanged;
    }

    private async void FilterForm_OnFieldChanged(object sender, FieldChangedEventArgs e)
    {
        EditContext.MarkAsUnmodified();
        await SubmitFilterAsync();
    }

    private async Task ClearFilterAsync()
    {
        await Filter.ResetAsync();
        await SubmitFilterAsync();
    }

    private async Task SubmitFilterAsync()
    {
        await FilterChanged.InvokeAsync(Filter);
    }
}
