﻿using Pondres.Omnia.WebToOmnia.Contracts.Api.Configuration;

namespace Pondres.Omnia.Control.Integrations.Web2Omnia.Services
{
    public interface IWeb2OmniaService
    {
        Task<List<CustomerConfigurationDto>> GetAllConfigurationsAsync();

        Task<CustomerConfigurationDto> GetConfigurationDetailsAsync(string customerId);

        Task CreateConfigurationAsync(CreateCustomerConfigurationDto model);

        Task UpdateConfigurationAsync(UpdateCustomerConfigurationDto model);
    }
}
