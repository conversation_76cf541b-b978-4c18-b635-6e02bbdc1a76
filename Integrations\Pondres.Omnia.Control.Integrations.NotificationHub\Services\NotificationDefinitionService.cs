﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Definition;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Services;

public class NotificationDefinitionService : BaseApiService, INotificationDefinitionService
{
    private static readonly string baseUrl = "notification/definition";

    public NotificationDefinitionService(HttpClient httpClient)
        : base(httpClient)
    { }

    public async Task<List<NotificationDefinitionModel>> GetDefinitionsListAsync(string customer) =>
        await GetAsync<List<NotificationDefinitionModel>>($"{baseUrl}/list?customer={customer}");

    public async Task<string> GetDefinitionAsync(string name, string type) =>
        await GetRawAsync($"{baseUrl}/editModel?definitionName={name}&definitionTypeName={type}");

    public async Task<string> GetDefinitionSchemaAsync(string type, string customer) =>
        await GetRawAsync($"{baseUrl}/schema?definitionTypeName={type}&customer={customer}");

    public async Task UpdateDefinitionAsync(UpsertNotificationDefinitionModel updateModel) =>
         await PostAsync(updateModel, $"{baseUrl}/update");

    public async Task CreateDefinitionAsync(UpsertNotificationDefinitionModel createModel) =>
         await PostAsync(createModel, $"{baseUrl}/create");
}
