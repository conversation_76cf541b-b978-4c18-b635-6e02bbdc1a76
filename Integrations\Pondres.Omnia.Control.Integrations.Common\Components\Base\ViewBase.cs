﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.ViewModels;

namespace Pondres.Omnia.Control.Integrations.Common.Components
{
    public abstract class ViewBase<TViewModel> : ComponentBase, IDisposable
           where TViewModel : ViewModelBase
    {
        [Inject]
        public TViewModel ViewModel
        {
            get; private set;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
                await ViewModel.InitializeAsync();

            await base.OnAfterRenderAsync(firstRender);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            ViewModel = null;
        }
    }
}