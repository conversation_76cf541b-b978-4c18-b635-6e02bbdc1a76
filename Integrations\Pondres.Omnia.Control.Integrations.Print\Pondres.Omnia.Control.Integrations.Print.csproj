﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<Content Remove="OpenAPIs\print_swagger.json" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="BlazorMonaco" Version="3.3.0" />
		<PackageReference Include="JsonSchema.Net" Version="7.3.1" />
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
		<PackageReference Include="Pondres.Common" Version="3.20221025.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NSwag.ApiDescription.Client" Version="14.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Telerik.UI.for.Blazor" Version="5.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<OpenApiReference Include="OpenAPIs\print_swagger.json" CodeGenerator="NSwagCSharp" Namespace="Pondres.Omnia.Control.Integrations.Print.Client" ClassName="PrintServiceClient">
			<SourceUri>https://api-omnia-test.pondres.nl/print/swagger/v1/swagger.json</SourceUri>
			<Options>/ExcludedParameterNames:x-token /GenerateExceptionClasses:false /AdditionalNamespaceUsages:Pondres.Omnia.Control.Integrations.Common.Exceptions /OperationGenerationMode:SingleClientFromPathSegments /GeneratePrepareRequestAndProcessResponseAsAsyncMethods:true</Options>
		</OpenApiReference>
	</ItemGroup>

	<ItemGroup>
		<Content Update="Components\ProductionScanning\InputMultiLineBarcodeModal.razor">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		</Content>
	</ItemGroup>

</Project>
