﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BlazorMonaco" Version="3.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pondres.Common" Version="3.20221025.2" />
    <PackageReference Include="Pondres.Omnia.Email.Contracts" Version="1.20240919.1" />
	<PackageReference Include="NSwag.ApiDescription.Client" Version="14.2.0">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	</PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
  </ItemGroup>

	<ItemGroup>
		<OpenApiReference Include="OpenAPIs\email_swagger.json" CodeGenerator="NSwagCSharp" Namespace="Pondres.Omnia.Control.Integrations.Email.Client" ClassName="EmailServiceClient">
			<SourceUri>https://api-omnia-test.pondres.nl/email/swagger/v1/swagger.json</SourceUri>
			<Options>/ExcludedParameterNames:x-token /GenerateExceptionClasses:false /AdditionalNamespaceUsages:Pondres.Omnia.Control.Integrations.Common.Exceptions /OperationGenerationMode:SingleClientFromPathSegments /GeneratePrepareRequestAndProcessResponseAsAsyncMethods:true</Options>
		</OpenApiReference>
	</ItemGroup>

</Project>
