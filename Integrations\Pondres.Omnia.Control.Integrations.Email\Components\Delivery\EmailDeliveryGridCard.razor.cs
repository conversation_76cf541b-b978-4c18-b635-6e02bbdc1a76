﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Components.Delivery
{
    public partial class EmailDeliveryGridCard
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private IEmailDeliveryService EmailService { get; set; }

        [Parameter]
        public string Customer { get; set; }

        [Parameter]
        public Guid OrderId { get; set; }

        private void NavigateToDelivery(Guid deliveryId) =>
            NavigationManager.NavigateTo($"Email/{Customer}/{deliveryId}");

        public async Task<PagedResultModel<DeliveryData>> GetNextItemsAsync(string continuationToken) =>
            await EmailService.GetDeliveriesAsync(CreateFilter(continuationToken));

        private DeliveryDefaultFilter CreateFilter(string continuationToken) =>
            new() { OrderId = OrderId, Customer = Customer, ContinuationToken = continuationToken };
    }
}
