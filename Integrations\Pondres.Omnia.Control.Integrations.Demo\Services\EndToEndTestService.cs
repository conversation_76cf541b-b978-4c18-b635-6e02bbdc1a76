﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Demo.Contracts.Api;

namespace Pondres.Omnia.Control.Integrations.Demo.Services
{
    public class EndToEndTestService : BaseApiService, IEndToEndTestService
    {
        public EndToEndTestService(HttpClient httpClient) : base(httpClient)
        {
        }

        public async Task<List<ApiTestRunResultItem>> GetTestRunsAsync(int skip, int take) =>
            await GetAsync<List<ApiTestRunResultItem>>($"api/endToEnd/runs-recent?skip={skip}&take={take}");

        public async Task<ApiTestRunResultDetails> GetTestRunDetailsAsync(Guid testRunId) =>
            await GetAsync<ApiTestRunResultDetails>($"api/endToEnd/runs-detail?testRunId={testRunId}");
    }
}
