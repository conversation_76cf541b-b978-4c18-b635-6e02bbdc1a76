using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Pages.Order;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Details : ControlPageBase
{
    [Inject]
    IOrderService OrderService { get; set; }

    [Inject] 
    IToastService ToastService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public string OrderIdName { get; set; }

    private Guid OrderId => Guid.Parse(OrderIdName);
    private OrderFullInformation Order { get; set; }

    private bool PageLoading { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await RefreshPageAsync();
        }
    }

    private async Task RefreshPageAsync()
    {
        PageLoading = true;
        try
        {
            Order = await OrderService.GetOrderByIdAsync(Customer, OrderId);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }

        PageLoading = false;
        await InvokeAsync(StateHasChanged);
    }
}
