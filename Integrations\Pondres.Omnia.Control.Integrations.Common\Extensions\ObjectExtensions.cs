﻿namespace Pondres.Omnia.Control.Integrations.Common.Extensions;

public static class ObjectExtensions
{
    public static string ToCSVString(this object dataValue, IEnumerable<string> columns, string separator = ",")
    {
        var result = new List<string>();

        foreach (var column in columns)
        {
            result.Add($"{dataValue.GetType().GetProperty(column).GetValue(dataValue)}");
        }

        return string.Join(separator, result);
    }
}
