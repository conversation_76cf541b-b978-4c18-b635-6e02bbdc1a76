using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;

public partial class DocumentGridFilter
{
    [Inject]
    IApiFilterManager FilterManager { get; set; }

    [Inject]
    WarehouseDocumentDefaultFilterContext DefaultFilter { get; set; }

    [Inject]
    WarehouseDocumentBatchFilterContext BatchFilter { get; set; }

    [Parameter]
    public EventCallback<IWarehouseDocumentFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilter, BatchFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterChanged(IWarehouseDocumentFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);
        await FilterChanged.InvokeAsync(filter);
    }

    private async void OnFilterTabChanged(TabPanelTab<IWarehouseDocumentFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);
        await FilterChanged.InvokeAsync(selectedTab.Data);
    }
}
