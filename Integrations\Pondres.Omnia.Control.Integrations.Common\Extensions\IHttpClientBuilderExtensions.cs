﻿using Microsoft.Extensions.DependencyInjection;

namespace Pondres.Omnia.Control.Integrations.Common.Extensions;

public static class IHttpClientBuilderExtensions
{
    public static IHttpClientBuilder ConfigureBaseAddress(this IHttpClientBuilder builder, string baseAddress) =>
        builder.ConfigureHttpClient(httpClientConfiguration => httpClientConfiguration.BaseAddress = new Uri(baseAddress));

    public static IHttpClientBuilder ConfigureAuthenticationToken(this IHttpClientBuilder builder, string authenticationToken) =>
        builder.ConfigureHttpClient(httpClientConfiguration => httpClientConfiguration.DefaultRequestHeaders.Add(Constants.ControlConstants.AuthenticationTokenHeaderName, authenticationToken));
}
