﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Warehouse.Contracts.Api;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Services;

public class WarehouseDocumentService : BaseApiService, IWarehouseDocumentService
{
    public WarehouseDocumentService(HttpClient httpClient)
        : base(httpClient)
    { }

    public async Task<WarehouseDocumentInformation> GetDocumentDetailsAsync(string customer, string documentId) =>
        await GetAsync<WarehouseDocumentInformation>($"wms/api/document/details?customer={customer}&documentId={documentId}");

    public async Task<PagedResultModel<WarehouseDocumentListItem>> GetDocumentsByDefaultFilterAsync(WarehouseDocumentDefaultFilter filter)
    {
        var documentListResult = await PostWithResultAsync<PagedList<WarehouseDocumentListItem>>(filter, "wms/api/document/pagedList");

        return new PagedResultModel<WarehouseDocumentListItem>
        {
            Items = documentListResult.Items,
            ContinuationToken = documentListResult.ContinuationToken
        };
    }

    public async Task<PagedResultModel<WarehouseDocumentListItem>> GetDocumentsByBatchFilterAsync(WarehouseDocumentBatchFilter filter)
    {
        var documentListResult = await PostWithResultAsync<PagedList<WarehouseDocumentListItem>>(filter, "wms/api/documentBatch/batchList");

        return new PagedResultModel<WarehouseDocumentListItem>
        {
            Items = documentListResult.Items,
            ContinuationToken = documentListResult.ContinuationToken
        };
    }

    public async Task<PagedResultModel<WarehouseDocumentListItem>> GetDocumentsForOrderIdAsync(Guid orderId, string continuationToken) =>
        await GetDocumentsByDefaultFilterAsync(new WarehouseDocumentDefaultFilter
        {
            OrderId = orderId,
            ContinuationToken = continuationToken,
            MaxPageSize = ControlConstants.DefaultPageSize
        });
}
