using Blazored.LocalStorage;
using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Customer.Models;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Services;
using PrintBundleDefaultFilter = Pondres.Omnia.Control.Integrations.Print.Client.PrintBundleDefaultFilter;

namespace Pondres.Omnia.Control.Pages.Print.ProductionScanning;

[Authorize(Roles = "ControlContributor, ControlOwner, PrintContributor")]

public partial class ProductionScanning
{
    [Inject]
    private IToastService ToastService { get; set; }
    private bool IsLoading = false;

    [Inject]
    private IPrintService PrintService { get; set; }

    [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; }

    [Inject] private ICustomerService CustomerService { get; set; }

    [Inject] private ILocalStorageService LocalStorageService { get; set; }

    private Dictionary<string, PrintBundleWithDetails> Bundles = new();


    private List<string> Batches = [];

    private ProductionScanningFilter Filter { get; set; } = new ProductionScanningFilter();

    public List<CustomerModel> Customers { get; set; } = [];

    private EditContext EditContext;

    private ProductionScanningInput ProductionScanningInput;

    private async Task OnFilterChangedAsync(ProductionScanningFilter filter)
    {
        await RefreshAsync();

    }

    private async Task RefreshAsync()
    {
        if (Filter.Customer.IsNullOrWhiteSpace())
        {
            ToastService.ShowError("Geen klant geselecteerd.");
            return;
        }

        try
        {
            IsLoading = true;
            await TryGetBundlesAsync();
            await TryGetActiveBatchesAsync();
        }
        finally
        {
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        EditContext = new EditContext(Filter);

        try
        {
            IsLoading = true;

            if (!Customers.Any())
            {
                Customers = await CustomerService.GetAllCustomersAsync();
            }

            if (Filter.Customer.IsNullOrWhiteSpace())
            {
                Filter.Customer = await LocalStorageService.GetItemAsync<string>(ControlConstants.CustomerStorageKey);

                if (Filter.Customer.IsNullOrWhiteSpace() && Customers.Any())
                {
                    Filter.Customer = Customers[0].Id;
                }
            }
        }
        finally
        {
            IsLoading = false;
        }

        await RefreshAsync();

        await base.OnInitializedAsync();
    }


    private async Task TryGetActiveBatchesAsync()
    {
        try
        {
            Batches = (await PrintService.GetBatchesAsync(new PrintBundleDefaultFilter
            {
                State = PrintBundleStateType.Active,
                IncludeEmptyBundles = false,
                TestCustomers = true,
                MaxPageSize = 10000,
                Customer = Filter.Customer,
                IsDirectlyCompleted = false
            }))
                .Items
                .Select(c => c.BatchName)
                .OrderBy(c => c)
                .ToList();
        }
        catch (Exception e)
        {
            ToastService.ShowError($"Ophalen actieve batches mislukt ({e.Message})");
            Batches = [];
        }
    }

    private async Task TryGetBundlesAsync()
    {
        try
        {

            if (Filter.IsEmpty())
            {
                Bundles = [];
                return;
            }

            Dictionary<string, PrintBundleWithDetails> newBundles = new();

            if (!string.IsNullOrEmpty(Filter.BarCode))
            {
                if (!Integrations.Print.Validation.Validation.IsValidBarcode(Filter.BarCode))
                {
                    ToastService.ShowError("Het barcode formaat is ongeldig");
                    return;
                }

                var documentsForBarcode = await PrintService.GetAllDocumentsByDefaultFilterAsync(new PrintDocumentListFilter { BarCode = Filter.BarCode, MaxPageSize = 1, Customer = Filter.Customer });

                if (documentsForBarcode.Items.Count == 0)
                {
                    ToastService.ShowError($"Barcode {Filter.BarCode} niet gevonden");
                    return;
                }


                var theDocument = documentsForBarcode.Items.Single();

                // Reset for next scan:
                Filter.BarCode = "";
                Filter.BatchName = theDocument.LastBundle.BatchName;

                await OnBundleScannedAsync(theDocument.LastBundle.BundleId.ToString());
            }

            if (string.IsNullOrEmpty(Filter.BatchName))
            {
                ToastService.ShowError($"Batch niet gevonden");
                return;
            }

            var bundles = await PrintService.GetAllBundlesByDefaultFilterAsync(new PrintBundleDefaultFilter { BatchName = Filter.BatchName, IncludeEmptyBundles = false, MaxPageSize = 10000, TestCustomers = true, Customer = Filter.Customer, IsDirectlyCompleted = false });

            foreach (var bundle in bundles.Items)
            {
                var bundleDetails = await PrintService.GetPrintBundleDetailsAsync(Guid.Parse(bundle.Id));

                newBundles.Add(bundle.Id, new PrintBundleWithDetails(bundle, bundleDetails));
            }

            Bundles = newBundles;
        }
        catch (Exception ex)
        {
            Bundles = new();
            ToastService.ShowError(ex.Message);
        }
    }

    private async Task OnBundleCompleteAsync(string bundleId)
    {
        var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();

        try
        {
            IsLoading = true;
            await PrintService.CompletePrintBundleAsync(bundleId, state.GetUsername());
            ToastService.ShowInfo($"Bundle {bundleId} is afgemeld");
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Afmelden mislukt ({ex.Message})");
        }
        finally
        {
            await RefreshAsync();
            await ProductionScanningInput.SelectScanFieldAsync();
            IsLoading = false;
        }

    }

    private async Task OnBundleScannedAsync(string bundleId)
    {
        try
        {
            IsLoading = true;
            var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();

            await PrintService.ScanPrintBundleAsync(bundleId, state.GetUsername());
        }
        catch (BundleNotFoundException)
        {
            ToastService.ShowError($"Geen bundel gevonden");
        }
        catch (BundleInWrongStateException e)
        {
            ToastService.ShowError($"Scanned mislukt, onjuiste status {e.CurrentState}. Dit moet {MapPrintBundleState.WaitingForScan} zijn.");
        }
        catch (Exception e)
        {
            ToastService.ShowError($"Scannen mislukt ({e.Message})");
        }
        finally
        {
            await RefreshAsync();
            await ProductionScanningInput.SelectScanFieldAsync();
            IsLoading = false;
        }
    }


    protected async Task OnCustomerChangedAsync(string customerId)
    {
        if (Filter.Customer != customerId)
        {
            Filter.Customer = customerId;
            Filter.BatchName = "";
            await LocalStorageService.SetItemAsync(ControlConstants.CustomerStorageKey, Filter.Customer);
            await RefreshAsync();
        }
    }

}