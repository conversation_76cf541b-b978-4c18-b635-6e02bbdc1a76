﻿@using System.Net.Http
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Pondres.Omnia.Control.Integrations.Web2Omnia;

@using Pondres.Omnia.Control.Integrations.Web2Omnia.Extensions
@using Pondres.Omnia.Control.Integrations.Web2Omnia.Services

@using Pondres.Omnia.Control.Integrations.Common.Components.Link
@using Pondres.Omnia.Control.Integrations.Common.Components.Form
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Control.Integrations.Common.Constants

@using Telerik.Blazor.Components
@using Telerik.Blazor