﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Warehouse;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddWarehouseServices(this IServiceCollection services, AppSettings appSettings)
    {
        var wmsServiceClientName = "WmsService";

        services.AddHttpClient(wmsServiceClientName)
            .ConfigureBaseAddress(appSettings.WarehouseServiceUri)
            .ConfigureAuthenticationToken(appSettings.WarehouseServiceAuthToken);

        services.AddHttpClient<IWarehouseResponseService, WarehouseResponseService>(name: wmsServiceClientName);
        services.AddHttpClient<IWarehouseDocumentService, WarehouseDocumentService>(name: wmsServiceClientName);
        services.AddHttpClient<IArticleMappingService, ArticleMappingService>(name: wmsServiceClientName);

        services.AddTransient<WarehouseDocumentBatchFilterContext>();
        services.AddTransient<WarehouseDocumentDefaultFilterContext>();
        services.AddTransient<WarehouseResponseDefaultFilterContext>();

        return services;
    }
}
