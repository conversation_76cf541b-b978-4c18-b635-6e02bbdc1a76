﻿using Microsoft.AspNetCore.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Modal;

public partial class ModalWindow
{
    [Parameter]
    public RenderFragment ModalHeader { get; set; }

    [Parameter]
    public RenderFragment ModalBody { get; set; }

    [Parameter]
    public RenderFragment ModalFooter { get; set; }

    private bool ShowModal { get; set; }

    public async Task OpenAsync()
    {
        ShowModal = true;

        await InvokeAsync(StateHasChanged);
    }

    public async Task CloseAsync()
    {
        ShowModal = false;

        await InvokeAsync(StateHasChanged);
    }
}
