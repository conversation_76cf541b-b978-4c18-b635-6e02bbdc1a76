replicaCount: 1

namespace: omnia

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-control"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets: []
nameOverride: "control-service" # name of application
fullnameOverride: "control-service" # name of application

config:
  AzureAd__Instance: "https://login.microsoftonline.com/"
  AzureAd__Domain: "pondresnl.onmicrosoft.com"
  AzureAd__TenantId: "53f82d5e-60e6-4fbc-86cc-db2834eb64a3"
  AzureAd__ClientId: "65a74430-49e4-404f-a646-187a8def66f1"
  AzureAd__CallbackPath: "/signin-oidc"
  AzureAd__SignedOutCallbackPath: "/signout-callback-oidc"
  SessionStorageAccountName: "#|ServiceStorageAccountName|#"
  OmniaEnvironment: "#|OmniaEnvironment|#"
  PrintFolderPath: "#|PrintFolderPath|#"
  WebBasePath: "/"
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  VaultName: "#|AZURE_KEYVAULT_NAME|#"
  ReportingServiceUri: "#|ReportingServiceUri|#"
  OrderServiceUri: "#|OrderServiceUri|#"
  NotificationServiceUri: "#|NotificationServiceUri|#"
  PrintServiceUri: "#|PrintServiceUri|#"
  WarehouseServiceUri: "#|WarehouseServiceUri|#"
  CustomerServiceUri: "#|CustomerServiceUri|#"
  SecureMailServiceUri: "#|SecureMailServiceUri|#"
  ESafeServiceUri: "#|ESafeServiceUri|#"
  DemoServiceUri: "#|DemoServiceUri|#"
  EmailServiceUri: "#|EmailServiceUri|#"
  WebToOmniaServiceUri: "#|WebToOmniaServiceUri|#"
  IntersolveServiceUri: "#|IntersolveServiceUri|#"
  ScalerPrintInstructionsUri: "#|ScalerPrintInstructionsUri|#"

secrets:
  AppInsightsConnectionString: "#|AppInsightsConnectionString|#"
  ScalerPrintInstructionsAuthToken: "#|ScalerPrintInstructionsAuthToken|#"

podAnnotations: {}
podLabels:
  aadpodidbinding: "#|AKS_IDENTITY_NAME|#"

service:
  type: ClusterIP
  port: 80
  containerPort: 8080

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases: []
#  - ip: IP_ADDRESS_1
#    hostnames:
#      - HOST_NAME_1

ingress:
  enabled: true # enable if API needs to be accesible from outside the cluster
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: control-#|Omnia.BaseDomain|#
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: ingress-tls-csi
      hosts:
        - control-#|Omnia.BaseDomain|#

resources:
  limits:
    cpu: 500m
    memory: 768Mi
  requests:
    cpu: 200m
    memory: 256Mi

volumes:
  - name: secrets-store-inline
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "azure-tls"
  - name: tmp
    emptyDir: {}

volumeMounts:
  - name: secrets-store-inline
    mountPath: "/mnt/secrets-store"
    readOnly: true 
  - name: tmp
    mountPath: /tmp
    

nodeSelector: {}

tolerations: []

affinity: {}
