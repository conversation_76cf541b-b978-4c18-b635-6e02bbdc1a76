html, body {
    background: #f8f9fc;
}

/* Table overrides */
.table-success-light, .table-success-light > td, .table-success-light > th {
    background-color: #daffe2;
    border-top: 1px solid #b0d8b9;
}

.table-greyed-out, .table-greyed-out > td, .table-greyed-out > th {
    background-color: lightgrey
}

.table-hover .table-success-light:hover > td {
    background-color: #beffcc;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-auto {
    cursor: auto;
}

.table-row-details {
    background-color: rgba(0,0,0,.075);
}

/* Table refresh countdown */
.countdown-progress {
    height: 5px;
    display: none;
}

.countdown-progress-bar {
    transition: width linear;
    width: 100%;
}

/* Code block */
pre {
    color: #e83e8c;
    background-color: #EAEAEA;
    padding: 10px;
    white-space: break-spaces;
}

    pre code {
        word-break: break-word;
    }

/* Other */
.bundle-print-files-list {
    max-height: 200px;
    background: rgba(0,0,0,.075)
}

.bundle-print-files-result {
    max-height: 700px;
}

.bundle-print-modal-width {
    max-width: 1000px;
}

.modal-open {
    display: block;
    overflow-x: hidden;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.3);
}

.print-file-link:visited {
    color: green;
}

.full-width-modal-body {
    margin: 0 -16px;
}

.display-none {
    display: none;
}

.batchfilterinput {
    height: 150px;
}

.TButton-50 {
    margin: 2px;
    width: 45%;
    min-width: 125px;
}

.TButton-100 {
    margin: 2px;
    width: 93%;
    min-width: 125px;
}
