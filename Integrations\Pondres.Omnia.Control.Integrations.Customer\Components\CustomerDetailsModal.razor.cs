﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Customer.Models;

namespace Pondres.Omnia.Control.Integrations.Customer.Components;

public partial class CustomerDetailsModal : ModalBase
{
    public CustomerDetailsModel Customer { get; private set; }

    public bool IsNew { get; private set; }

    [Parameter]
    public EventCallback OnSaveAction { get; set; }

    [Parameter]
    public EventCallback OnCloseAction { get; set; }

    public void ShowDialog(CustomerDetailsModel customer, bool isNew)
    {
        Customer = customer;
        IsNew = isNew;

        IsVisible = true;
    }

    public async Task OnSubmitModalAsync()
    {
        if (OnSaveAction.HasDelegate)
            await OnSaveAction.InvokeAsync();

        await CloseDialogAsync();
    }

    public async Task CloseDialogAsync()
    {
        if (OnCloseAction.HasDelegate)
            await OnCloseAction.InvokeAsync();

        Customer = null;

        IsVisible = false;
    }
}