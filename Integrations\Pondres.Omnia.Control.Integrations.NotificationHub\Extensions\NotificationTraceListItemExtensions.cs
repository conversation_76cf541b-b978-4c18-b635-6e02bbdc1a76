﻿using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;
using Pondres.Omnia.NotificationHub.Contracts.Model;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class NotificationTraceListItemExtensions
{
    public static string GetStatusColorStyle(this NotificationTraceListItem trace) =>
        trace.Status.ToStatusColorClass();

    public static string GetStatusColorStyle(this NotificationTraceDetails trace) =>
        trace.StateTypeName.ToStatusColorClass();

    private static string ToStatusColorClass(this string traceStatus) =>
        (Enum.TryParse<NotificationStateType>(traceStatus, out var result) ? result : NotificationStateType.None) switch
        {
            NotificationStateType.Active => "table-primary",
            NotificationStateType.Completed => "table-success-light",
            NotificationStateType.Failed => "table-danger",
            NotificationStateType.Ignored => "table-warning",
            _ => "table-light"
        };
}
