﻿using System.ComponentModel.DataAnnotations;

namespace Pondres.Omnia.Control.Integrations.Customer.Models;

public class SecretModel
{
    [Required(ErrorMessage = "Customer id is required")]
    public string CustomerId { get; set; }

    [Required(ErrorMessage = "Secret name is required")]
    public string SecretName { get; set; }

    [Required(ErrorMessage = "Secret value is required")]
    public string SecretValue { get; set; }
}
