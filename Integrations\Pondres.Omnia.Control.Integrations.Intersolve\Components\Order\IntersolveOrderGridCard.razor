﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Extensions
@using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions

<GridCard Title="Intersolve orders"
          GetRawColorFunc="(order) => order.GetStatusColorStyle()"
          GetItemsAsync="GetNextItemsAsync">
    <TableHeaderContent>
        <th>Id</th>
        <th>Status</th>
        <th>Updated On</th>
    </TableHeaderContent>
    <TableRowContent>
        <td><a class="link" @onclick="() => NavigateToOrder(context)" @onclick:stopPropagation="true">@context.IntersolveOrderId</a></td>
        <td>@context.Status</td>
        <td>@context.LastUpdatedOn.LocalDateTime</td>
    </TableRowContent>
</GridCard>

