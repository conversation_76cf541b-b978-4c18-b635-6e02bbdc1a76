﻿<div class="top-row pl-4 navbar navbar-dark">
    <a class="navbar-brand" href="">Omnia Control</a>
    <button class="navbar-toggler" @onclick="() => CollapseNavMenu = !CollapseNavMenu">
        <span class="navbar-toggler-icon" />
    </button>
</div>

<div class="@NavMenuCssClass" @onclick="() => CollapseNavMenu = !CollapseNavMenu">
    <ul class="nav flex-column">
        <li class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="oi oi-dashboard" aria-hidden="true" /><span> Dashboard</span>
            </NavLink>
        </li>

        <AuthorizeView Roles="ControlReader, ControlContributor, ControlOwner, ControlDataReader">
            <li class="nav-item px-3">
                <NavLink class="nav-link" @onclick="() => ExpandCustomer = !ExpandCustomer">
                    <span class="oi oi-person" aria-hidden="true" /><span> Customer</span>
                </NavLink>
                @if (ExpandCustomer)
                {
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="Orders">
                            <span class="oi oi-document" aria-hidden="true" /> Orders
                        </NavLink>
                        <NavLink class="nav-link" href="Notifications/Traces">
                            <span class="oi oi-bell" aria-hidden="true" /> Notifications
                        </NavLink>
                        <NavLink class="nav-link" href="SecureMail/Deliveries">
                            <span class="oi oi-envelope-closed" aria-hidden="true" /> Secure Mail
                        </NavLink>
                        <NavLink class="nav-link" href="ESafe/Deliveries">
                            <span class="oi oi-envelope-closed" aria-hidden="true" /> E-Safe
                        </NavLink>
                        <NavLink class="nav-link" href="Email/Deliveries">
                            <span class="oi oi-envelope-closed" aria-hidden="true" /> E-Mail
                        </NavLink>
                        <NavLink class="nav-link" href="Intersolve/Orders">
                            <span class="oi oi-envelope-closed" aria-hidden="true" /> Intersolve Orders
                        </NavLink>
                    </div>
                }
            </li>
            <li class="nav-item px-3">
                <NavLink class="nav-link" @onclick="() => ExpandWMS = !ExpandWMS">
                    <span class="oi oi-home" aria-hidden="true" /><span> WMS</span>
                </NavLink>
                @if (ExpandWMS)
                {
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="WMS/Responses">
                            <span class="oi oi-document" aria-hidden="true" /> Responses
                        </NavLink>
                    </div>
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="WMS/Documents">
                            <span class="oi oi-document" aria-hidden="true" /> Documents
                        </NavLink>
                    </div>
                }
            </li>
            <li class="nav-item px-3">
                <NavLink class="nav-link" @onclick="() => ExpandBilling = !ExpandBilling">
                    <span class="oi oi-euro" aria-hidden="true" /><span> Billing</span>
                </NavLink>
                @if (ExpandBilling)
                {
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="Billing/Orders">
                            <span class="oi oi-document" aria-hidden="true" /> Orders
                        </NavLink>
                    </div>
                }
            </li>
        </AuthorizeView>

        <AuthorizeView Roles="PrintReader, PrintContributor, ControlOwner">
            <li class="nav-item px-3">
                <NavLink class="nav-link" @onclick="() => ExpandPrint = !ExpandPrint">
                    <span class="oi oi-print" aria-hidden="true" /><span> PrintMail</span>
                </NavLink>
                @if (ExpandPrint)
                {
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="Print/Dashboard">
                            <span class="oi oi-dashboard" aria-hidden="true" /> Dashboard
                        </NavLink>
                        <NavLink class="nav-link" href="Print/Bundles">
                            <span class="oi oi-list-rich" aria-hidden="true" /> Bundles
                        </NavLink>
                        <NavLink class="nav-link" href="Print/Documents">
                            <span class="oi oi-list-rich" aria-hidden="true" /> Documents
                        </NavLink>
                        <NavLink class="nav-link" href="Print/Batches">
                            <span class="oi oi-list-rich" aria-hidden="true" /> Batches
                        </NavLink>
                        <NavLink class="nav-link" href="Print/ProductionScanning">
                            <span class="oi oi-list-rich" aria-hidden="true" /> Production Scanning
                        </NavLink>
                        <NavLink class="nav-link" href="Print/Configuration/Schedules">
                            <span class="oi oi-document" aria-hidden="true" /> Schedules
                        </NavLink>
                    </div>
                }
            </li>
        </AuthorizeView>

        <AuthorizeView Roles="ControlContributor, ControlOwner">
            <li class="nav-item px-3">
                <NavLink class="nav-link" @onclick="() => ExpandTechnical = !ExpandTechnical">
                    <span class="oi oi-wrench" aria-hidden="true" /><span> Technical</span>
                </NavLink>
                @if (ExpandTechnical)
                {
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="E2E/Results">
                            <span class="oi oi-ellipses" aria-hidden="true" /> E2E Tests
                        </NavLink>
                    </div>
                }
            </li>
        </AuthorizeView>

        <AuthorizeView Roles="ControlOwner">
            <li class="nav-item px-3">
                <NavLink class="nav-link" @onclick="() => ExpandConfiguration = !ExpandConfiguration">
                    <span class="oi oi-list-rich" aria-hidden="true" /><span> Configuration</span>
                </NavLink>
                @if (ExpandConfiguration)
                {
                    <div class="sub-menu rounded ml-2">
                        <NavLink class="nav-link" href="Configuration/OrderFlow">
                            <span class="oi oi-document" aria-hidden="true" /> Flow configuration
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/Notification">
                            <span class="oi oi-document" aria-hidden="true" /> Notification
                        </NavLink>
                        <NavLink class="nav-link" href="Secrets">
                            <span class="oi oi-lock-locked" aria-hidden="true" /> Secrets
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/Customer">
                            <span class="oi oi-document" aria-hidden="true" /> Customer
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/Warehouse">
                            <span class="oi oi-list-rich" aria-hidden="true" /> Warehouse
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/ArticleMappings">
                            <span class="oi oi-document" aria-hidden="true" /> Article mapping
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/Web2Omnia">
                            <span class="oi oi-document" aria-hidden="true" /> Web2Omnia
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/Intersolve">
                            <span class="oi oi-document" aria-hidden="true" /> Intersolve
                        </NavLink>
                        <NavLink class="nav-link" href="Configuration/Email">
                            <span class="oi oi-envelope-closed" aria-hidden="true" /> Email
                        </NavLink>
                    </div>
                }
            </li>
        </AuthorizeView>
    </ul>
</div>


