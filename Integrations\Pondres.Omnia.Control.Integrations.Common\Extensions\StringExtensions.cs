﻿using Pondres.Omnia.Control.Integrations.Common.Constants;

namespace Pondres.Omnia.Control.Integrations.Common.Extensions;

public static class StringExtensions
{
    public static TEnum? ToNullableEnum<TEnum>(this string instance) where TEnum : struct =>
        string.IsNullOrEmpty(instance) ? null : Enum.Parse<TEnum>(instance);

    public static Guid? ToNullableGuid(this string instance) =>
        string.IsNullOrEmpty(instance) ? null : Guid.Parse(instance);

    public static int? ToNullableInt(this string instance) =>
        string.IsNullOrWhiteSpace(instance) ? null : int.Parse(instance);

    public static string Shorten(this string instance, int length = 5) =>
        instance[..length];

    public static string ToDropdownInvariantValue(this string instance) =>
        instance == ControlConstants.DefaultDropdownValue ? null : instance;
}
