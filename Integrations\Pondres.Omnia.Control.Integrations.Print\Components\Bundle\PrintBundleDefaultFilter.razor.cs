using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Bundle;

public partial class PrintBundleDefaultFilter
{
    [Parameter]
    public PrintBundleDefaultFilterContext Filter { get; set; }

    [Parameter]
    public EventCallback<PrintBundleDefaultFilterContext> FilterChanged { get; set; }

    private async Task OnFilterChangedAsync()
    {
        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }
}