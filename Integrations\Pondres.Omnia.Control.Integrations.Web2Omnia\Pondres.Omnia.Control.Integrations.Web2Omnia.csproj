﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BlazorMonaco" Version="3.3.0" />
    <PackageReference Include="JsonSchema.Net" Version="7.3.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pondres.Omnia.WebToOmnia.Contracts" Version="1.20240919.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
  </ItemGroup>

</Project>
