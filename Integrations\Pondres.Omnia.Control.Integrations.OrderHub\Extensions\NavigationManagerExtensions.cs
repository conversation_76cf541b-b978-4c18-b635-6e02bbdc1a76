﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
public static class NavigationManagerExtensions
{
    public static void NavigateToOrderDetails(this NavigationManager navigationManager, string orderId, string customer) =>
        navigationManager.NavigateTo($"Orders/{customer}/{orderId}");

    public static void NavigateToOrderListWithFilter(this NavigationManager navigationManager, FilterType filterType, string filterQueryParameters) =>
        navigationManager.NavigateTo($"Orders?FilterType={filterType}&{filterQueryParameters}");

    public static void NavigateToOrderListWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"Orders?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");
}