﻿using Microsoft.Extensions.Caching.Memory;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;
using Pondres.Omnia.NotificationHub.Contracts.Model;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Services;

public class NotificationTraceService : BaseApiService, INotificationTraceService
{
    private readonly IMemoryCache memoryCache;

    public NotificationTraceService(
        HttpClient httpClient,
        IMemoryCache memoryCache)
        : base(httpClient)
    {
        this.memoryCache = memoryCache;
    }

    public async Task<PagedResultModel<NotificationTraceListItem>> GetAllTracesForFilterAsync(NotificationTraceListFilter filter)
    {
        var tracesResponse = await PostWithResultAsync<PagedList<NotificationTraceListItem>>(filter, "notification/trace/pagedList");

        return new PagedResultModel<NotificationTraceListItem>
        {
            Items = tracesResponse.Items.ToList(),
            ContinuationToken = tracesResponse.ContinuationToken
        };
    }

    public async Task<NotificationTraceDetails> GetTraceDetailsAsync(string traceId, string customer) =>
        await GetAsync<NotificationTraceDetails>($"notification/trace/details?traceId={traceId}&customer={customer}");

    public async Task<NotificationTraceRetryResponse> RetryTraceFailedOutputsForCustomerAsync(string traceId, string customer) =>
        await PostWithResultAsync<NotificationTraceRetryResponse>(
            new NotificationTraceFailedOutputsRetryRequest { TraceId = Guid.Parse(traceId), Customer = customer },
            "notification/trace/retryFailedOutputs");

    public async Task<NotificationTraceRetryResponse> RetryOutputAsync(string outputId, string traceId, string customer) =>
        await PostWithResultAsync<NotificationTraceRetryResponse>(
            new NotificationTraceSingleOutputRetryRequest { TraceId = Guid.Parse(traceId), Customer = customer, OutputId = outputId },
            "notification/trace/retrySingleOutput");

    public async Task CancelOutputAsync(Guid outputId) =>
        await PostAsync(
            new NotificationTraceSingleOutputCancellationRequest { OutputId = outputId },
            "notification/trace/cancelOutput");

    public async Task<List<string>> GetEventTypesAsync()
    {
        if (!memoryCache.TryGetValue("_TracesEventTypes", out List<string> eventTypes))
        {
            eventTypes = await GetAsync<List<string>>("notification/trace/eventTypes");
            memoryCache.Set("_TracesEventTypes", eventTypes, new MemoryCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60) });
        }

        return eventTypes;
    }

    public List<string> GetStatusTypes() => Enum.GetNames(typeof(NotificationStateType)).ToList();
}
