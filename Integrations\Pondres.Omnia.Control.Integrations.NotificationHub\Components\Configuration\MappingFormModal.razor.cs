﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Configuration;
using Pondres.Omnia.NotificationHub.Contracts.Internal.Model;
using Pondres.Omnia.NotificationHub.Contracts.Model;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration;

public partial class MappingFormModal
{
    [Inject]
    private INotificationConfigurationService NotificationService { get; set; }

    [Inject]
    private INotificationDefinitionService DefinitionService { get; set; }

    [Inject]
    private IOrderService OrderService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [CascadingParameter]
    private NotificationMappingGridContext MappingContext { get; set; }

    [Parameter]
    public EventCallback OnSubmit { get; set; }

    private EditContext editContext;

    private MappingFormModel mappingModel;

    private NotificationConfigurationMappingModel mapping;

    private bool showModal;

    private string definitionJson;

    private bool addOnSubmit;

    private List<string> flows = new();

    private readonly JsonSerializerOptions serializerOptions = new JsonSerializerOptions { WriteIndented = true };

public async Task OpenNewModalAsync()
    {
        addOnSubmit = true;
        await OpenModalAsync(new());
    }

    public async Task OpenEditModalAsync(NotificationConfigurationMappingModel model)
    {
        addOnSubmit = false;
        await OpenModalAsync(model);
    }

    private async Task OpenModalAsync(NotificationConfigurationMappingModel model)
    {
        flows = await OrderService.GetFlowsAsync(MappingContext.Config.Customer);
        definitionJson = "";
        mapping = model;

        mappingModel = mapping.ToFormModel();

        if (!mappingModel.DefinitionId.IsNullOrWhiteSpace())
            definitionJson = await LoadDefinitionAsync();

        editContext = new(mappingModel);
        showModal = true;

        await InvokeAsync(StateHasChanged);
    }

    private void CloseModal()
    {
        showModal = false;
    }

    private async Task OnSubmitModalAsync()
    {
        if (editContext.Validate())
        {
            if (addOnSubmit)
                await AddMappingAsync();
            else
                await UpdateMappingAsync();

            CloseModal();
        }
    }

    private async Task AddMappingAsync()
    {
        MappingContext.Config.Mappings.Add(mappingModel.ToDto());

        await UpdateConfigurationAsync();
    }

    private async Task UpdateMappingAsync()
    {
        mapping.UpdateFromModel(mappingModel);

        await UpdateConfigurationAsync();
    }

    private async Task UpdateConfigurationAsync()
    {
        try
        {
            await NotificationService.UpdateConfigurationAsync(new UpdateNotificationConfigurationModel
            {
                Customer = MappingContext.Config.Customer,
                Mappings = MappingContext.Config.Mappings,
                ETag = MappingContext.Config.ETag
            });

            await OnSubmit.InvokeAsync();
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }

    private async Task TypeChangedAsync(NotificationDefinitionType? type)
    {
        mappingModel.DefinitionType = type;
        mappingModel.EventType = null;
        mappingModel.DefinitionId = null;

        definitionJson = string.Empty;
        await InvokeAsync(StateHasChanged);
    }

    private async Task EventTypeChangedAsync(NotificationEventType? type)
    {
        mappingModel.EventType = type;
        mappingModel.DefinitionId = null;

        definitionJson = string.Empty;
        await InvokeAsync(StateHasChanged);
    }

    private async Task DefinitionIdChangedAsync(string definitionId)
    {
        mappingModel.DefinitionId = definitionId;
        definitionJson = await LoadDefinitionAsync();

        await InvokeAsync(StateHasChanged);
    }

    private async Task FlowChangedAsync(string flow)
    {
        mappingModel.Flow = flow;

        await InvokeAsync(StateHasChanged);
    }

    private async Task<string> LoadDefinitionAsync()
    {
        try
        {
            var definitionRaw = await DefinitionService.GetDefinitionAsync(mappingModel.DefinitionId, mappingModel.DefinitionType.ToString());

            var definition = JsonSerializer.Deserialize<object>(definitionRaw);

            return JsonSerializer.Serialize(definition, serializerOptions);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
            return "";
        }
    }
}