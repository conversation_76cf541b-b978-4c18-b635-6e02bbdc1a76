using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Link;

public partial class OrderDetailsLink
{
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public string OrderId { get; set; }

    [Parameter]
    public string Customer { get; set; }

    private void NavigateToOrder() => NavigationManager.NavigateToOrderDetails(orderId: OrderId, customer: Customer);
}
