using Blazored.Toast.Configuration;
using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Billing;

public partial class BillingOrderFilter
{
    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public OrderBillingFilter Filter { get; set; }

    [Parameter]
    public string CustomerId { get; set; }

    [Parameter]
    public EventCallback<OrderBillingFilter> FilterSubmitted { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    private EditContext EditContext;

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
        EditContext.OnFieldChanged += FilterForm_OnFieldChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await FilterSubmitted.InvokeAsync(Filter);
    }

    private async void FilterForm_OnFieldChanged(object sender, FieldChangedEventArgs e)
    {
        EditContext.MarkAsUnmodified();
        RestrictFromToDates();
        await SubmitFilterAsync();
    }

    private void RestrictFromToDates()
    {
        var maxDays = TimeSpan.FromDays(31);

        var restricted = false;

        if (Filter.CreatedOnFilter.HasValues() && Filter.CreatedOnFilter.ToDate - Filter.CreatedOnFilter.FromDate > maxDays)
        {
            Filter.CreatedOnFilter.ToDate = Filter.CreatedOnFilter.FromDate.Value.Add(maxDays);
            restricted = true;
        }

        if (Filter.CompletedOnFilter.HasValues() && Filter.CompletedOnFilter.ToDate - Filter.CompletedOnFilter.FromDate > maxDays)
        {
            Filter.CompletedOnFilter.ToDate = Filter.CompletedOnFilter.FromDate.Value.Add(maxDays);
            restricted = true;

        }

        if (restricted)
        {
            ToastService.ShowInfo($"Restricted filter to {maxDays.Days} days");
        }
    }

    private async Task SubmitFilterAsync()
    {
        await FilterSubmitted.InvokeAsync(Filter);
    }

    private async Task SetFilterCreatedTodayAsync()
    {
        Filter.Customer = CustomerId;
        Filter.CompletedOnFilter = new();
        Filter.CreatedOnFilter = DateTimePickerFilterFields.Today();

        await SubmitFilterAsync();
        await InvokeAsync(StateHasChanged);
    }

    private async Task SetFilterCompletedTodayAsync()
    {
        Filter.Customer = CustomerId;
        Filter.CompletedOnFilter = DateTimePickerFilterFields.Today();
        Filter.CreatedOnFilter = new();

        await SubmitFilterAsync();
        await InvokeAsync(StateHasChanged);
    }
}