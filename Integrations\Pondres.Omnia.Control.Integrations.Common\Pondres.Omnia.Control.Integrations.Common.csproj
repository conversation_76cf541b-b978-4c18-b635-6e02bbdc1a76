﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>annotations</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="JsonSchema.Net" Version="7.3.1" />
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.1" />
		<PackageReference Include="Blazored.Toast" Version="4.2.1" />
		<PackageReference Include="Pondres.Common" Version="3.20221025.2" />
		<PackageReference Include="Telerik.UI.for.Blazor" Version="5.0.1" />
		<PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\Pondres.Omnia.Control.Common\Pondres.Omnia.Control.Common.csproj" />
	</ItemGroup>

</Project>
