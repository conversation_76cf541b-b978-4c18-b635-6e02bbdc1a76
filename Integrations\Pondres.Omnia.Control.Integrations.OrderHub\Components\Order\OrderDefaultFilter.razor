﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters
@using Pondres.Omnia.OrderHub.Contracts.Order

<ListFilter Filter="Filter" FilterChanged="OnFilterChangedAsync">
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="OrderId" />
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.Reference" LabelText="Reference" />
        <ListFilterInputText @bind-Value="Filter.Fields.Category1" LabelText="Category one" />
        <ListFilterInputText @bind-Value="Filter.Fields.Category2" LabelText="Category two" />
        <ListFilterInputText @bind-Value="Filter.Fields.Category3" LabelText="Category three" />
        <ListFilterInputTemplate LabelText="State">
            <TelerikDropDownList Data="@Enum.GetNames(typeof(OrderStateType)).ToList()" @bind-Value="Filter.Fields.StateTypeName" />
        </ListFilterInputTemplate>
        <ListFilterInputTemplate LabelText="Result">
            <TelerikDropDownList Data="@Enum.GetNames(typeof(OrderResultType)).ToList()" @bind-Value="Filter.Fields.ResultTypeName" />
        </ListFilterInputTemplate>
        <ListFilterInputTemplate LabelText="Flow">
            <TelerikDropDownList Data="@Flows" @bind-Value="Filter.Fields.Flow" />
        </ListFilterInputTemplate>
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="Created on" />
    </ListFilterRow>
    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />
</ListFilter>