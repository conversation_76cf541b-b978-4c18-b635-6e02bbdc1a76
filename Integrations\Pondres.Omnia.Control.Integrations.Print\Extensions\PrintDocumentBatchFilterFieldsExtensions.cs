﻿using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions;

public static class PrintDocumentBatchFilterFieldsExtensions
{
    public static PrintDocumentBatchListFilter ToApiFilter(this PrintDocumentBatchFilterFields fields, string continuationToken) =>
        new()
        {
            MaxPageSize = fields.PageSize,
            ContinuationToken = continuationToken,
            OrderIds = fields.OrderIds.Select(x => Guid.Parse(x)).ToList(),
            OrderReferences = fields.OrderReferences,
            References = fields.References,
            Barcodes = fields.BarCodes,
            SequenceIds = fields.SequenceIds,
            GordNumbers = fields.GordNumbers,
            TaskNumbers = fields.TaskNumbers
        };
}
