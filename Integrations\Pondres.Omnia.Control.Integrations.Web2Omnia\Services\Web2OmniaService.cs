﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.WebToOmnia.Contracts.Api.Configuration;

namespace Pondres.Omnia.Control.Integrations.Web2Omnia.Services
{
    public class Web2OmniaService : BaseApiService, IWeb2OmniaService
    {
        public Web2OmniaService(HttpClient httpClient) : base(httpClient)
        { }

        public async Task<List<CustomerConfigurationDto>> GetAllConfigurationsAsync() => 
            await GetAsync<List<CustomerConfigurationDto>>($"webtoomnia/configuration/list");

        public async Task<CustomerConfigurationDto> GetConfigurationDetailsAsync(string customerId) =>
            await GetAsync<CustomerConfigurationDto>($"webtoomnia/configuration/get?customer={customerId}");

        public async Task CreateConfigurationAsync(CreateCustomerConfigurationDto model) =>
            await PostAsync(model, "webtoomnia/configuration/create");

        public async Task UpdateConfigurationAsync(UpdateCustomerConfigurationDto model) =>
            await <PERSON>Async(model, "webtoomnia/configuration/update");
    }
}
