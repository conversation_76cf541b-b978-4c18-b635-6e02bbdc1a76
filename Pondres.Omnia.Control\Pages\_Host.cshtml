﻿@page "/"
@namespace Pondres.Omnia.Control.Pages
@using Pondres.Common.Extensions
@using Pondres.Omnia.Control.Integrations.Common.Models.Common
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject EnvironmentSettings EnvSettings

@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Omnia Control</title>
    <base href="~/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="~/fontawesome/css/all.min.css" />
    <link href="_content/Blazored.Toast/blazored-toast.min.css" rel="stylesheet" />
    <link href="_content/BlazorMonaco/lib/monaco-editor/min/vs/editor/editor.main.css" rel="stylesheet" />
    <link rel="stylesheet" href="_content/Telerik.UI.for.Blazor/css/kendo-theme-default/all.css" />
    <link href="Pondres.Omnia.Control.styles.css" rel="stylesheet" />
    <link href="css/site.css" rel="stylesheet" />
    <link href="css/ribbon.css" rel="stylesheet" />
    <link href="css/omnia.css" rel="stylesheet" />
    <link href="https://blazor.cdn.telerik.com/blazor/5.0.1/kendo-theme-default/all.css" rel="stylesheet" />
    <link href="css/blazored-toast-overrides.css" rel="stylesheet" />
    <link href="css/telerik-overrides.css" rel="stylesheet" />
</head>
<body>

    @if (!EnvSettings.OmniaEnvironment.IsNullOrEmpty() && !EnvSettings.IsProduction())
    {
        <div class="ribbon ribbon-top-left <EMAIL>"><span></span></div>
    }
    <app>
        <component type="typeof(App)" render-mode="Server" />
    </app>

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>

    <script src="~/js/jquery-3.5.1.slim.min.js"></script>
    <script src="~/js/popper.min.js"></script>
    <script src="~/js/bootstrap.min.js"></script>

    <script src="~/js/custom/infiniteScroll.js"></script>
    <script src="~/js/custom/countdown-progress.js"></script>
    <script src="~/js/custom/monacoJsonSchema.js"></script>
    <script src="~/js/custom/scroll.js"></script>
    <script src="~/js/custom/downloader.js"></script>

    <!--Telerik components-->
    <script src="_content/Telerik.UI.for.Blazor/js/telerik-blazor.js"></script>

    <script src="_content/BlazorMonaco/lib/monaco-editor/min/vs/loader.js"></script>
    <script>require.config({ paths: { 'vs': '_content/BlazorMonaco/lib/monaco-editor/min/vs' } });</script>
    <script src="_content/BlazorMonaco/lib/monaco-editor/min/vs/editor/editor.main.js"></script>
    <script src="_content/BlazorMonaco/jsInterop.js"></script>
</body>
</html>
