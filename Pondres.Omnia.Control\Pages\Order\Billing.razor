﻿@page "/Billing/Orders"
@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Billing

@inherits CustomerPageBase

<PageHeader Title="Billing Orders">
    <CustomerSelection Customers="@Customers" SelectedCustomerId="@CurrentCustomerId" CustomerChanged="OnCustomerChangedAsync" />
</PageHeader>

<PageBody>
    <EditForm EditContext="@EditContext">
        <BillingOrderFilter Filter="Filter" FilterSubmitted="OnFilterChangedAsync" Loading="IsLoading"/>
        <LoadingSpinner Show=IsLoading></LoadingSpinner>
        <CascadingValue Value=Statistics>
            <BillingOrderGrid Loading="IsLoading"/>
        </CascadingValue>
    </EditForm>
</PageBody>


