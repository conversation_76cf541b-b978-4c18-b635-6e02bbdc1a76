﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
		<PackageReference Include="Pondres.Common" Version="3.20221025.2" />
		<PackageReference Include="Pondres.Omnia.NotificationHub.Contracts" Version="1.20250103.1" />
		<PackageReference Include="JsonSchema.Net" Version="7.3.1" />
		<PackageReference Include="BlazorMonaco" Version="3.3.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Control.Integrations.OrderHub\Pondres.Omnia.Control.Integrations.OrderHub.csproj" />
	</ItemGroup>
</Project>
