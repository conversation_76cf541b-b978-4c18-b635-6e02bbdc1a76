﻿@using Omnia.Warehouse.Contracts.Api.WarehouseDocument
@using System.Collections.ObjectModel
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Telerik.SvgIcons

@inherits SelectableGridBase<WarehouseDocumentListItem>

<div class="card shadow mb-4">
    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <GotoDropdown Disabled="!HasSelectedItems" StyleClass="float-right mx-1">
            @NavigationLinks
        </GotoDropdown>
    </TableHeader>
        
    <TelerikGrid Data="@Documents" @ref="DataGrid" Class="grid-no-scroll"
                 OnRowDoubleClick="@OnRowDoubleClick"
                 SelectedItemsChanged="@(async (IEnumerable<WarehouseDocumentListItem> items) => await InvokeAsync(StateHasChanged))"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="Documents" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(WarehouseDocumentListItem.DocumentId))" Title="Document" 
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="OrderMetadata.CustomerReference" Title="Customer reference" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

            <SelectableGridColumn Field="OrderMetadata.OrderId" Title="Order Id" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    <a class="link" @onclick="() => NavigateToOrder((context as WarehouseDocumentListItem))">
                        @((context as WarehouseDocumentListItem).OrderMetadata.OrderId.ToString())
                    </a>
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="LastStatus.StatusCode" Title="Status" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    <span>@((context as WarehouseDocumentListItem)?.LastStatus?.StatusCode) - @((context as WarehouseDocumentListItem)?.LastStatus?.StatusName)</span>
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@(nameof(WarehouseDocumentListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        </GridColumns>
    </TelerikGrid>
</div>