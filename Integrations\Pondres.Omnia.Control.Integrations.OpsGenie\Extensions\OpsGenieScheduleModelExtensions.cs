﻿using Pondres.Omnia.Control.Integrations.OpsGenie.Models;

namespace Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;

public static class OpsGenieScheduleModelExtensions
{
    public static string ToScheduledOnCallName(this OpsGenieScheduleModel model)
    {
        var firstPerson = model.Data?.OnCallRecipients?.FirstOrDefault();

        if (string.IsNullOrWhiteSpace(firstPerson))
            return string.Empty;

        var firstName = firstPerson[..firstPerson.IndexOf(".")];

        return firstName.ToCapitalizedFirstName();
    }
}
