﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;
using Pondres.Omnia.Control.Integrations.SecureMail.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.SecureMail;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddSecureMailServices(this IServiceCollection services, AppSettings appSettings)
    {
        services.AddHttpClient<ISecureMailDeliveryService, SecureMailDeliveryService>()
            .ConfigureBaseAddress(appSettings.SecureMailServiceUri)
            .ConfigureAuthenticationToken(appSettings.SecureMailServiceAuthToken);

        services.AddTransient<SecureMailDeliveryDefaultFilterContext>();
        services.AddTransient<SecureMailDeliveryBatchFilterContext>();

        return services;
    }
}
