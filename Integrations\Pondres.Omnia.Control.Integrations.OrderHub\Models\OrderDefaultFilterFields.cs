﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Models;

public class OrderDefaultFilterFields : FilterFieldsBase<OrderDefaultFilterFields>
{
    public string OrderId { get; set; }
    public string Reference { get; set; }
    public string StateTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    public string ResultTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    public string Flow { get; set; } = ControlConstants.DefaultDropdownValue;
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
    public string Category1 { get; set; }
    public string Category2 { get; set; }
    public string Category3 { get; set; }
}
