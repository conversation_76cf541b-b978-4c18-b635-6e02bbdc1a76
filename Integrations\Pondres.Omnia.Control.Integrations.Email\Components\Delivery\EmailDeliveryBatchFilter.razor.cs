﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Email.Models;

namespace Pondres.Omnia.Control.Integrations.Email.Components.Delivery
{
    public partial class EmailDeliveryBatchFilter
    {
        private EditContext EditContext;

        [Parameter]
        public EmailDeliveryBatchFilterContext Filter { get; set; }

        [Parameter]
        public EventCallback<EmailDeliveryBatchFilterContext> FilterChanged { get; set; }

        protected override void OnInitialized()
        {
            EditContext = new EditContext(Filter);
            EditContext.OnFieldChanged += OnFormFieldChanged;
            base.OnInitialized();
        }

        private async void OnFormFieldChanged(object sender, FieldChangedEventArgs e)
        {
            EditContext.MarkAsUnmodified();
            await FilterChanged.InvokeAsync(Filter);
        }

        private async Task ClearFilterAsync()
        {
            await Filter.ResetAsync();
            await FilterChanged.InvokeAsync(Filter);
        }
    }
}
