using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Bundle;

public partial class PrintBundleRemoveModal : ModalBase
{
    public RemoveDocumentModel Model { get; private set; }

    [Parameter]
    public EventCallback OnSaveAction { get; set; }

    [Parameter]
    public EventCallback OnCloseAction { get; set; }

    public bool ConfirmButtonEnabled { get; set; } = true;

    public void ShowDialog(RemoveDocumentModel model)
    {
        ConfirmButtonEnabled = true;
        Model = model;

        IsVisible = true;
    }

    public async Task OnSubmitModalAsync()
    {
        ConfirmButtonEnabled = false;

        if (OnSaveAction.HasDelegate)
            await OnSaveAction.InvokeAsync();

        await CloseDialogAsync();
    }

    public async Task CloseDialogAsync()
    {
        if (OnCloseAction.HasDelegate)
            await OnCloseAction.InvokeAsync();

        Model = null;

        IsVisible = false;

        ConfirmButtonEnabled = true;
    }
}