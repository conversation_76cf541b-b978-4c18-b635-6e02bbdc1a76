﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Web2Omnia.Components.Configuration;
using Pondres.Omnia.Control.Integrations.Web2Omnia.Services;
using Pondres.Omnia.WebToOmnia.Contracts.Api.Configuration;
using Pondres.Omnia.Control.Integrations.Web2Omnia.Extensions;
using Telerik.Blazor;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Web2Omnia.Configuration;

public partial class Web2OmniaConfigurationGrid : SelectableGridBase<CustomerConfigurationDto>
{
    public Web2OmniaConfigurationEditor DialogEditor { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    [Inject]
    protected IWeb2OmniaService Web2OmniaService { get; set; }

    public List<CustomerConfigurationDto> Configurations { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadConfigurationsAsync();

        await base.OnInitializedAsync();
    }

    private async Task LoadConfigurationsAsync()
    {
        await InvokeAsync(StateHasChanged);

        try
        {
            Configurations = await Web2OmniaService.GetAllConfigurationsAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    public async Task OnSaveActionAsync()
    {
        try
        {
            if (DialogEditor.IsNew)
                await Web2OmniaService.CreateConfigurationAsync(DialogEditor.Configuration.ToCreateDto());
            else
                await Web2OmniaService.UpdateConfigurationAsync(DialogEditor.Configuration.ToUpdateDto());

            await LoadConfigurationsAsync();

            await DialogEditor.CloseDialogAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private async Task OnViewCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var config = await Web2OmniaService.GetConfigurationDetailsAsync((e.Item as CustomerConfigurationDto).Customer);

            DialogEditor.ShowDialog(config.ToJson(), true);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    public void OnAddCommand()
    {
        DialogEditor.ShowDialog(new CreateCustomerConfigurationDto().ToJson(), false, true);
    }

    private async Task EditCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var config = await Web2OmniaService.GetConfigurationDetailsAsync((e.Item as CustomerConfigurationDto).Customer);

            DialogEditor.ShowDialog(config.ToJson(), false);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }
}