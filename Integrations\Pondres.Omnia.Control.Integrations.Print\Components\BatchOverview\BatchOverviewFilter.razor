﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters
@using Pondres.Omnia.Control.Integrations.Print.Client;

<ListFilter Filter="Filter" FilterChanged="OnFilterChangedAsync">
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.Customer" LabelText="Customer" />
        <ListFilterInputText @bind-Value="Filter.Fields.DGCode" LabelText="DG Code" />
        <ListFilterInputText @bind-Value="Filter.Fields.GordNumber" LabelText="Gordnumber" />
        <ListFilterInputText @bind-Value="Filter.Fields.BatchName" LabelText="Batch name" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.PrinterType" LabelText="Printer type" Options="Enum.GetNames(typeof(PrinterType)).ToList()" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.StateTypeName" LabelText="State" Options="Enum.GetNames(typeof(PrintBundleStateType)).ToList()" />
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedOn" LabelText="Created on" />
        <ListFilterInputDateTime @bind-Value="Filter.Fields.MailDate" LabelText="Mail date" />
    </ListFilterRow>
    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize">
        <ListFilterInputCheckbox @bind-Value="Filter.Fields.ShowTestCustomers" LabelText="Test customers" />
        <ListFilterInputCheckbox @bind-Value="Filter.Fields.ShowEmptyBundles" LabelText="Empty bundles" />
    </ListFilterPagenator>
</ListFilter>
