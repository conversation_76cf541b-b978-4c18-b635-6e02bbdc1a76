using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Response;

public partial class ResponseSummary
{
    [Inject]
    IToastService ToastService { get; set; }

    [Inject] 
    IWarehouseResponseService WarehouseService { get; set; }

    [Parameter]
    public WarehouseResponseDetails Response { get; set; }

    [Parameter]
    public string Filename { get; set; }

    [Parameter]
    public RenderFragment ActionButtons { get; set; }

    [Parameter]
    public EventCallback OnReprocess { get; set; }

    private FileDisplayModal FileDetailsModal { get; set; }

    private FileModel FileDetails { get; set; }

    private ActionModal<string> FileReprocessModal { get; set; }

    private async Task ReprocessFileASync(string filename) => await WarehouseService.ReprocessFileAsync(filename);
    private async Task OpenFileModalAsync()
    {
        try
        {
            FileDetails = await WarehouseService.GetFileContentsAsync(Filename);
            await FileDetailsModal.OpenAsync(FileDetails);
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Failed to display file: {ex.Message}");
        }
    }
}
