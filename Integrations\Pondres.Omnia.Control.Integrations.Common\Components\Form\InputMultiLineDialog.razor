﻿@using System.Diagnostics.CodeAnalysis
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal;

@inherits InputBase<List<string>>

<button class="btn btn-primary ml-1" @onclick="OpenModalAsync"><i class="oi oi-list mr-2"></i> @CreateFieldTitle()</button>

<ModalWindow @ref="EditWindow">
    <ModalHeader>
        @FieldName
    </ModalHeader>
    <ModalBody>
        <div class="form-row">
            <div class="form-group col-sm-12">
                Please provide each @ItemName on a seperate line
            </div>
        </div>
        <div class="form-row">
            <div class="form-group col-sm-12">
                <EditForm EditContext="EditContext">
                    <InputTextArea @bind-Value="CurrentValueAsString" class="form-control" rows="20" maxlength="10000"></InputTextArea>
                </EditForm>
            </div>
        </div>
    </ModalBody>
    <ModalFooter>
        <button class="btn btn-secondary" @onclick="CloseModalAsync">Close</button>
    </ModalFooter>
</ModalWindow>