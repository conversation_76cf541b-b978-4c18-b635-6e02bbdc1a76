﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Intersolve.Extensions;
using Pondres.Omnia.Control.Integrations.Intersolve.Models;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
{
    public partial class IntersolveOrderGridFilter
    {
        [Inject]
        private IApiFilterManager FilterManager { get; set; }

        [Inject]
        private IntersolveOrderDefaultFilterContext defaultFilter { get; set; }

        [Parameter]
        public EventCallback<IIntersolveOrderFilterContext> FilterChanged { get; set; }

        protected override async Task OnInitializedAsync()
        {
            FilterManager.SetFilters(defaultFilter);
            await FilterManager.SetInitialFilterFromUriAsync();
        }

        private async void OnFilterTabChanged(TabPanelTab<IIntersolveOrderFilterContext> selectedTab)
        {
            await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);

            if (FilterChanged.HasDelegate)
                await FilterChanged.InvokeAsync(selectedTab.Data);
        }

        private async void OnFilterChanged(IIntersolveOrderFilterContext filter)
        {
            await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);

            if (FilterChanged.HasDelegate)
                await FilterChanged.InvokeAsync(filter);
        }
    }
}
