﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;

namespace Pondres.Omnia.Control.Pages.Intersolve
{
    public partial class Details
    {
        [Inject]
        private IntersolveServiceClient IntersolveServiceClient { get; set; }

        [Inject]
        private IToastService ToastService { get; set; }

        [Parameter]
        public string ProductOwnerNr { get; set; }

        [Parameter]
        public string IntersolveOrderId { get; set; }

        [Parameter]
        public string Customer { get; set; }

        private IntersolveOrderDetails Order { get; set; }
        private bool InvalidResponse { get; set; } = false;
        private bool LoadingOrders { get; set; } = true;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
                await RefreshPageAsync();
        }

        private async Task RefreshPageAsync()
        {
            LoadingOrders = true;
            try
            {
                Order = await IntersolveServiceClient.OrderDetailsAsync(
                    productOwnerNr: ProductOwnerNr,
                    customer: Customer,
                    id: IntersolveOrderId);
            }
            catch (Exception ex)
            {
                InvalidResponse = true;
                ToastService.ShowError(ex.Message);
            }

            LoadingOrders = false;

            await InvokeAsync(StateHasChanged);
        }
    }
}