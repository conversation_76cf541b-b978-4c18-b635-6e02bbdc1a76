﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.ESafe.Models;

namespace Pondres.Omnia.Control.Integrations.ESafe.Extensions;

public static class DeliveryTimeoutModelExtensions
{
    public static PagedResultModel<ESafeDeliveryTimeoutModel> ToPagedResultModel(this List<ESafeDeliveryTimeoutModel> timeouts) =>
        new() { Items = timeouts.OrderBy(x => x.ExpectedOn).ToList() };
}
