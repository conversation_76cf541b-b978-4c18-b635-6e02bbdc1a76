using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using System.Diagnostics.CodeAnalysis;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Form;

public partial class InputMultiLineDialog : InputBase<List<string>>
{
    [Parameter]
    public string FieldName { get; set; }

    [Parameter]
    public string ItemName { get; set; }

    private ModalWindow EditWindow { get; set; }

    private async Task CloseModalAsync() => await EditWindow.CloseAsync();

    private async Task OpenModalAsync() => await EditWindow.OpenAsync();

    private string CreateFieldTitle()
    {
        if (CurrentValueHasItems())
            return $"{FieldName} ({CurrentValue.Count})";
        else
            return FieldName;
    }

    private bool CurrentValueHasItems() => CurrentValue != null && CurrentValue.Any();

    protected override string FormatValueAsString(List<string> value) => string.Join(Environment.NewLine, value);

    protected override bool TryParseValueFromString(string value, [MaybeNullWhen(false)] out List<string> result, [NotNullWhen(false)] out string validationErrorMessage)
    {
        if (string.IsNullOrEmpty(value))
        {
            validationErrorMessage = "Value cannot be empty";
            result = new List<string>();
            return true;
        }

        validationErrorMessage = null;
        result = value.Split().Select(x => x.Trim()).ToList();

        return true;
    }
}
