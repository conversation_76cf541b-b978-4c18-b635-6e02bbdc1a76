﻿@using Pondres.Omnia.NotificationHub.Contracts.Internal.Model
@using Pondres.Omnia.NotificationHub.Contracts.Model

<TelerikDialog Visible="@showModal" Width="800px">
    <DialogTitle>
        Edit Mapping
    </DialogTitle>
    <DialogContent>
        <div class="row">
            <div class="col-6 pr-0">
                <EditForm EditContext="editContext" Id="MappingForm" OnValidSubmit="OnSubmitModalAsync">

                    <DataAnnotationsValidator />

                    <div class="form-group col-12">
                        <label class="pr-4 col-form-label">Definition type</label>
                        <InputSelect Value="@mappingModel.DefinitionType" class="form-control" 
                                     ValueChanged="((NotificationDefinitionType? type) => TypeChangedAsync(type))" 
                                     ValueExpression="(() => mappingModel.DefinitionType)">
                            <option value="" disabled selected>Select definition type</option>
                            @foreach (var definitionType in Enum.GetValues(typeof(NotificationDefinitionType)))
                            {
                                <option value="@definitionType">@definitionType</option>
                            }
                        </InputSelect>
                        <ValidationMessage For="@(() => mappingModel.DefinitionType)" />
                    </div>

                    <div class="form-group col-12">
                        <label class="pr-4 col-form-label">Event type</label>
                        <InputSelect Value="@mappingModel.EventType" class="form-control"
                                     ValueChanged="((NotificationEventType? type) => EventTypeChangedAsync(type))"                                      
                                     ValueExpression="(() => mappingModel.EventType)">
                            <option value="" disabled selected>Select event type</option>
                            @foreach (var eventType in Enum.GetValues(typeof(NotificationEventType)))
                            {
                            <option value="@eventType">@eventType</option>
                            }
                        </InputSelect>
                        <ValidationMessage For="@(() => mappingModel.EventType)" />
                    </div>

                    <div class="form-group col-12">
                        <label class="pr-4 col-form-label">Definition</label>
                        <InputSelect Value="mappingModel.DefinitionId" class="form-control" 
                                     ValueChanged="((string defId) => DefinitionIdChangedAsync(defId))" 
                                     ValueExpression="(() => mappingModel.DefinitionId)">
                            <option value="" disabled selected>Select definition id</option>
                            @foreach (var definition in MappingContext.Definitions.Where(x => x.Type == mappingModel.DefinitionType.ToString()).ToList())
                            {
                                <option value="@definition.Id">@definition.Id</option>
                            }
                        </InputSelect>
                        <ValidationMessage For="@(() => mappingModel.DefinitionId)" />
                    </div>

                    <div class="form-group col-12">
                        <label class="pr-4 col-form-label">Flow</label>
                        <InputSelect Value="@mappingModel.Flow" class="form-control"
                                     ValueChanged="((string flow) => FlowChangedAsync(flow))"
                                     ValueExpression="(() => mappingModel.Flow)">
                            <option value="" disabled selected>Select flow</option>
                            @foreach (var flow in flows)
                            {
                                <option value="@flow">@flow</option>
                            }
                        </InputSelect>
                    </div>

                    <div class="form-group col-12">
                        <input id="mapping-enabled" type="checkbox" style="transform: scale(1.5); " @bind="mappingModel.Enabled" />
                        <label for="mapping-enabled" class="pl-2 col-form-label">Enabled</label>
                    </div>

                </EditForm>
            </div>

            <div class="col-6 pl-0" style="height: 350px">
                <pre class="m-0 h-100" style="white-space: pre;"><code>@definitionJson</code></pre>
            </div>
        </div>
    </DialogContent>
    <DialogButtons>
        <button class="btn btn-secondary" @onclick="CloseModal">Close</button>
        <button class="btn btn-success text-white" type="submit" form="MappingForm">Save</button>
    </DialogButtons>
</TelerikDialog>