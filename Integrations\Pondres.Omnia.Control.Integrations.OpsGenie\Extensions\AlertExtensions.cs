﻿using Pondres.Omnia.Control.Integrations.OpsGenie.Models;

namespace Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;

public static class AlertExtensions
{
    public static AlertViewModel ToViewModel(this List<Alert> alerts, string priority)
    {
        var alertsOfPriority = alerts.Where(x => x.Priority == priority);

        return new AlertViewModel()
        {
            Amount = alertsOfPriority.Count(),
            HasUnacknowledged = alertsOfPriority.Any(x => !x.Acknowledged)
        };
    }
}
