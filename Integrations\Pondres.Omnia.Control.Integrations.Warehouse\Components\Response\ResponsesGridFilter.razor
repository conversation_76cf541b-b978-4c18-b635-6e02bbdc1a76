﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

<TabPanel TDataType="IWarehouseResponseFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IWarehouseResponseFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IWarehouseResponseFilterContext">
                <TabPanelTab TDataType="IWarehouseResponseFilterContext" Name="Default" Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <ResponsesDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>