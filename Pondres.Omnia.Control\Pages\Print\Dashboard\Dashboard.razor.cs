using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Services;

namespace Pondres.Omnia.Control.Pages.Print.Dashboard;

[Authorize(Roles = "PrintReader, PrintContributor, ControlOwner")]
public partial class Dashboard
{
    [Inject]
    IPrintService PrintService { get; set; }

    [Inject] 
    NavigationManager NavigationManager { get; set; }

    [Inject]
    IToastService ToastService { get; set; }

    private PrintBundleTotalsResult TotalsResult { get; set; }
    private PrintDashboardFilter Filter { get; set; } = new PrintDashboardFilter();
    private bool Loading { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var newfilter = FilterHelper.GetFilterFromUri<PrintDashboardFilter>(NavigationManager.ToAbsoluteUri(NavigationManager.Uri));
        Filter.Merge(newfilter);

        await LoadAllAsync();
    }

    private async void OnFilterChanged(PrintDashboardFilter filter)
    {
        Filter.Merge(filter);
        NavigationManager.NavigateTo($"/Print/Dashboard?{FilterHelper.ConvertToQueryParameter(filter)}");

        await LoadAllAsync();
    }

    private async Task StartLoadingAsync()
    {
        Loading = true;
        await InvokeAsync(StateHasChanged);
    }

    private async Task StopLoadingAsync()
    {
        Loading = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task LoadAllAsync()
    {
        await StartLoadingAsync();
        await LoadPrintBundleTotalsAsync();
        await StopLoadingAsync();
    }

    private async Task LoadPrintBundleTotalsAsync()
    {
        try
        {
            TotalsResult = await PrintService.GetPrintBundleTotalsAsync(Filter);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }
}
