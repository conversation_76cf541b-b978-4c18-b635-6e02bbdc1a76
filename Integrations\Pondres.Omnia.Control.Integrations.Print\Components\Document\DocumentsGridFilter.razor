﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

<TabPanel TDataType="IPrintDocumentFilterContext" TabChanged="OnFilterTabChanged">
	<div class="card shadow mb-4">
		<div class="card-header">
			<TabPanelHeader TDataType="IPrintDocumentFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
		</div>
		<div class="card-body">
			<TabPanelTabs TDataType="IPrintDocumentFilterContext">
				<TabPanelTab TDataType="IPrintDocumentFilterContext" Name="Default" Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
					<Template>
						<DocumentsDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" />
					</Template>
				</TabPanelTab>
				<TabPanelTab TDataType="IPrintDocumentFilterContext" Name="Batch" Data="BatchFilter" Selected="FilterManager.CurrentFilter == BatchFilter">
					<Template>
						<DocumentsBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
					</Template>
				</TabPanelTab>
			</TabPanelTabs>
		</div>
	</div>
</TabPanel>


