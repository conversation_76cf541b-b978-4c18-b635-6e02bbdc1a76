﻿@page "/Print/Configuration/Schedules"

@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Print.Client;
@using Pondres.Omnia.Control.ViewModels.Schedules
@using Pondres.Omnia.Control.Integrations.Print.Components.Configuration
@using Pondres.Omnia.Control.Integrations.Print.Extensions
@using Telerik.DataSource

@inject ISchedulesOverviewViewModel schedulesOverviewViewModel;
@inject NavigationManager navigationManager

@inherits SelectableCustomerGridPageBase<ReleaseSchedules>

<ReleaseSchedulesEditor @ref="DialogEditor" OnSaveAction="@OnSaveActionAsync" />

<PageHeader Title="Release Schedules configuration"></PageHeader>
<PageBody>
    @if (Loading)
    {
        <LoadingSpinner />
    }
    else
    {
        <TelerikCard>
            <CardHeader>
                <CardTitle>
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Release schedules configuration</h6>
                    <AuthorizeView Roles="ControlContributor, ControlOwner">
                        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Success)" OnClick="@OnAddCommand">Add</TelerikButton>
                    </AuthorizeView>
                </CardTitle>
            </CardHeader>
            <CardBody>
                <TelerikGrid Data=@ReleaseSchedulesList @ref="DataGrid" Class="grid-no-scroll"
                         OnStateInit="@((GridStateEventArgs<ReleaseSchedules> args) => OnGridStateInit(args))"
                         SelectionMode="GridSelectionMode.None">
                    <GridColumns>
                        <GridColumn Field="@(nameof(ReleaseSchedules.Id))" Title="Name" />
                        <GridColumn Field="@(nameof(ReleaseSchedules.Type))" Title="Type" />
                        <GridColumn Field="@(nameof(ReleaseSchedules.Active))" Title="Active" />
                        <GridCommandColumn Context="schedule" Width="140px">
                            <AuthorizeView Roles="ControlContributor, ControlOwner">
                                <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnEditCommand" Command="Edit">Edit</GridCommandButton>
                            </AuthorizeView>
                        </GridCommandColumn>
                        <GridCommandColumn Context="schedule" Width="140px">
                            <AuthorizeView Roles="PrintContributor, PrintReader">
                                <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand" Command="View">View</GridCommandButton>
                            </AuthorizeView>
                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            </CardBody>
        </TelerikCard>
    }
</PageBody>

@code {
    public bool Loading { get; set; }
    public List<ReleaseSchedules> ReleaseSchedulesList { get; set; }
    public ReleaseSchedulesEditor DialogEditor { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadAllAsync();
    }

    private async Task StartLoadingAsync()
    {
        Loading = true;
        await InvokeAsync(StateHasChanged);
    }

    private async Task StopLoadingAsync()
    {
        Loading = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task LoadAllAsync()
    {
        await StartLoadingAsync();
        try 
        {
            ReleaseSchedulesList = await schedulesOverviewViewModel.LoadAllReleaseSchedulesAsync();
        } 
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
        await StopLoadingAsync();
    }

    public async Task OnSaveActionAsync()
    {
        var releaseSchedule = DialogEditor.ReleaseSchedule.ToDto();

        try
        {
            ReleaseScheduleResult result = null;

            if (DialogEditor.IsNew)
                result = await schedulesOverviewViewModel.CreateReleaseSchedulesAsync(releaseSchedule);
            else
                result = await schedulesOverviewViewModel.UpdateReleaseScheduleAsync(releaseSchedule);

            if (result.IsValid)
            {
                await LoadAllAsync();
                await DialogEditor.CloseDialogAsync();
            }
            else
                DialogEditor.ValidationMessage = result.Message;
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private void OnAddCommand()
    {
        DialogEditor.ShowDialog(CreateEmptyReleaseSchedule().ToJson(), isView: false, isNew: true);
    }

    private void OnDetailsCommand(GridCommandEventArgs e)
    {
        DialogEditor.ShowDialog((e.Item as ReleaseSchedules).ToJson(), true);
    }

    private void OnEditCommand(GridCommandEventArgs e)
    {
        DialogEditor.ShowDialog((e.Item as ReleaseSchedules).ToJson(), false);
    }

    private static void OnGridStateInit(GridStateEventArgs<ReleaseSchedules> args)
    {
        var state = new GridState<ReleaseSchedules>
            {
                SortDescriptors = new List<SortDescriptor>
            {
                new SortDescriptor{ Member = nameof(ReleaseSchedules.Id), SortDirection = ListSortDirection.Descending }
            }
            };

        args.GridState = state;
    }

    private static ReleaseSchedules CreateEmptyReleaseSchedule()
    {
        return new ReleaseSchedules
            {
                Schedule = new List<ReleasePrintSchedule>()
                {
                    new ReleasePrintSchedule()
                    {
                        DayOfWeek = new List<string>(),
                        Releases = new List<PrintScheduleTime>()
                        {
                            new PrintScheduleTime()
                        }
                    }
                }
            };
    }
}
