﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Email.Contracts.Api;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Services
{
    public class EmailDeliveryService : BaseApiService, IEmailDeliveryService
    {
        public EmailDeliveryService(HttpClient httpClient) : base(httpClient)
        { }

        public async Task<DeliveryDetailedData> GetDetailsAsync(string customer, Guid deliveryId) =>
            await GetAsync<DeliveryDetailedData>($"email/delivery/details?deliveryId={deliveryId}&customer={customer}");

        public async Task<PagedResultModel<DeliveryData>> GetDeliveriesAsync(DeliveryBatchFilter filter)
        {
            var listResult = await PostWithResultAsync<PagedCollection<DeliveryData>>(filter, "email/deliveryBatch/getDeliveries");

            return new PagedResultModel<DeliveryData>
            {
                Items = listResult.Items.ToList(),
                ContinuationToken = listResult.ContinuationToken
            };
        }

        public async Task<PagedResultModel<DeliveryData>> GetDeliveriesAsync(DeliveryDefaultFilter filter)
        {
            var listResult = await PostWithResultAsync<PagedCollection<DeliveryData>>(filter, "email/delivery/getDeliveries");

            return new PagedResultModel<DeliveryData>
            {
                Items = listResult.Items.ToList(),
                ContinuationToken = listResult.ContinuationToken
            };
        }

        public async Task UpdateBouncedResultAsync(string customer, Guid deliveryId)
        {
            var route = "email/delivery/handleBounced";
            var queryParams = new Dictionary<string, string>
            {
                {  new string(nameof(customer)), customer },
                {  new string(nameof(deliveryId)), deliveryId.ToString() }
            };

            await PostWithParametersAsync(route, queryParams);
        }
    }
}
