using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Configuration;

public partial class UpsertCustomerConfigurationModal
{
    [Inject] 
    IWarehouseResponseService WarehouseService { get; set; }

    [Inject]
    IToastService ToastService { get; set; }

    [Parameter]
    public EventCallback OnSubmit { get; set; }

    private CustomerConfiguration configuration;
    private ModalWindow ModalWindow { get; set; }

    private EditContext editContext;
    private bool addOnSubmit;
    private string ModalHeader => addOnSubmit ? "Add Configuration" : "Edit Configuration";

    public async Task OpenNewModalAsync(string customer)
    {
        addOnSubmit = true;
        await OpenModalAsync(new()
        { Customer = customer });
    }

    public async Task OpenEditModalAsync(CustomerConfiguration configuration)
    {
        addOnSubmit = false;
        await OpenModalAsync(configuration);
    }

    private async Task OpenModalAsync(CustomerConfiguration configuration)
    {
        this.configuration = configuration;
        editContext = new(this.configuration);

        await ModalWindow.OpenAsync();
        await InvokeAsync(StateHasChanged);
    }

    private async Task CloseModalAsync() => await ModalWindow.CloseAsync();

    private async Task AddUpdateCustomerConfigurationAsync()
    {
        try
        {
            if (addOnSubmit)
                await AddCustomerConfigurationAsync();
            else
                await UpdateCustomerConfigurationAsync();
            await OnSubmit.InvokeAsync();
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }

    private void AddAddressMappingLine()
    {
        configuration.AddressMappings.Add(new());
    }

    private void RemoveAddressMappingLine(CustomerConfigurationAddressMapping mapping)
    {
        configuration.AddressMappings.Remove(mapping);
    }

    private async Task AddCustomerConfigurationAsync() =>
        await WarehouseService.CreateCustomerConfigurationAsync(configuration);

    private async Task UpdateCustomerConfigurationAsync() =>
        await WarehouseService.UpdateCustomerConfigurationAsync(configuration);
}
