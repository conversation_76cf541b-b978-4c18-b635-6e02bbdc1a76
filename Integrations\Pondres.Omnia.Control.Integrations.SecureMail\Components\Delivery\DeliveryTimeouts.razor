﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid

<GridCard Title="Timeouts"
          GetRawColorFunc="(x) => x.RawColorClass"
          GetItemsAsync="(_) => Task.FromResult(ExpectedTimeouts.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Expected on</th>
        <th>Received on</th>
    </TableHeaderContent>
    <TableRowContent>
        <td>@context.ExpectedOn</td>
        <td>@context.ReceivedOn</td>
    </TableRowContent>
</GridCard>