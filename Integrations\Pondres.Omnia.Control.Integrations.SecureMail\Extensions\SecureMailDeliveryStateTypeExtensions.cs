﻿using Pondres.Omnia.SecureMail.Contracts.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Extensions;

public static class SecureMailDeliveryStateTypeExtensions
{
    public static string GetStatusColorStyle(this SecureMailDeliveryStateType state) => state switch
    {
        SecureMailDeliveryStateType.Active => "table-info",
        SecureMailDeliveryStateType.Completed => "table-success-light",
        SecureMailDeliveryStateType.Warning => "table-warning",
        _ => "table-light"
    };

    public static string GetStatusString(this SecureMailDeliveryStateType state, SecureMailDeliveryResultType result, bool? trackStatus = default) =>
        $"{state} ({result}){GetTrackStatusSuffix(trackStatus)}";

    private static string GetTrackStatusSuffix(bool? trackStatus = default) =>
        trackStatus.HasValue && !trackStatus.Value ? " - no tracking" : "";
}
