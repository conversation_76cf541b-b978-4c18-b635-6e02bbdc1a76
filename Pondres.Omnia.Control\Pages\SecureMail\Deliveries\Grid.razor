﻿@page "/SecureMail/Deliveries"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Link
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery
@using Pondres.Omnia.Control.Integrations.SecureMail.Extensions
@using Pondres.Omnia.Control.Integrations.SecureMail.Models
@using Pondres.Omnia.SecureMail.Contracts.Api.Delivery
@using Telerik.SvgIcons

@inherits CustomerFilterGridPageBase<DeliveryListItem, ISecureMailDeliveryFilterContext>

<PageHeader Title="Secure Mail Deliveries">
    <CustomerSelection Customers="@Customers" SelectedCustomerId="@CurrentCustomerId" CustomerChanged="OnCustomerChangedAsync" />
</PageHeader>
<PageBody>
    <SecureMailDeliveryGridFilter FilterChanged="OnFilterChangedAsync" Flows="@FilterFlows" />

    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <AuthorizeView Roles="ControlContributor, ControlOwner">
            <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Enabled="HasSelectedItems"
                           OnClick="CopySelectedSecureMailConnectorsToClipboardAsync">Copy connectors</TelerikButton>
            <GotoDropdown Disabled="!HasSelectedItems" StyleClass="float-right mx-1">
                <BatchOrderLink CustomerReferences="SelectedUniqueCustomerReferences" StyleClass="dropdown-item">Orders</BatchOrderLink>
            </GotoDropdown>
        </AuthorizeView>
    </TableHeader>

    <TelerikGrid Data="@GridItems" @ref="DataGrid" Class="grid-no-scroll"
                 OnRowDoubleClick="@OnRowDoubleClick"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="SecuremailDeliveries" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.Id))" Title="Id" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.CustomerReference))" Title="Customer reference"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

            <SelectableGridColumn Field="@(nameof(DeliveryListItem.StateTypeName))" Title="Status" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    @((context as DeliveryListItem).StateType.GetStatusString((context as DeliveryListItem).ResultType, (context as DeliveryListItem).TrackStatus))
                </Template>
            </SelectableGridColumn>

            <SelectableGridColumn Field="@(nameof(DeliveryListItem.Connector))" Title="Connector" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.Flow))" Title="Flow" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <GridCommandColumn Width="90px" OnCellRender="@ApplyBackground">
                <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand">Details</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>
</PageBody>
