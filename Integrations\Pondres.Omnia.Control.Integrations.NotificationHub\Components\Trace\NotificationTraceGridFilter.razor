﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab


<TabPanel TDataType="INotificationTraceFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="INotificationTraceFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="INotificationTraceFilterContext">
                <TabPanelTab TDataType="INotificationTraceFilterContext" Name="Default" Data="defaultFilter" Selected="FilterManager.CurrentFilter == defaultFilter">
                    <Template>
                        <NotificationTraceDefaultFilter Filter="defaultFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>


