﻿@using static Telerik.Blazor.ThemeConstants.Button;
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions
@using System.Text.Json

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <h6 class="m-0 font-weight-bold text-primary pt-2">
                @GetFlowOrderTypesString()
            </h6>
            <div class="float-right">
                @* Put actions here *@
            </div>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table p-0 m-0">
            <tbody>
                <tr class="@Order.Status.Status.ToStatusColorClass()">
                    <th>@nameof(Order.Status.Status)</th>
                    <td>@Order.Status.Status (@Order.Status.Timestamp.LocalDateTime)</td>
                </tr>
                <tr>
                    <th>@nameof(Order.CreatedOn)</th>
                    <td>@Order.CreatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th style="width:300px">@nameof(Order.OrderMetadataDefinition.OrderId)</th>
                    <td>
                        @OrderDetailsLink
                    </td>
                </tr>
                <tr>
                    <th>@nameof(Order.OrderMetadataDefinition.CustomerReference)</th>
                    <td>@Order.OrderMetadataDefinition.CustomerReference</td>
                </tr>
                <tr>
                    <th>@nameof(Order.OrderMetadataDefinition.Categories)</th>
                    <td>@GetConcatinatedCategories()</td>
                </tr>

                <tr>
                    <th>@nameof(Order.ProductOwnerNr)</th>
                    <td>@Order.ProductOwnerNr</td>
                </tr>
                <tr>
                    <th>@nameof(Order.PrivateCustomerNr)</th>
                    <td>@Order.PrivateCustomerNr</td>
                </tr>
                <tr>
                    <th>@nameof(Order.DeliveryMetadata.Reference)</th>
                    <td>@Order.DeliveryMetadata.Reference</td>
                </tr>
                <tr>
                    <th>@nameof(Order.DeliveryMetadata.CustomerType)</th>
                    <td>@Order.DeliveryMetadata.CustomerType</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>

