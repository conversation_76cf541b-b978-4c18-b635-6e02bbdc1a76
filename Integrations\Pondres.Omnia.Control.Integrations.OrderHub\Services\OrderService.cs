﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.OrderHub.Contracts.Api;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;
using Pondres.Omnia.OrderHub.Contracts.Api.OrderTask;
using Pondres.Omnia.OrderHub.Contracts.Order;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;
using Solude.Packages.Common;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Services;

public class OrderService : BaseApiService, IOrderService
{
    public OrderService(HttpClient httpClient) : base(httpClient)
    {
    }

    public async Task<OrderCancellationResponse> CancelOrderAsync(string customer, Guid orderId, string cancelledBy) =>
        await GetAsync<OrderCancellationResponse>($"order/cancel?customer={customer}&orderId={orderId}&cancelledBy={cancelledBy}");

    public async Task<OrderTaskCancellationResponse> CancelTaskAsync(Guid taskId) =>
        await PostWithResultAsync<OrderTaskCancellationResponse>(
            new OrderTaskCancelCommand { TaskId = taskId },
            "order/task/cancel");

    public async Task<OrderActiveStatus> GetActiveStatusAsync(string customer, Guid orderId) =>
        await GetAsync<OrderActiveStatus>($"order/activeStatusRaw?customer={customer}&OrderId={orderId}");

    public async Task<OrderFilesResult> GetFilesAsync(string customer, Guid orderId) =>
        await GetAsync<OrderFilesResult>($"order/files?customer={customer}&OrderId={orderId}");

    public Task<List<string>> GetFlowsAsync(string customer) =>
        GetAsync<List<string>>($"order/config/flows?customer={customer}");

    public async Task<OrderFullInformation> GetOrderByIdAsync(string customer, Guid orderId) =>
        await GetAsync<OrderFullInformation>($"order/details?customer={customer}&orderid={orderId}");

    public async Task<PagedResultModel<OrderListItem>> GetOrdersByFilterAsync(OrderListFilter filter)
    {
        var orderListResult = await PostWithResultAsync<ListResultDto<OrderListItem>>(filter, $"order/pagedList");

        return new PagedResultModel<OrderListItem>
        {
            Items = orderListResult.Items,
            ContinuationToken = orderListResult.ContinuationToken
        };
    }

    public async Task<PagedResultModel<OrderListItem>> GetOrdersByBatchFilterAsync(OrderBatchSelectionFilter filter)
    {
        var orderListResult = await PostWithResultAsync<ListResultDto<OrderListItem>>(filter, $"order/batch/pagedList");

        return new PagedResultModel<OrderListItem>
        {
            Items = orderListResult.Items,
            ContinuationToken = orderListResult.ContinuationToken
        };
    }

    public async Task<OrderTaskRetryResult> RetryTaskAsync(Guid orderId, Guid taskId) =>
        await PostWithResultAsync<OrderTaskRetryResult>(
            new OrderTaskRetryCommand { OrderId = orderId, TaskId = taskId },
            "order/task/retry");

    public async Task<OrderRetryStarted> RetryOrderAsync(string customer, Guid orderId) =>
        await GetAsync<OrderRetryStarted>($"order/retry?customer={customer}&orderId={orderId}");

    public async Task<OrderStarted> StartOrderAsync(string customer, Guid orderId) =>
        await GetAsync<OrderStarted>($"order/start?customer={customer}&orderId={orderId}");

    public async Task<OrderRejectionResponse> RejectOrderAsync(OrderRejectCommand rejectCommand) =>
        await PostWithResultAsync<OrderRejectionResponse>(rejectCommand, $"order/reject");

    public async Task<OrderRetryFailedTasksResponse> RetryFailedOrderTasksAsync(Guid orderId) =>
        await PostWithResultAsync<OrderRetryFailedTasksResponse>(new OrderRetryFailedTasksCommand
        {
            OrderId = orderId
        }, $"order/retryFailedTasks");
}
