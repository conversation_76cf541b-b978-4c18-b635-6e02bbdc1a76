﻿using Pondres.Omnia.OrderHub.Contracts.Api.Flow;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions
{
    public static class OrderFlowDefinitionExtensions
    {
        public static string ToJson(this OrderFlowDefinition orderFlowDefinition) =>
            JsonSerializer.Serialize(orderFlowDefinition, new JsonSerializerOptions { WriteIndented = true });
    }
}