﻿@using static Telerik.Blazor.ThemeConstants.Button
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.OrderHub.Contracts.Api.OrderTask

<ActionModal @ref="CancelTaskModal" 
             T="OrderTaskInformation" 
             Context="task" 
             Title="Cancel preprocessing?" 
             ButtonText="Cancel" 
             ButtonType="danger"
             OnClose="RefreshPageAsync"
             OnSubmit="CancelBatchAsync">
    You are about to cancel <b>@task.Name</b>, Continue?
</ActionModal>

<ActionModal @ref="RetryTaskModal" 
             T="OrderTaskInformation" 
             Context="task" 
             Title="Retry preprocessing?" 
             ButtonText="Retry" 
             ButtonType="@ThemeColor.Primary"
             OnSubmit="RetryBatchAsync" 
             OnClose="RefreshPageAsync">
    You are about to retry <b>@task.Name</b>, Continue?
</ActionModal>

<GridCard Title="Tasks"
          GetRawColorFunc="@((task) => task.GetStatusColorStyle())"
          GetItemsAsync="(_) => Task.FromResult(OrderTasks.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Name</th>
        <th>Type</th>
        <th>StartedOn</th>
        <th></th>
    </TableHeaderContent>
    <TableRowContent Context="task">
        <td><b>@task.Name</b></td>
        <td>@task.Type</td>
        <td>@task.Status.StartedOn?.LocalDateTime</td>
        <td>
            <AuthorizeView Roles="ControlContributor, ControlOwner">
                @if (task.HasAction("cancel"))
                {
                    <button class="btn btn-danger float-right" @onclick="() => CancelTaskModal.OpenModal(task)">Cancel</button>
                }
                @if (task.HasAction("retry"))
                {
                    <button class="btn btn-primary float-right" @onclick="() => RetryTaskModal.OpenModal(task)">Retry</button>
                }
            </AuthorizeView>
        </td>
    </TableRowContent>
</GridCard>


