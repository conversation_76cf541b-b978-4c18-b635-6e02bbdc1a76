﻿{
  "openapi": "3.0.1",
  "info": {
    "title": "Pondres.Omnia.Email.Manager",
    "version": "1.0"
  },
  "servers": [
    {
      "url": "/email"
    }
  ],
  "paths": {
    "/configurations": {
      "post": {
        "tags": [
          "Configuration"
        ],
        "parameters": [
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateCustomerConfigurationModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateCustomerConfigurationModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CreateCustomerConfigurationModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              },
              "application/json": {
                "schema": {
                  "type": "string"
                }
              },
              "text/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          },
          "409": {
            "description": "Conflict"
          }
        }
      },
      "get": {
        "tags": [
          "Configuration"
        ],
        "parameters": [
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/CustomerConfigurationModel"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/CustomerConfigurationModel"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/CustomerConfigurationModel"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/configurations/{customer}": {
      "get": {
        "tags": [
          "Configuration"
        ],
        "parameters": [
          {
            "name": "customer",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              }
            }
          },
          "404": {
            "description": "Not Found"
          }
        }
      },
      "put": {
        "tags": [
          "Configuration"
        ],
        "parameters": [
          {
            "name": "customer",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateCustomerConfigurationModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateCustomerConfigurationModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateCustomerConfigurationModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/CustomerConfigurationModel"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              },
              "application/json": {
                "schema": {
                  "type": "string"
                }
              },
              "text/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          },
          "404": {
            "description": "Not Found"
          },
          "409": {
            "description": "Conflict"
          }
        }
      },
      "delete": {
        "tags": [
          "Configuration"
        ],
        "parameters": [
          {
            "name": "customer",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success"
          },
          "404": {
            "description": "Not Found"
          }
        }
      }
    },
    "/delivery/getDeliveries": {
      "post": {
        "tags": [
          "Delivery"
        ],
        "parameters": [
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliveryDefaultFilter"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliveryDefaultFilter"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/DeliveryDefaultFilter"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDataPagedCollection"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDataPagedCollection"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDataPagedCollection"
                }
              }
            }
          }
        }
      }
    },
    "/delivery/details": {
      "get": {
        "tags": [
          "Delivery"
        ],
        "parameters": [
          {
            "name": "deliveryId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "customer",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDetailedData"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDetailedData"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDetailedData"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              },
              "application/json": {
                "schema": {
                  "type": "string"
                }
              },
              "text/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/delivery/handleBounced": {
      "post": {
        "tags": [
          "Delivery"
        ],
        "parameters": [
          {
            "name": "customer",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "deliveryId",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "uuid"
            }
          },
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    },
    "/deliveryBatch/getDeliveries": {
      "post": {
        "tags": [
          "DeliveryBatch"
        ],
        "parameters": [
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliveryBatchFilter"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/DeliveryBatchFilter"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/DeliveryBatchFilter"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDataPagedCollection"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDataPagedCollection"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DeliveryDataPagedCollection"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "CreateCustomerConfigurationModel": {
        "type": "object",
        "properties": {
          "environments": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/EmailEnvironment"
            },
            "nullable": true
          },
          "vaultName": {
            "type": "string",
            "nullable": true
          },
          "customer": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CustomerConfigurationModel": {
        "type": "object",
        "properties": {
          "customer": {
            "type": "string",
            "nullable": true
          },
          "environments": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/EmailEnvironment"
            },
            "nullable": true
          },
          "vaultName": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliveryBatchFilter": {
        "type": "object",
        "properties": {
          "maxPageSize": {
            "type": "integer",
            "format": "int32"
          },
          "continuationToken": {
            "type": "string",
            "nullable": true
          },
          "customer": {
            "type": "string",
            "nullable": true
          },
          "deliveryIds": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "nullable": true
          },
          "customerReferences": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliveryData": {
        "type": "object",
        "properties": {
          "deliveryId": {
            "type": "string",
            "format": "uuid"
          },
          "orderMetadata": {
            "$ref": "#/components/schemas/OrderMetadata"
          },
          "customer": {
            "type": "string",
            "nullable": true
          },
          "result": {
            "$ref": "#/components/schemas/EmailDeliveryResultType"
          },
          "state": {
            "$ref": "#/components/schemas/EmailDeliveryStateType"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "lastUpdatedOn": {
            "type": "string",
            "format": "date-time"
          },
          "trackStatus": {
            "type": "boolean"
          },
          "reference": {
            "type": "string",
            "nullable": true
          },
          "customerReference": {
            "type": "string",
            "nullable": true
          },
          "flow": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliveryDataPagedCollection": {
        "type": "object",
        "properties": {
          "items": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DeliveryData"
            },
            "nullable": true
          },
          "continuationToken": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliveryDefaultFilter": {
        "type": "object",
        "properties": {
          "maxPageSize": {
            "type": "integer",
            "format": "int32"
          },
          "continuationToken": {
            "type": "string",
            "nullable": true
          },
          "orderId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "deliveryId": {
            "type": "string",
            "format": "uuid",
            "nullable": true
          },
          "customer": {
            "type": "string",
            "nullable": true
          },
          "customerReference": {
            "type": "string",
            "nullable": true
          },
          "flow": {
            "type": "string",
            "nullable": true
          },
          "stateTypeName": {
            "type": "string",
            "nullable": true
          },
          "resultTypeName": {
            "type": "string",
            "nullable": true
          },
          "createdFromDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "createdToDate": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliveryDetailedData": {
        "type": "object",
        "properties": {
          "deliveryId": {
            "type": "string",
            "format": "uuid"
          },
          "orderMetadata": {
            "$ref": "#/components/schemas/OrderMetadata"
          },
          "customer": {
            "type": "string",
            "nullable": true
          },
          "result": {
            "$ref": "#/components/schemas/EmailDeliveryResultType"
          },
          "state": {
            "$ref": "#/components/schemas/EmailDeliveryStateType"
          },
          "createdOn": {
            "type": "string",
            "format": "date-time"
          },
          "lastUpdatedOn": {
            "type": "string",
            "format": "date-time"
          },
          "trackStatus": {
            "type": "boolean"
          },
          "reference": {
            "type": "string",
            "nullable": true
          },
          "customerReference": {
            "type": "string",
            "nullable": true
          },
          "flow": {
            "type": "string",
            "nullable": true
          },
          "deliveryMetadata": {
            "$ref": "#/components/schemas/DeliveryMetadata"
          },
          "deliveryRequestMessage": {
            "type": "string",
            "nullable": true
          },
          "lastCheckStarted": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "DeliveryMetadata": {
        "type": "object",
        "properties": {
          "environmentName": {
            "type": "string",
            "nullable": true
          },
          "reference": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "EmailDeliveryPermissionType": {
        "enum": [
          "None",
          "SingleOptIn",
          "ConfirmedOptIn",
          "DoubleOptIn",
          "DoubleOptInPlus",
          "Other"
        ],
        "type": "string"
      },
      "EmailDeliveryResultType": {
        "enum": [
          "None",
          "Sent",
          "Bounced",
          "Opened",
          "Clicked",
          "BouncedHandled",
          "Failed"
        ],
        "type": "string"
      },
      "EmailDeliveryStateType": {
        "enum": [
          "None",
          "Active",
          "Completed",
          "Warning"
        ],
        "type": "string"
      },
      "EmailEnvironment": {
        "type": "object",
        "properties": {
          "checkInterval": {
            "type": "string",
            "example": "00:00:00"
          },
          "lastCheckStarted": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "name": {
            "type": "string",
            "nullable": true
          },
          "customer": {
            "type": "string",
            "nullable": true
          },
          "environmentUri": {
            "type": "string",
            "format": "uri",
            "nullable": true
          },
          "apiKey": {
            "type": "string",
            "nullable": true
          },
          "permissionType": {
            "$ref": "#/components/schemas/EmailDeliveryPermissionType"
          }
        },
        "additionalProperties": false
      },
      "OrderCategories": {
        "type": "object",
        "properties": {
          "one": {
            "type": "string",
            "nullable": true
          },
          "two": {
            "type": "string",
            "nullable": true
          },
          "three": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "OrderMetadata": {
        "type": "object",
        "properties": {
          "customer": {
            "type": "string",
            "nullable": true
          },
          "flow": {
            "type": "string",
            "nullable": true
          },
          "customerReference": {
            "type": "string",
            "nullable": true
          },
          "orderId": {
            "type": "string",
            "format": "uuid"
          },
          "categories": {
            "$ref": "#/components/schemas/OrderCategories"
          }
        },
        "additionalProperties": false
      },
      "UpdateCustomerConfigurationModel": {
        "type": "object",
        "properties": {
          "environments": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/EmailEnvironment"
            },
            "nullable": true
          },
          "vaultName": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      }
    }
  }
}