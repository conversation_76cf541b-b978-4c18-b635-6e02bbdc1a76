﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.NotificationHub.Contracts.Api.Configuration
@using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration
@inherits SelectableGridBase<NotificationConfigurationMappingModel>
@using Telerik.SvgIcons

<CascadingValue Value="@MappingContext">

    <MappingFormModal @ref="MappingFormModal" OnSubmit="OnConfigurationModalSubmitted" />

    <div class="card shadow mb-4">
        @if (MappingContext.Config == null)
        {
            <div class="card-body">
                No notification configuration found for customer <br />
                <button class="btn btn-primary" @onclick="@CreateConfigurationAsync">Initialize configuration</button>
            </div>
        }
        else
        {
            <div class="card-header py-3">
                <div class="d-flex justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Notification mappings</h6>
                    <AuthorizeView Roles="ControlContributor, ControlOwner, PrintContributer">
                        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Success)"  
                                       OnClick="OpenNewModalAsync">Add</TelerikButton>
                    </AuthorizeView>
                </div>
            </div>
                        
        <TelerikGrid Data="@MappingContext.Config.Mappings" @ref="DataGrid" Class="grid-no-scroll"
                     SelectedItemsChanged="@(async (IEnumerable<NotificationConfigurationMappingModel> items) => await InvokeAsync(StateHasChanged))"
                     SelectionMode="GridSelectionMode.Multiple">
            <GridExport>
                <GridCsvExport FileName="notificationmappings" OnBeforeExport="@OnBeforeCsvExport" />
            </GridExport>
            <GridToolBarTemplate>
                <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export to selected CSV" : "Export to CSV")</GridCommandButton>
                <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
            </GridToolBarTemplate>
            <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" />
                <SelectableGridColumn Field="DefinitionId" Title="Name" SelectionChanged="@OnColumnSelect" />
                <SelectableGridColumn Field="DefinitionType" Title="Name" SelectionChanged="@OnColumnSelect" />
                <SelectableGridColumn Field="EventType" Title="Type" SelectionChanged="@OnColumnSelect" />
                <GridColumn Field="Enabled" Title="Enabled">
                    <Template>
                        @if ((context as NotificationConfigurationMappingModel).Enabled)
                        {<i class="fa fa-check pr-1" />}
                    </Template>
                </GridColumn>
                <GridCommandColumn Width="90px" Context="mapping">
                    <GridCommandButton Title="Edit" Class="btn btn-primary mx-1 float-right" OnClick="@EditMappingAsync">Edit</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
        </TelerikGrid>
        }
    </div>
</CascadingValue>