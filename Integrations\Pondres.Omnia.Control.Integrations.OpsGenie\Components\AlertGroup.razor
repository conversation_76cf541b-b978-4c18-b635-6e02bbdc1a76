﻿<div class="mb-5">
    <h4>@Title</h4>
    <div class="row">
        <div class="col-2 p-2">
            <Counter HeaderText="Not Acknowledged" Value="@Alerts?.NotAcknowledged.ToString()" Loading="Alerts == null" Size="1" BackgroundColor="@Alerts?.GetNotAcknowledgedBgColor()" />
        </div>
        <div class="col-2 p-2">
            <Counter HeaderText="P1" Value="@Alerts?.P1.Amount.ToString()" Loading="Alerts == null" Size="1" BackgroundColor="@Alerts?.P1.GetAlertBgColor(true)" />
        </div>
        <div class="col-2 p-2">
            <Counter HeaderText="P2" Value="@Alerts?.P2.Amount.ToString()" Loading="Alerts == null" Size="1" BackgroundColor="@Alerts?.P2.GetAlertBgColor(true)" />
        </div>
        <div class="col-2 p-2">
            <Counter HeaderText="P3" Value="@Alerts?.P3.Amount.ToString()" Loading="Alerts == null" Size="1" BackgroundColor="@Alerts?.P3.GetAlertBgColor(true)" />
        </div>
        <div class="col-2 p-2">
            <div class="mb-2">
                <Counter HeaderText="P4" Value="@Alerts?.P4.Amount.ToString()" Loading="Alerts == null" Size="1" BackgroundColor="@Alerts?.P4.GetAlertBgColor(false)"/>
            </div>
            <div>
                <Counter HeaderText="P5" Value="@Alerts?.P5.Amount.ToString()" Loading="Alerts == null" Size="1" BackgroundColor="@Alerts?.P5.GetAlertBgColor(false)" />
            </div>
        </div>
        @if(!string.IsNullOrEmpty(Alerts?.Scheduled))
        {
            <div class="col-2 p-2">
                <Counter HeaderText="Scheduled" Value="@Alerts?.Scheduled" Loading="Alerts == null" Size="3" BackgroundColor="bg-warning text-white" />
            </div>
        }
    </div>
</div>



