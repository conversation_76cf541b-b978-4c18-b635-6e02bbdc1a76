﻿@using Pondres.Omnia.Control.Integrations.Print.Client;

<EditForm EditContext="@EditContext">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 col-sm-12">
                <label class="pr-4 col-form-label">Customer</label>
                <InputText @bind-Value="Filter.Customer" class="form-control border-1 small" placeholder="Customer" />
            </div>

            <div class="col-md-3 col-sm-12">
                <label class="pr-4 col-form-label">PrinterType</label>
                <InputSelect @bind-Value="Filter.PrinterType" class="form-control">
                    <option>@ControlConstants.DefaultDropdownValue</option>
                    @foreach (var printerType in Enum.GetNames(typeof(PrinterType)))
                    {
                        <option>@printerType</option>
                    }
                </InputSelect>
            </div>

            <div class="col-md-3 col-sm-12">
                <label class="pr-4 col-form-label">Display</label>
                <InputSelect @bind-Value="Filter.DisplayOptionType" class="form-control">
                    @foreach (var displayOptionType in Enum.GetNames(typeof(PrintBundleTotalDisplayOptionType)))
                    {
                        <option>@displayOptionType</option>
                    }
                </InputSelect>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-12">
                <label class="pr-4 col-form-label">From</label>
                <InputDate class="form-control border-1 small" placeholder="From Date" @bind-Value="Filter.CreatedOn.FromDate" />
            </div>

            <div class="col-md-3 col-sm-12">
                <label class="pr-4 col-form-label">To</label>
                <InputDate class="form-control border-1 small" placeholder="From Date" @bind-Value="Filter.CreatedOn.ToDate" />
            </div>
        </div>
        <div class="row">
            <div class="col-12 pt-2">
                <div class="float-right">
                    <span class="m-1 mr-5">
                        <InputCheckbox id="test-customers" style="transform: scale(1.5);" @bind-Value="Filter.ShowTestCustomers" class="m-2" /><label for="test-customers">Show test customers</label>
                    </span>
                    <button class="btn btn-outline-primary ml-1" @onclick="ClearFilterAsync"><i class="oi oi-reload mr-2"></i> Reset</button>
                </div>
            </div>
        </div>
    </div>
</EditForm>




