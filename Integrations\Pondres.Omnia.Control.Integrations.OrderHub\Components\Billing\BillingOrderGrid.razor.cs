﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Billing;

public record OrderStatisticsRow(string Flow, string CategoryOne, string CategoryTwo, string CategoryThree, int ReceivedCount, int CompletedCount, int ActiveCount, int FailedCount);

public partial class BillingOrderGrid
{

    private const string total = "Totaal";

    [CascadingParameter]
    public List<OrderStatisticsResult> Statistics
    {
        set
        {
            Rows = GetOrderedCategories(value);
        }
    }

    public List<OrderStatisticsRow> Rows { get; set; } = new List<OrderStatisticsRow>();

    [Parameter]
    public bool Loading { get; set; }

    private static void OnRowRender(GridRowRenderEventArgs args)
    {
        if (args.Item is OrderStatisticsRow row && (row.CategoryOne == total || row.CategoryTwo == total || row.CategoryThree == total))
        {
            args.Class += " table-success-light"; // 'highlight-row' is your custom CSS class
        }
    }

    private static List<OrderStatisticsRow> GetOrderedCategories(List<OrderStatisticsResult> statistics)
    {
        var rows = new List<OrderStatisticsRow>();
        foreach (var statistic in statistics)
        {
            rows.Add(new OrderStatisticsRow(
                Flow: statistic.Flow,
                CategoryOne: total,
                CategoryTwo: total,
                CategoryThree: total,
                ReceivedCount: statistic.PerCategoryResults.Sum(x => x.ReceivedCount),
                CompletedCount: statistic.PerCategoryResults.Sum(x => x.CompletedCount),
                ActiveCount: statistic.PerCategoryResults.Sum(x => x.ActiveCount),
                FailedCount: statistic.PerCategoryResults.Sum(x => x.FailedCount)));

            foreach (var statisticsPerCategoryResult in statistic.PerCategoryResults)
            {
                rows.Add(new OrderStatisticsRow(
                Flow: statistic.Flow,
                CategoryOne: statisticsPerCategoryResult.CategoryInformation.One,
                CategoryTwo: statisticsPerCategoryResult.CategoryInformation.Two,
                CategoryThree: statisticsPerCategoryResult.CategoryInformation.Three,
                ReceivedCount: statisticsPerCategoryResult.ReceivedCount,
                CompletedCount: statisticsPerCategoryResult.CompletedCount,
                ActiveCount: statisticsPerCategoryResult.ActiveCount,
                FailedCount: statisticsPerCategoryResult.FailedCount));
            }
        }

        return rows;
    }
}
