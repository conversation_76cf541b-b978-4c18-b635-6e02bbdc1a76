using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using System.Text.Json;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Grid;

public partial class GridCard<TItem>
{
    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public Func<TItem, string> GetRawColorFunc { get; set; }

    [Parameter]
    public Func<string, Task<PagedResultModel<TItem>>> GetItemsAsync { get; set; }

    [Parameter]
    public RenderFragment TableHeaderContent { get; set; }

    [Parameter]
    public RenderFragment<TItem> TableRowContent { get; set; }

    [Parameter]
    public RenderFragment<TItem> TableRowExpandableContent { get; set; }

    private List<TItem> GridItems { get; set; } = new List<TItem>();
    private bool Loading { get; set; } = true;
    private string ContinuationToken { get; set; }
    private string CardTitle => $"{Title} ({GridItems.Count})";

    private readonly JsonSerializerOptions serializerOptions = new JsonSerializerOptions { WriteIndented = true };

    public bool Expanded { get; set; } = true;
    public TelerikAnimationContainer AnimationContainer { get; set; }


    protected override async Task OnInitializedAsync() => 
        await TryLoadNextPageAsync();

    private async Task TryLoadNextPageAsync()
    {
        try
        {
            Loading = true;
            await LoadNextPageAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError($"Failed to get {typeof(TItem).Name} list {exception.Message}");
        }
        finally
        {
            Loading = false;
        }
    }

    private async Task LoadNextPageAsync()
    {
        var page = await GetItemsAsync(ContinuationToken);
        ContinuationToken = page.ContinuationToken;
        GridItems.AddRange(page.Items);
    }

    private string GetItemJson(TItem item)
    {
        return JsonSerializer.Serialize(item, serializerOptions);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await AnimationContainer.ToggleAsync();
        }
        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task ToggleAsync()
    {
        if (Expanded && AnimationContainer != null)
        {
            await AnimationContainer.HideAsync();
        }
        else if (AnimationContainer != null)
        {
            await AnimationContainer.ShowAsync();
        }

        Expanded = !Expanded;
    }
}
