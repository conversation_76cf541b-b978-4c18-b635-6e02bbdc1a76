﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Email.Extensions;
using Pondres.Omnia.Control.Integrations.Email.Models;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.Email.Contracts.Api.Delivery;
using Pondres.Omnia.Email.Contracts.Delivery;
using Telerik.Blazor;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Email;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Grid : CustomerFilterGridPageBase<DeliveryData, IEmailDeliveryFilterContext>
{
    [Inject]
    protected IOrderService OrderService { get; set; }

    [Inject]
    private IEmailDeliveryService EmailDeliveryService { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    public List<string> FilterFlows { get; private set; }
    private bool HasBouncedDeliveries => DataGrid.SelectedItems.Any(delivery => delivery.Result == EmailDeliveryResultType.Bounced);

    private void NavigateToDetails(string id) =>
        NavigationManager.NavigateTo($"Email/{CurrentCustomerId}/{id}");

    private void OnDetailsCommand(GridCommandEventArgs e) =>
         NavigateToDetails((e.Item as DeliveryData).DeliveryId.ToString());

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
         NavigateToDetails((e.Item as DeliveryData).DeliveryId.ToString());

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await TryLoadFilterFlowsAsync();

        await LoadGridItemsAsync(refresh: true);
    }

    private async Task TryLoadFilterFlowsAsync()
    {
        try
        {
            FilterFlows?.Clear();
            FilterFlows = await OrderService.GetFlowsAsync(CurrentCustomerId);
        }
        catch
        {
            ToastService.ShowError($"Flows could not be loaded for customer {CurrentCustomerId}");
        }
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await TryLoadFilterFlowsAsync();

            await LoadGridItemsAsync(refresh: true);
        }
    }

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as DeliveryData).State.GetStatusColorStyle();

    private async Task HandleBouncedAsync()
    {
        var selectedItems = DataGrid.SelectedItems.Where(delivery => delivery.Result == EmailDeliveryResultType.Bounced);

        bool isConfirmed = await Dialogs.ConfirmAsync("Are you sure you want to complete the bounced email?", title: "Complete bounced email");

        if (isConfirmed)
        {
            foreach (var delivery in selectedItems)
                await EmailDeliveryService.UpdateBouncedResultAsync(delivery.Customer, delivery.DeliveryId);

            await LoadGridItemsAsync(refresh: true);
        }
    }
}