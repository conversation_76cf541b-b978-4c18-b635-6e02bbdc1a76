using Microsoft.AspNetCore.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;

public partial class TabPanel<TDataType>
{
    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public EventCallback<TabPanelTab<TDataType>> TabChanged { get; set; }

    public List<TabPanelTab<TDataType>> Tabs { get; set; } = new List<TabPanelTab<TDataType>>();
    private TabPanelTab<TDataType> SelectedTab { get; set; } = null;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await SelectCurrentlySelectedTabAsync();
    }

    private async Task SelectCurrentlySelectedTabAsync()
    {
        if (Tabs.Any())
        {
            var selectedTab = Tabs.FirstOrDefault(x => x.Selected);

            if (selectedTab == null)
                await SetSelectedTabAsync(Tabs.First());
            else
                await SetSelectedTabAsync(selectedTab);
        }
    }

    public void AddColumn(TabPanelTab<TDataType> tab)
    {
        Tabs.Add(tab);
    }

    public void RemoveColumn(TabPanelTab<TDataType> tab)
    {
        if (IsSelected(tab))
            SelectedTab = null;

        Tabs.Remove(tab);
    }

    public async Task SelectTabAsync(TabPanelTab<TDataType> tab)
    {
        await SetSelectedTabAsync(tab);
    }

    public async Task SetSelectedTabAsync(TabPanelTab<TDataType> tab)
    {
        SelectedTab = tab;
        await TabChanged.InvokeAsync(tab);
        await InvokeAsync(StateHasChanged);
    }

    public bool IsSelected(TabPanelTab<TDataType> tab) => SelectedTab == tab;
}
