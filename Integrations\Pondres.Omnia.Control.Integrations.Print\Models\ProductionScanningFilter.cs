namespace Pondres.Omnia.Control.Integrations.Print.Models
{
    public class ProductionScanningFilter
    {
        private string barCode = "";

        public string BarCode
        {
            get => barCode;
            set => barCode = value?.Trim() ?? "";
        }

        public string BatchName { get; set; } = "";
        public List<string> BarCodesToBeReprinted { get; set; } = [];
        public string Customer { get; set; } = "";

        public void Reset()
        {
            BatchName = "";
            BarCode = "";
            BarCodesToBeReprinted = [];
        }

        public bool IsEmpty()
        {
            return string.IsNullOrEmpty(BatchName) && string.IsNullOrEmpty(BarCode);
        }

    }
}