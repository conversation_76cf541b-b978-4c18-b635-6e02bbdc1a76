﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;
using Pondres.Omnia.OrderHub.Contracts.Order;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Services;

public interface IOrderService
{
    Task<List<string>> GetFlowsAsync(string customer);

    Task<OrderActiveStatus> GetActiveStatusAsync(string customer, Guid orderId);

    Task<OrderCancellationResponse> CancelOrderAsync(string customer, Guid orderId, string cancelledBy);

    Task<OrderRetryStarted> RetryOrderAsync(string customer, Guid orderId);

    Task<OrderStarted> StartOrderAsync(string customer, Guid orderId);

    Task<OrderRejectionResponse> RejectOrderAsync(OrderRejectCommand rejectCommand);

    Task<OrderRetryFailedTasksResponse> RetryFailedOrderTasksAsync(Guid orderId);

    Task<PagedResultModel<OrderListItem>> GetOrdersByFilterAsync(OrderListFilter filter);

    Task<PagedResultModel<OrderListItem>> GetOrdersByBatchFilterAsync(OrderBatchSelectionFilter filter);

    Task<OrderFilesResult> GetFilesAsync(string customer, Guid orderId);

    Task<OrderFullInformation> GetOrderByIdAsync(string customer, Guid orderId);

    Task<OrderTaskCancellationResponse> CancelTaskAsync(Guid taskId);

    Task<OrderTaskRetryResult> RetryTaskAsync(Guid orderId, Guid taskId);
}
