﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderConfirmationTriggerExtensions
{
    public static PagedResultModel<OrderConfirmationTrigger> ToPagedResultModel(this List<OrderConfirmationTrigger> triggers) =>
        new()
        {
            Items = triggers
                .OrderBy(x => x.ReceivedOn == null)
                .ThenBy(o => o.ReceivedOn)
                .ToList()
        };

    public static string GetStatusColorStyle(this OrderConfirmationTrigger trigger) =>
        trigger.ReceivedOn.HasValue ? "table-success-light" : "table-primary";
}
