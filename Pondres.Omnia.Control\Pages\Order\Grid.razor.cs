using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Order;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Grid : CustomerFilterGridPageBase<OrderListItem, IOrderFilterContext>
{
    [Inject]
    private IOrderService OrderService { get; set; }

    [Parameter]
    public RenderFragment NavigationLinks { get; set; }

    public List<string> FilterFlows { get; private set; }
    private string RejectionErrorCode { get; set; }
    private string RejectionErrorReason { get; set; }

    private IEnumerable<string> SelectedUniqueCustomerReferences =>
        DataGrid.SelectedItems.Select(x => x.CustomerReference).Distinct();

    private IEnumerable<string> SelectedUniqueOrderIds =>
        DataGrid.SelectedItems.Select(x => x.OrderId).Distinct();

    private void NavigateToOrderDetails(string orderId) =>
        NavigationManager.NavigateTo($"Orders/{CurrentCustomerId}/{orderId}");

    private void OnDetailsCommand(GridCommandEventArgs e) =>
         NavigateToOrderDetails((e.Item as OrderListItem).OrderId);

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
         NavigateToOrderDetails((e.Item as OrderListItem).OrderId);

    private string SelectionCounterDisplayName =>
        DataGrid.SelectedItems.Any() ? $"({DataGrid.SelectedItems.Count()})" : string.Empty;

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as OrderListItem).Status.ToStatusColorClass();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await TryLoadFilterFlowsAsync();

        await LoadGridItemsAsync(refresh: true);
    }

    private async Task TryLoadFilterFlowsAsync()
    {
        try
        {
            FilterFlows?.Clear();
            FilterFlows = await OrderService.GetFlowsAsync(CurrentCustomerId);
        }
        catch
        {
            ToastService.ShowError($"Flows could not be loaded for customer {CurrentCustomerId}");
        }
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await TryLoadFilterFlowsAsync();

            await LoadGridItemsAsync(refresh: true);
        }
    }

    private async IAsyncEnumerable<ActionListButtonResult> CancelOrdersAsync()
    {
        foreach (var orderId in DataGrid.SelectedItems.Select(item => item.OrderId))
        {
            ActionListButtonResult result;
            try
            {
                await OrderService.CancelOrderAsync(CurrentCustomerId, Guid.Parse(orderId), CurrentUser);
                result = new ActionListButtonResult { Key = orderId, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = orderId, FailedResultMessage = ex.Message };
            }
            yield return result;
        }
    }

    private async IAsyncEnumerable<ActionListButtonResult> RejectOrdersAsync()
    {
        foreach (var orderId in DataGrid.SelectedItems.Select(item => item.OrderId))
        {
            ActionListButtonResult result;
            try
            {
                await OrderService.RejectOrderAsync(new OrderRejectCommand
                {
                    Customer = CurrentCustomerId,
                    OrderId = orderId,
                    ErrorCode = RejectionErrorCode,
                    ErrorReason = RejectionErrorReason,
                    InitiatedBy = CurrentUser
                });

                result = new ActionListButtonResult { Key = orderId, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = orderId, FailedResultMessage = ex.Message };
            }
            yield return result;
        }

        await LoadGridItemsAsync(true);
    }

    private async IAsyncEnumerable<ActionListButtonResult> RetryFailedOrderTasksAsync()
    {
        foreach (var orderId in DataGrid.SelectedItems.Select(item => item.OrderId))
        {
            ActionListButtonResult result;
            try
            {
                await OrderService.RetryFailedOrderTasksAsync(Guid.Parse(orderId));
                result = new ActionListButtonResult { Key = orderId, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = orderId, FailedResultMessage = ex.Message };
            }
            yield return result;
        }

        await LoadGridItemsAsync(true);
    }

    private async IAsyncEnumerable<ActionListButtonResult> RetryOrdersAsync()
    {
        foreach (var orderId in DataGrid.SelectedItems.Select(item => item.OrderId))
        {
            ActionListButtonResult result;
            try
            {
                await OrderService.RetryOrderAsync(CurrentCustomerId, Guid.Parse(orderId));
                result = new ActionListButtonResult { Key = orderId, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = orderId, FailedResultMessage = ex.Message };
            }
            yield return result;
        }

        await LoadGridItemsAsync(true);
    }

    private async IAsyncEnumerable<ActionListButtonResult> StartOrdersAsync()
    {
        foreach (var orderId in DataGrid.SelectedItems.Select(item => item.OrderId))
        {
            ActionListButtonResult result;
            try
            {
                await OrderService.StartOrderAsync(CurrentCustomerId, Guid.Parse(orderId));
                result = new ActionListButtonResult { Key = orderId, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = orderId, FailedResultMessage = ex.Message };
            }
            yield return result;
        }

        await LoadGridItemsAsync(true);
    }
}