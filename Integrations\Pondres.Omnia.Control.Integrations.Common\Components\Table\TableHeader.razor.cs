using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Table;

public partial class TableHeader
{
    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    [Parameter]
    public bool AutoRefresh { get; set; }

    [Parameter]
    public EventCallback Refresh { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    private async Task OnRefreshAsync()
    {
        if (Refresh.HasDelegate)
            await Refresh.InvokeAsync();
    }

    private async Task OnRefreshProgressAsync(int duration)
    {
        if (duration != 0)
            await JSRuntime.InvokeAsync<string>("countdown.start", "countdown-bar", duration);
        else
            await JSRuntime.InvokeAsync<string>("countdown.stop", "countdown-bar");
    }
}