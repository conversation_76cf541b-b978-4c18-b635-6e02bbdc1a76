﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Json.Schema;
using System.Text.Json;
using Telerik.Blazor.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using BlazorMonaco.Editor;
using Pondres.Omnia.Control.Extensions;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Configuration
{
    public partial class IntersolveConfigurationEditor : ModalBase
    {
        private string validationMessage;

        [Inject]
        IJSRuntime JS { get; set; }

        private StandaloneCodeEditor Editor { get; set; }

        private TelerikDialog Dialog { get; set; }

        public string Configuration { get; set; }

        public bool IsNew { get; set; }

        public bool IsView { get; set; }

        [Parameter]
        public EventCallback OnSaveAction { get; set; }

        [Parameter]
        public EventCallback OnCloseAction { get; set; }

        public string ValidationMessage
        {
            get => validationMessage;
            set
            {
                validationMessage = value;
                Dialog.Refresh();
            }
        }

        public void ShowDialog(string configuration, bool isView, bool isNew = false)
        {
            IsNew = isNew;
            IsView = isView;
            Configuration = configuration;
            ValidationMessage = null;

            IsVisible = true;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
                await JS.InvokeVoidAsync("monacoJsonSchema.setJsonSchema", Configuration);

            await base.OnAfterRenderAsync(firstRender);
        }

        public async Task MonacoEditorInitializedAsync()
        {
            if (Editor != null)
                await Editor.SetValue(Configuration);
        }

        public async Task SaveDialogAsync()
        {
            Configuration = await Editor.GetValue();
            var schema = JsonSchema.FromText(Configuration);
            var value = JsonSerializer.Deserialize<JsonElement>(Configuration);
            var validationResult = schema.Evaluate(value, new EvaluationOptions { RequireFormatValidation = true });

            if (!validationResult.IsValid)
                ValidationMessage = validationResult.ErrorMessage();

            if (OnSaveAction.HasDelegate)
                await OnSaveAction.InvokeAsync();
        }

        public async Task CloseDialogAsync()
        {
            if (OnCloseAction.HasDelegate)
                await OnCloseAction.InvokeAsync();

            await Editor.SetValue("{\n\n}");

            Configuration = null;
            ValidationMessage = null;

            IsVisible = false;

            await InvokeAsync(StateHasChanged);
        }

        private StandaloneEditorConstructionOptions EditorConstructionOptions(Editor editor) =>
            new()
            {
                AutomaticLayout = true,
                FixedOverflowWidgets = true,
                Language = "json",
                AutoIndent = "true",
                ReadOnly = IsView
            };
    }
}