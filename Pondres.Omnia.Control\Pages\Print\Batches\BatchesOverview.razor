﻿@page "/Print/Batches"
@using Microsoft.AspNetCore.Mvc
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Customer.Components;
@using Pondres.Omnia.Control.Integrations.Print.Client;
@using Pondres.Omnia.Control.Integrations.Print.Components.BatchOverview
@using Pondres.Omnia.Control.Integrations.Print.Models
@using Telerik.DataSource
@using Pondres.Omnia.Control.Integrations.Print.Components.Bundle
@using static Telerik.Blazor.ThemeConstants.Button;
@using Telerik.SvgIcons

@inherits CustomerFilterGridPageBase<DGCodeBatch, IPrintBatchFilterContext>

<PageHeader Title="Print batch overview" />
<PageBody>
    <PrintBundlePrintConfirmationModal @ref="PrintBundlePrintConfirmationDialog"
                                       OnSaveAction="@ConfirmPrintBatchAsync"
                                       CloseOnSubmit="false" />
    <PrintBundleWithPrintertypePrintConfirmationModal @ref="PrintBundleWithPrintertypePrintConfirmationDialog"
                                                      OnSaveAction="@ConfirmPrintBundlesAsync"
                                                      CloseOnSubmit="false" />

    <BatchGridFilter FilterChanged="OnFilterChangedAsync"></BatchGridFilter>

    <TelerikGrid Data="@GridItems"
                 @ref="DataGrid"
                 Class="grid-no-scroll"
                 EditMode="@GridEditMode.Incell">
        <GridColumns>
            <GridColumn Field="Customer" Title="Customer" Editable="false" />
            <GridColumn Field="BatchName" Title="Batch" Editable="false" />
            <GridColumn Field="DgCode" Title="DGCode" Editable="false" />
            <GridColumn Field="GordNumber" Title="GordNumber" Editable="false" />
            <GridColumn Field="Status" Title="Status" Editable="false" />
            <GridCommandColumn Context="item" Width="200px">
                @{
                    var batch = item as DGCodeBatch;
                }
                <AuthorizeView Roles="ControlOwner, PrintContributor">
                    @if (batch.Status == "WaitingForPrint")
                    {
                        <GridCommandButton Command="SetPrinted" FillMode="outline" OnClick="@OnPrintBatchConfirmationCommand" Icon="@SvgIcon.Check" ThemeColor="success" Class="TButton-50">Printed</GridCommandButton>
                    }
                </AuthorizeView>
                <AuthorizeView Roles="ControlOwner, PrintContributor, PrintReader">
                    <GridCommandButton Command="Edit" Icon="@SvgIcon.FilePdf" FillMode="outline" ThemeColor="info" Class="TButton-50">Instructions</GridCommandButton>
                    <GridCommandButton Command="OpenBatchInstructions" FillMode="outline" OnClick="@BatchInstructionsCommandAsync" Icon="@SvgIcon.FilePdf" ShowInEdit="true" Class="TButton-50" ThemeColor="info">Batch</GridCommandButton>
                    <GridCommandButton Command="OpenPrintInstructions" FillMode="outline" OnClick="@PrintInstructionsCommandAsync" Icon="@SvgIcon.FilePdf" ShowInEdit="true" Class="TButton-50" ThemeColor="info">Print</GridCommandButton>
                    <GridCommandButton Command="OpenCombinedInstructions" FillMode="outline" OnClick="@CombinedPrintInstructionsCommandAsync" Icon="@SvgIcon.FilePdf" ShowInEdit="true" Class="TButton-100" ThemeColor="info">Batch and Print</GridCommandButton>
                </AuthorizeView>
            </GridCommandColumn>
        </GridColumns>
        <DetailTemplate Context="batch">
            <TelerikGrid Data="batch.Details">
                <GridAggregates>
                    <GridAggregate Field="@nameof(DGCodeBatchDetails.Sheets)" Aggregate="@GridAggregateType.Sum" />
                    <GridAggregate Field="@nameof(DGCodeBatchDetails.DocumentCount)" Aggregate="@GridAggregateType.Sum" />
                </GridAggregates>
                <GridColumns>
                    <GridColumn Field="@nameof(DGCodeBatchDetails.PrinterType)" Title="PrinterType"></GridColumn>
                    <GridColumn Field="@nameof(DGCodeBatchDetails.PrintFileLocation)" Title="Printfile location"></GridColumn>
                    <GridColumn Field="@nameof(DGCodeBatchDetails.Sheets)" Title="Sheets">
                        <FooterTemplate>
                            Totaal: @context.Sum
                        </FooterTemplate>
                    </GridColumn>
                    <GridColumn Field="@nameof(DGCodeBatchDetails.DocumentCount)" Title="Documents">
                        <FooterTemplate>
                            Totaal: @context.Sum
                        </FooterTemplate>
                    </GridColumn>
                    <GridColumn Field="@nameof(DGCodeBatchDetails.Status)" Title="Status" TextAlign="@ColumnTextAlign.Center">
                        <Template>
                            @{
                                var batch = context as DGCodeBatchDetails;

                                if (batch.Status == "WaitingForPrint")
                                {
                                    <span class="k-badge k-badge-md k-badge-solid k-badge-solid-info k-rounded-md">Waiting for print</span>
                                }
                                else if (batch.Status == "Cancelled")
                                {
                                    <span class="k-badge k-badge-md k-badge-solid k-badge-solid-error k-rounded-md">Cancelled</span>
                                }
                                else if (batch.Status == "Skipped")
                                {
                                    <span class="k-badge k-badge-md k-badge-solid k-badge-solid-error k-rounded-md">Skipped</span>
                                }
                                else if (batch.Status == "ImpositioningOnHold")
                                {
                                    <span class="k-badge k-badge-md k-badge-solid k-badge-solid-error k-rounded-md">Impositioning on hold</span>
                                }
                                else
                                {
                                    <span class="k-badge k-badge-md k-badge-solid k-badge-solid-success k-rounded-md">Printed</span>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridCommandColumn Context="item">
                        @{
                            var batchDetails = item as DGCodeBatchDetails;
                        }
                        <AuthorizeView Roles="ControlOwner, PrintContributor">
                            @if (batchDetails.Status == "WaitingForPrint")
                            {
                                <GridCommandButton Command="SetPrinted" FillMode="outline" OnClick="@OnPrintBundleConfirmationCommand" Icon="@SvgIcon.Check" ThemeColor="success">Set Printed</GridCommandButton>
                            }
                        </AuthorizeView>
                    </GridCommandColumn>
                </GridColumns>
                <DetailTemplate Context="batchDetails">
                    <TelerikGrid Data="batchDetails.PrinterDetails">
                        <GridColumns>
                            <GridColumn Field="@nameof(Integrations.Print.Client.PrinterDetails.PrintFileName)" Title="File">
                                <Template>
                                    <a href=@((context as Integrations.Print.Client.PrinterDetails).PrintFilePath) target="_blank" class="btn-link">@((context as Integrations.Print.Client.PrinterDetails).PrintFileName)</a>
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </TelerikGrid>
                </DetailTemplate>
            </TelerikGrid>
        </DetailTemplate>
    </TelerikGrid>
</PageBody>