﻿using Pondres.Omnia.NotificationHub.Contracts.Api.Configuration;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Services;

public interface INotificationConfigurationService
{
    Task<NotificationConfigurationModel> CreateConfigurationAsync(CreateNotificationConfigurationModel model);

    Task<NotificationConfigurationModel> GetConfigurationAsync(string customer);

    Task<NotificationConfigurationModel> UpdateConfigurationAsync(UpdateNotificationConfigurationModel model);
}
