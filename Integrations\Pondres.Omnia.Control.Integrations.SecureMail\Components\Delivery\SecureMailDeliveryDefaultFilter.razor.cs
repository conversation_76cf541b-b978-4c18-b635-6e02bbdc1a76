using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery;

public partial class SecureMailDeliveryDefaultFilter
{
    [Parameter]
    public EventCallback<SecureMailDeliveryDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public SecureMailDeliveryDefaultFilterContext Filter { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    private async Task OnFilterChangedAsync()
    {
        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }
}