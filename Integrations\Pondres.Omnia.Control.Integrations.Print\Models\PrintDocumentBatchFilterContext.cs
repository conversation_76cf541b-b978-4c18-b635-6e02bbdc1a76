﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Services;
using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintDocumentBatchFilterContext : CachedFilterContext<PrintDocumentListItem, PrintDocumentBatchFilterFields>, IPrintDocumentFilterContext
{
    private readonly IPrintService printService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public PrintDocumentBatchFilterContext(
        NavigationManager navigationManager,
        IPrintService printService,
        IFilterCacheService filterCacheService)
        : base(navigationManager, filterCacheService)
    {
        this.printService = printService;
    }

    protected override void NavigateToFilter(string filterId) =>
        navigationManager.NavigateToPrintDocumentListWithBatchFilter(filterId);

    protected override async Task<PagedResultModel<PrintDocumentListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await printService.GetAllDocumentsByBatchFilterAsync(Fields.ToApiFilter(nextContinuationToken));
}
