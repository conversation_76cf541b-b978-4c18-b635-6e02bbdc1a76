﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using static Telerik.Blazor.ThemeConstants.Button;

<ActionModal @ref="RetryOutputModal"
             T="NotificationTraceOutput"
             Context="task"
             Title="Retry notification output?"
             ButtonText="Retry"
             ButtonType="@ThemeColor.Primary"
             OnSubmit="RetryOutputAsync">
    You are about to retry output <b>@task.DefinitionName</b>, Continue?
</ActionModal>

<ActionModal @ref="CancelOutputModal"
             T="NotificationTraceOutput"
             Context="task"
             Title="Cancel notification output?"
             ButtonText="Cancel"
             ButtonType="danger"
             OnSubmit="CancelOutputAsync">
    You are about to cancel output <b>@task.DefinitionName</b>, Continue?
</ActionModal>

<GridCard Title="Notification Outputs"
          GetRawColorFunc="(output) => output.GetOutputStatusColorStyle()"
          GetItemsAsync="(_) => Task.FromResult(Outputs.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Definition</th>
        <th>Status</th>
        <th>Last state update</th>
        <th>Actions</th>
    </TableHeaderContent>
    <TableRowContent Context="output">
        <td><b>@output.DefinitionType</b></td>
        <td>@output.StateTypeName (@output.ResultTypeName)</td>
        <td>
            @if (output.StateUpdatedOn.HasValue)
            {
                @output.StateUpdatedOn.Value.LocalDateTime
            }
        </td>
        <td>
            <AuthorizeView Roles="ControlContributor, ControlOwner">
                @if (output.HasActionOfType(NotificationTraceOutputAction.Retry))
                {
                    <button class="btn btn-primary float-right" @onclick="@(() => RetryOutputModal.OpenModal(output))" @onclick:stopPropagation="true">Retry</button>
                }
                @if (output.HasActionOfType(NotificationTraceOutputAction.Cancel))
                {
                    <button class="btn btn-warning float-right" @onclick="@(() => CancelOutputModal.OpenModal(output))" @onclick:stopPropagation="true">Cancel</button>
                }
            </AuthorizeView>
        </td>
    </TableRowContent>
    <TableRowExpandableContent Context="output">
        @foreach (var filePath in output.FilePaths)
        {
            <div class="d-flex justify-content-between mb-2">
                @filePath
                <span>
                    <button class="btn btn-primary" @onclick="@(async () => await OnFileOpened.InvokeAsync(filePath))">View</button>
                    <button class="btn btn-primary" @onclick="@(async () => await OnFileDownloaded.InvokeAsync(filePath))">Download</button>
                </span>
            </div>
        }
    </TableRowExpandableContent>
</GridCard>