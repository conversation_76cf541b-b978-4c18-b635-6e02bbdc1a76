using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Document;

public partial class DocumentsDefaultFilter
{
    [Parameter]
    public EventCallback<PrintDocumentDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public PrintDocumentDefaultFilterContext Filter { get; set; }

    private async void OnFilterChanged()
    {
        await FilterChanged.InvokeAsync(Filter);
    }
}
