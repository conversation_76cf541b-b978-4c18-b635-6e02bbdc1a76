﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning;

public record BundleScanningRow(string Batch, string FileName, string Format, string PostalDistributor, int Count, bool IsScanned, MapPrintBundleState Status, string BundleId, bool IsCompleted);

public partial class ProductionScanningGrid
{
    private List<BundleScanningRow> Rows { get; set; } = [];

    [CascadingParameter]
    public Dictionary<string, PrintBundleWithDetails> Bundles
    {
        set
        {
            Rows = GetRowsFromBundles(value);
        }
    }

    [Parameter]
    public EventCallback<string> OnBundleComplete { get; set; }

    [Parameter]
    public EventCallback<string> OnBundleScanned { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    public AuthenticationStateProvider AuthenticationStateProvider { get; set; }

    private static List<BundleScanningRow> GetRowsFromBundles(Dictionary<string, PrintBundleWithDetails> value)
    {
        var result = new List<BundleScanningRow>();

        foreach (var bundle in value.Values)
        {
            result.Add(new BundleScanningRow
                (
                    Batch: bundle.PrintBundle.BatchName,
                    FileName: string.Join(",", bundle.PrintBundle.Files.OrderBy(c => c).Distinct().ToList()),
                    Format: bundle.PrintBundle.Metadata.DocumentFormat,
                    bundle.PrintBundle.Metadata.Carrier,
                    Count: bundle.PrintBundle.Metadata.Quantity,
                    IsScanned: bundle.PrintBundle.Status.Status == MapPrintBundleState.Scanned || bundle.PrintBundleDetails.StatusHistory.Any(c => c.Status == MapPrintBundleState.Scanned),
                    Status: bundle.PrintBundle.Status.Status,
                    BundleId: bundle.PrintBundle.Id,
                    IsCompleted: bundle.PrintBundle.Status.Status == MapPrintBundleState.Completed
                    )
            );
        }

        return result.OrderBy(c => c.FileName).ToList();
    }

    [Parameter]
    public bool Loading { get; set; }

    private static void OnRowRender(GridRowRenderEventArgs args)
    {
        if (args.Item is BundleScanningRow row)
        {
            if (!row.IsCompleted && !row.IsScanned)
            {
                args.Class += "table-greyed-out";
            }
            else if (row.IsCompleted && row.IsScanned)
            {
                args.Class += "table-success-light";

            }
            else
            {
                args.Class += "table-warning";
            }
        }
    }

    private async Task OnBundleCompleteClickedAsync(GridCommandEventArgs obj)
    {

        try
        {
            var row = obj.Item as BundleScanningRow;

            if (OnBundleComplete.HasDelegate)
            {
                await OnBundleComplete.InvokeAsync(row!.BundleId);
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }

    private async Task OnBundleScannedClickedAsync(GridCommandEventArgs obj)
    {
        try
        {
            var row = obj.Item as BundleScanningRow;

            if (OnBundleScanned.HasDelegate)
            {
                await OnBundleScanned.InvokeAsync(row!.BundleId);
            }
        }
        catch (Exception e)
        {
            ToastService.ShowError(e.Message);
        }
    }
}
