using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Order;

public partial class OrderBatchFilter
{
    private EditContext EditContext;

    [Parameter]
    public OrderBatchFilterContext Filter { get; set; }

    [Parameter]
    public EventCallback<OrderBatchFilterContext> FilterChanged { get; set; }

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
        EditContext.OnFieldChanged += OnFormFieldChanged;
        base.OnInitialized();
    }

    private async void OnFormFieldChanged(object sender, FieldChangedEventArgs e)
    {
        EditContext.MarkAsUnmodified();

        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }

    private async Task ClearFilterAsync()
    {
        await Filter.ResetAsync();

        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }
}