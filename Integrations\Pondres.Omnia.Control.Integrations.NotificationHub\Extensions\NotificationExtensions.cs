﻿using Pondres.Omnia.NotificationHub.Contracts.Api.Definition;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class NotificationExtensions
{
    public static UpsertNotificationDefinitionModel ToUpsertModel(this NotificationDefinitionModel definition, string definitionJson, DateTimeOffset? failSilentlyUntil) =>
        new()
        {
            Customer = definition.Customer,
            Name = definition.Id,
            Type = definition.Type,
            ETag = definition.ETag,
            DefinitionJson = definitionJson,
            FailSilentlyUntil = failSilentlyUntil
        };
}
