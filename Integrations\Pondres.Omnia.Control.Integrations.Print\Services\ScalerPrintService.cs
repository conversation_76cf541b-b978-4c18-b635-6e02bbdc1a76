﻿using Microsoft.Extensions.Configuration;
using Pondres.Omnia.Control.Integrations.Common.Services;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Pondres.Omnia.Control.Integrations.Print.Services
{
    public class ScalerPrintService : BaseApiService, IScalerPrintService
    {
        private readonly IConfiguration configuration;
        private readonly JsonSerializerOptions serializerOptions;

        public ScalerPrintService(HttpClient httpClient, IConfiguration configuration) : base(httpClient)
        {
            this.configuration = configuration;

            serializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            serializerOptions.Converters.Add(new JsonStringEnumConverter());
        }

        public async Task<Stream> GetPrintInstructionsPDFAsync(List<PrintBundleForInstructions> printBundlesForInstructions)
        {
            var baseUrl = configuration["ScalerPrintInstructionsUri"];
            return await GetInstructionsByUrlAsync(printBundlesForInstructions, baseUrl + "/rest/api/submit-job/printInstruction");
        }

        public async Task<Stream> GetBatchInstructionsPDFAsync(List<PrintBundleForInstructions> printBundlesForInstructions)
        {
            var baseUrl = configuration["ScalerPrintInstructionsUri"];
            return await GetInstructionsByUrlAsync(printBundlesForInstructions, baseUrl + "/rest/api/submit-job/batchInstructions");
        }

        public async Task<Stream> GetCombinedPrintInstructionsPDFAsync(List<PrintBundleForInstructions> printBundlesForInstructions)
        {
            var baseUrl = configuration["ScalerPrintInstructionsUri"];
            return await GetInstructionsByUrlAsync(printBundlesForInstructions, baseUrl + "/rest/api/submit-job/combinedPrintInstructions");
        }

        private async Task<Stream> GetInstructionsByUrlAsync(List<PrintBundleForInstructions> printBundlesForInstructions, string url)
        {
            var base64 = await PostWithStringResultAsync(printBundlesForInstructions, url, configuration["ScalerPrintInstructionsAuthToken"], serializerOptions);
            var byteArray = Convert.FromBase64String(base64);

            var stream = new MemoryStream(byteArray);

            return stream;
        }
    }
}
