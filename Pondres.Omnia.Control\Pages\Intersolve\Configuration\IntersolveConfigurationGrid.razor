﻿@page "/Configuration/Intersolve"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Intersolve.Contracts.Configuration
@using Pondres.Omnia.Control.Integrations.Intersolve.Components.Configuration
@using ConfigurationDto = Pondres.Omnia.Intersolve.Contracts.Configuration.Configuration;

@inherits SelectableGridBase<ConfigurationDto>

<IntersolveConfigurationEditor @ref="DialogEditor" OnSaveAction="@OnSaveActionAsync" />

<PageHeader Title="Intersolve Configuration" />
<PageBody>
    <AuthorizeView Roles="ControlOwner">
        <TelerikCard>
            <CardHeader>
                <CardTitle>
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Configurations</h6>
                    <div class="float-right">
                        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Success)" OnClick="@OnAddCommand">Add Configuration</TelerikButton>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardBody>
                <TelerikGrid Data="@Configurations" @ref="DataGrid" Class="grid-no-scroll"
                             SelectionMode="GridSelectionMode.Multiple">
                    <GridColumns>
                        <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" />
                        <SelectableGridColumn Field="Customer" Title="Customer" SelectionChanged="@OnColumnSelect" />
                        <GridCommandColumn Context="configuration">
                            <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="Edit" OnClick="@EditCommandAsync">Edit</GridCommandButton>
                            <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="Disable" OnClick="@OnViewCommandAsync">View</GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            </CardBody>
        </TelerikCard>
    </AuthorizeView>
</PageBody>