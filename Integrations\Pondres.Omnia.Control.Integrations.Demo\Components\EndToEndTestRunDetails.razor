﻿@using Pondres.Omnia.Demo.Contracts.Api;
@using Pondres.Omnia.Demo.Contracts.EndToEnd;
@using Telerik.Blazor.Components
@using Newtonsoft.Json
@if (Details == null)
{
    <LoadingSpinner></LoadingSpinner>
}
else
{
    <TelerikGrid Data="@Details.Results.OrderBy(x => x.Name)">
        <DetailTemplate>
            <TelerikGrid Data="@context.Logs.OrderBy(x => x.LastReceived)">
                <GridColumns>
                    <GridColumn Field="@nameof(TestResultLog.LastReceived)"></GridColumn>
                    <GridColumn Field="@nameof(TestResultLog.Amount)"></GridColumn>
                    <GridColumn Field="@nameof(TestResultLog.Message)"></GridColumn>
                </GridColumns>
            </TelerikGrid>

            <pre>@JsonConvert.SerializeObject(JsonConvert.DeserializeObject(context.RawResultData), Formatting.Indented)</pre>
        </DetailTemplate>
        <GridColumns>
            <GridColumn OnCellRender="ApplyBackground" Field="@nameof(TestResult.Name)"></GridColumn>
            <GridColumn OnCellRender="ApplyBackground" Field="@nameof(TestResult.Message)"></GridColumn>
            <GridColumn OnCellRender="ApplyBackground" Field="@nameof(TestResult.Success)"></GridColumn>
            <GridColumn OnCellRender="ApplyBackground" Field="@nameof(TestResult.StartDate)"></GridColumn>
            <GridColumn OnCellRender="ApplyBackground" Field="@nameof(TestResult.EndDate)"></GridColumn>
        </GridColumns>
    </TelerikGrid>
}
