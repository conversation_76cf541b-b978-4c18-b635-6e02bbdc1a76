﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Customer.Contracts.Shared;

namespace Pondres.Omnia.Control.Integrations.Customer.Extensions;

public static class TemporaryFileDataExtensions
{
    public static FileModel ToFileModel(this TemporaryFileData fileResult, string content) =>
        new()
        {
            FileName = fileResult.FileName,
            FilePath = fileResult.FilePath,
            Contents = content
        };
}
