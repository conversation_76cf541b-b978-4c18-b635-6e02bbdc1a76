﻿using Pondres.Omnia.Control.Integrations.ESafe.Models;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Extensions;

public static class ESafeDeliveryBatchFilterExtensions
{
    public static DeliveryBatchSelectionFilter ToApiFilter(this ESafeDeliveryBatchFilterFields fields, string continuationToken, string currentCustomer) => new()
    {
        Customer = currentCustomer,
        ContinuationToken = continuationToken,
        CustomerReferences = fields.CustomerReferences,
        MaxPageSize = fields.PageSize,
        Connectors = fields.Connectors
    };
}
