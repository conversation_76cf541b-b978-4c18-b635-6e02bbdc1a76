﻿@inherits InputSelect<int>

<ListFilterRow>
    <div class="container-fluid px-0">

        @if (ChildContent != null)
            @ChildContent

        <div class="float-right">

            <label class="col-form-label">Page size</label>

            <select @bind="CurrentValueAsString" @bind:event="onchange" class="form-control" style="width: unset; display: unset;">
                @foreach (var pageSize in ControlConstants.PageSizes)
                {
                    <option value="@pageSize">@pageSize</option>
                }
            </select>

            <button class="btn btn-outline-primary ml-1" @onclick="ClearFilterAsync"><i class="oi oi-reload mr-2"></i>Reset</button>
        </div>
    </div>
</ListFilterRow>


