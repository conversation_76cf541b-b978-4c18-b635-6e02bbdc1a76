﻿@page "/ESafe/Deliveries"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Link
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.ESafe.Components.Delivery
@using Pondres.Omnia.Control.Integrations.ESafe.Extensions
@using Pondres.Omnia.Control.Integrations.ESafe.Models
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.ESafe.Contracts.Api.Delivery
@using Telerik.SvgIcons

@inherits CustomerFilterGridPageBase<DeliveryListItem, IESafeDeliveryFilterContext>

<PageHeader Title="E-Safe Deliveries">
    <CustomerSelection CustomerChanged="@OnCustomerChangedAsync" SelectedCustomerId="@CurrentCustomerId" Customers="Customers" />
</PageHeader>
<PageBody>
    <ESafeDeliveryGridFilter FilterChanged="OnFilterChangedAsync" Flows="@FilterFlows" />

    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <AuthorizeView Roles="ControlContributor, ControlOwner">
            <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
                           Enabled="HasSelectedItems"
                           OnClick="CopySelectedESafeConnectorsToClipboardAsync">
                Copy connectors
            </TelerikButton>
        </AuthorizeView>
        <GotoDropdown Disabled="!HasSelectedItems" StyleClass="float-right mx-1">
            <BatchOrderLink CustomerReferences="@DataGrid.SelectedItems.Select(x => x.CustomerReference).Distinct()" StyleClass="dropdown-item">Orders</BatchOrderLink>
        </GotoDropdown>
    </TableHeader>

    <TelerikGrid Data="@GridItems" @ref="DataGrid" Class="grid-no-scroll"
                 SelectedItemsChanged="@(async (IEnumerable<DeliveryListItem> deliveries) => await InvokeAsync(StateHasChanged))"
                 OnRowDoubleClick="@OnRowDoubleClick"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="ESafeDeliveries" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.Id))" Title="Id" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.CustomerReference))" Title="Customer reference" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.StateTypeName))" Title="Status" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    @((context as DeliveryListItem).StateType.GetStatusString((context as DeliveryListItem).ResultType, (context as DeliveryListItem).TrackVaultStatus))
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.Connector))" Title="Connector" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.Flow))" Title="Flow" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <GridCommandColumn Width="90px" OnCellRender="@ApplyBackground">
                <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand">Details</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>
</PageBody>