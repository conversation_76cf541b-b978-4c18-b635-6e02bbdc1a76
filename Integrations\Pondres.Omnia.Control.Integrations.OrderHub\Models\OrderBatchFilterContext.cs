﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Models;

public class OrderBatchFilterContext : CachedFilterContext<OrderListItem, OrderBatchFilterFields>, IOrderFilterContext
{
    private readonly IOrderService orderService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public OrderBatchFilterContext(
        NavigationManager navigationManager,
        IOrderService orderService,
        IFilterCacheService filterCacheService)
        : base(navigationManager, filterCacheService)
    {
        this.orderService = orderService;
    }

    protected override async Task<PagedResultModel<OrderListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await orderService.GetOrdersByBatchFilterAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter(string filterId) =>
         navigationManager.NavigateToOrderListWithFilter(FilterType.Batch, FilterHelper.GetFilterIdQueryParameter(filterId));
}
