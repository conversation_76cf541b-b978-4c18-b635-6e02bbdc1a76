﻿using Json.Schema;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class NotificationDefinitionExtensions
{
    public static EvaluationResults ValidateJsonWithSchema(this string jsonValue, string jsonSchema)
    {
        try
        {
            var schema = JsonSchema.FromText(jsonSchema);
            var value = JsonSerializer.Deserialize<JsonElement>(jsonValue);
            var validationResult = schema.Evaluate(value, new EvaluationOptions { RequireFormatValidation = true, OutputFormat = OutputFormat.List });

            return validationResult;
        }
        catch (Exception exception)
        {
            throw new JsonException("JSON could not be parsed.", exception);
        }
    }
}