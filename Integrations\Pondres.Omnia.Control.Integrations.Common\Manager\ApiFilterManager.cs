﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Common.Manager;

public class ApiFilterManager : IApiFilterManager
{
    private readonly NavigationManager navigationManager;

    private List<IFilterContext> filters;

    public IFilterContext CurrentFilter { get; private set; }

    public ApiFilterManager(NavigationManager navigationManager)
    {
        this.navigationManager = navigationManager;
    }

    public void SetFilters(params IFilterContext[] filters) => this.filters = filters.ToList();

    public async Task SetInitialFilterFromUriAsync()
    {
        var filterType = FilterHelper.GetFilterTypeFromUri(navigationManager.ToAbsoluteUri(navigationManager.Uri));

        await UpdateCurrentFilterFromTypeAsync(filterType);
    }

    public async Task UpdateCurrentFilterAsync(
        IFilterContext filter,
        bool initial,
        bool save)
    {
        CurrentFilter = filter;

        if (initial)
            await CurrentFilter.MergeFromCurrentUriAsync();

        if (save)
            await CurrentFilter.NavigateToFilterAsync();
    }

    private async Task UpdateCurrentFilterFromTypeAsync(string filterType)
    {
        if (filters.Any())
        {
            var filter = GetFilterByType(filterType);

            await UpdateCurrentFilterAsync(filter, initial: true, save: false);
        }
    }

    private IFilterContext GetFilterByType(string filterType)
    {
        // Warning: this is to have backwards compatibility with when we had just 1 filter,
        // make sure the first filter is always the "old filter"
        if (string.IsNullOrEmpty(filterType))
            return filters.First();

        return filters.Single(x => x.FilterTypeName == filterType);
    }
}
