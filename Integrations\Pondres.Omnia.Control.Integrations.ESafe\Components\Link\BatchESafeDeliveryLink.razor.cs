﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.ESafe.Extensions;
using Pondres.Omnia.Control.Integrations.ESafe.Models;

namespace Pondres.Omnia.Control.Integrations.ESafe.Components.Link;

public partial class BatchESafeDeliveryLink
{
    [Inject]
    public IFilterCacheService FilterCacheService { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    [Parameter]
    public string StyleClass { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public IEnumerable<string> CustomerReferences { get; set; } = new List<string>();

    private async Task NavigateAsync()
    {
        var filterFields = new ESafeDeliveryBatchFilterFields { CustomerReferences = CustomerReferences.ToList() };
        var filterId = await FilterCacheService.SaveFilterFieldsAsync(filterFields);
        NavigationManager.NavigateToESafeDeliveryListWithBatchFilter(filterId);
    }
}
