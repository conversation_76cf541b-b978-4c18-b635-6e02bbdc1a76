﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.ESafe.Models;
using Pondres.Omnia.Control.Integrations.ESafe.Services;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Pages.ESafe;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Details
{
    [Inject]
    private IESafeDeliveryService Service { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public string DeliveryId { get; set; }

    private DeliveryDetails Delivery { get; set; }

    private bool PageLoading { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await RefreshPageAsync();
    }

    private async Task RefreshPageAsync()
    {
        PageLoading = true;

        try
        {
            Delivery = await Service.GetDetailsAsync(Customer, Guid.Parse(DeliveryId));
            PageLoading = false;
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    private List<ESafeDeliveryTimeoutModel> CreateDeliveryTimeoutModels()
    {
        var result = new List<ESafeDeliveryTimeoutModel>();

        for (var i = 0; i < Math.Max(Delivery.ExpectedTimeouts.Count, Delivery.ReceivedTimeouts.Count); i++)
        {
            var expectedTime = GetItemAtIndexOrNull(Delivery.ExpectedTimeouts, i);
            var receivedTime = GetItemAtIndexOrNull(Delivery.ExpectedTimeouts, i);

            result.Add(new ESafeDeliveryTimeoutModel { ExpectedOn = expectedTime, ReceivedOn = receivedTime });
        }

        return result;
    }

    private static DateTimeOffset? GetItemAtIndexOrNull(List<DateTimeOffset> items, int index) =>
        index < items.Count ? items[index] : null;
}
