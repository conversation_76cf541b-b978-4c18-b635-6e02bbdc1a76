﻿using Pondres.Omnia.Control.Integrations.Customer.Models;

namespace Pondres.Omnia.Control.Integrations.Customer.Services;

public interface ICustomerService
{
    Task<List<CustomerModel>> GetAllCustomersAsync();

    Task<CustomerDetailsModel> GetCustomerDetailsAsync(string customerId);

    Task CreateCustomerAsync(CustomerDetailsModel model);

    Task UpdateCustomerAsync(CustomerDetailsModel model);

    Task DisableCustomerAsync(string customerId);
}