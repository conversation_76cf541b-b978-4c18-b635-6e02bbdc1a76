﻿@page "/Intersolve/Orders"

@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Intersolve.Client
@using Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
@using Pondres.Omnia.Control.Integrations.Intersolve.Extensions

@inherits CustomerFilterGridPageBase<IntersolveOrderListItem, IIntersolveOrderFilterContext>

<PageHeader Title="Audit Traces">
    <CustomerSelection Customers="@Customers" SelectedCustomerId="@CurrentCustomerId" CustomerChanged="OnCustomerChangedAsync" />
</PageHeader>
<PageBody>
    <div class="container-fluid">
        <IntersolveOrderGridFilter FilterChanged="OnFilterChangedAsync" />

        <IntersolveOrderGrid Orders="GridItems" Loading="LoadingGridItems" OnRefresh="OnRefresh" CustomerId="@CurrentCustomerId" />
    </div>
</PageBody>