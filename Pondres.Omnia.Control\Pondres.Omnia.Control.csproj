﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>bf0b0c82-3112-473f-af3b-3be4e389abc1</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Authentication\**" />
		<Content Remove="Authentication\**" />
		<EmbeddedResource Remove="Authentication\**" />
		<None Remove="Authentication\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
		<PackageReference Include="Azure.Extensions.AspNetCore.DataProtection.Blobs" Version="1.4.0" />
		<PackageReference Include="Azure.Identity" Version="1.13.2" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.22.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.AzureAD.UI" Version="6.0.36" />
		<PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="9.0.1" />
		<PackageReference Include="Microsoft.Azure.SignalR" Version="1.29.0" />
		<PackageReference Include="Microsoft.Identity.Web" Version="3.6.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Pondres.Common" Version="3.20221025.2" />
		<PackageReference Include="Pondres.Omnia.NotificationHub.Contracts" Version="1.20250103.1" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Telerik.UI.for.Blazor" Version="5.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Customer\Pondres.Omnia.Control.Integrations.Customer.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Demo\Pondres.Omnia.Control.Integrations.Demo.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Email\Pondres.Omnia.Control.Integrations.Email.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.ESafe\Pondres.Omnia.Control.Integrations.ESafe.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Intersolve\Pondres.Omnia.Control.Integrations.Intersolve.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.NotificationHub\Pondres.Omnia.Control.Integrations.NotificationHub.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.OpsGenie\Pondres.Omnia.Control.Integrations.OpsGenie.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.OrderHub\Pondres.Omnia.Control.Integrations.OrderHub.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Print\Pondres.Omnia.Control.Integrations.Print.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.SecureMail\Pondres.Omnia.Control.Integrations.SecureMail.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Warehouse\Pondres.Omnia.Control.Integrations.Warehouse.csproj" />
		<ProjectReference Include="..\Integrations\Pondres.Omnia.Control.Integrations.Web2Omnia\Pondres.Omnia.Control.Integrations.Web2Omnia.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Control.Common\Pondres.Omnia.Control.Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Pages\SecureMail\" />
	</ItemGroup>

</Project>
