﻿window.infiniteScroll = {
    register: function (ObjRef) {
        window.onscroll = function () {
            var scrollHeight = document.body.scrollHeight;
            var totalHeight = window.scrollY + window.innerHeight;

            if (totalHeight >= scrollHeight) {
                ObjRef.invokeMethodAsync("ScrollHandlerAsync");
            }
        }
    },
    dispose: function () {
        window.onscroll = undefined;
    }
}

