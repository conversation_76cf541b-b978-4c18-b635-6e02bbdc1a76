﻿using Azure.Identity;
using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Azure.SignalR;
using Pondres.Omnia.Control.Configuration;
using Pondres.Omnia.Control.Integrations.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Provicers;
using Pondres.Omnia.Control.Integrations.Customer;
using Pondres.Omnia.Control.Integrations.Email;
using Pondres.Omnia.Control.Integrations.ESafe;
using Pondres.Omnia.Control.Integrations.NotificationHub;
using Pondres.Omnia.Control.Integrations.OpsGenie;
using Pondres.Omnia.Control.Integrations.OrderHub;
using Pondres.Omnia.Control.Integrations.Demo;
using Pondres.Omnia.Control.Integrations.Print;
using Pondres.Omnia.Control.Integrations.SecureMail;
using Pondres.Omnia.Control.Integrations.Warehouse;
using Pondres.Omnia.Control.ViewModels.Batches;
using Pondres.Omnia.Control.ViewModels.Schedules;
using Serilog;
using Pondres.Omnia.Control.Integrations.Web2Omnia;
using Pondres.Omnia.Control.Settings;
using Pondres.Omnia.Control.Integrations.Intersolve;
using Azure.Storage.Blobs;
using Azure.Core;

namespace Pondres.Omnia.Control.Extensions;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddApplicationInsights(this IServiceCollection services, AppSettings appSettings) =>
        services
            .AddApplicationInsightsTelemetry(config => config.ConnectionString = appSettings.AppInsightsConnectionString)
            .AddSingleton<ITelemetryInitializer, AppInsightsTelemetryInitializer>()
            .ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, config) =>
            {
                config.EnableDependencyTrackingTelemetryModule = false;
                module.IncludeDiagnosticSourceActivities.Add("MassTransit");
            });

    public static IServiceCollection AddCache(this IServiceCollection services, AppSettings appSettings) =>
        services
            .AddSingleton<IDistributedCacheProvider, DistributedCacheProvider>()
            .AddStackExchangeRedisCache(options => options.Configuration = appSettings.RedisConnectionString);

    public static IServiceCollection AddAzureSignalRService(this IServiceCollection services, AppSettings appSettings)
    {
        if (string.IsNullOrWhiteSpace(appSettings.AzureSignalRConnectionString))
        {
            Log.Warning("Not adding Azure SignalR Service because 'AzureSignalRConnectionString' is not set");
            return services;
        }

        services.AddSignalR().AddAzureSignalR(options =>
        {
            options.ConnectionString = appSettings.AzureSignalRConnectionString;
            options.ServerStickyMode = ServerStickyMode.Required;
        });

        return services;
    }

    public static IServiceCollection AddIntegrations(this IServiceCollection services, AppSettings appSettings) => services
        .AddOrderHubServices(appSettings)
        .AddEmailServices(appSettings)
        .AddESafeServices(appSettings)
        .AddCommonServices(appSettings)
        .AddCustomerServices(appSettings)
        .AddOpsGenieServices(appSettings)
        .AddNotificationHubServices(appSettings)
        .AddPrintServices(appSettings)
        .AddDemoServices(appSettings)
        .AddSecureMailServices(appSettings)
        .AddWarehouseServices(appSettings)
        .AddWeb2OmniaServices(appSettings)
        .AddIntersolveServices(appSettings);

    public static IServiceCollection AddDataProtection(this IServiceCollection services, AppSettings appSettings)
    {
        if (string.IsNullOrWhiteSpace(appSettings.SessionStorageAccountName))
        {
            Log.Warning("Not persisting keys to azure storage because 'SessionStorageAccountName' is not set");
            return services;
        }

        Log.Information("Persisting keys to azure storage");

        var blobUri = $"https://{appSettings.SessionStorageAccountName}.blob.core.windows.net";
        var containerName = "keys";

        var options = new BlobClientOptions
        {
            Retry =
            {
                MaxRetries = 3,
                Mode = RetryMode.Exponential
            }
        };

        var blobClient = new BlobServiceClient(
            serviceUri: new Uri(blobUri),
            credential: new DefaultAzureCredential(),
        options: options);

        var containerClient = blobClient.GetBlobContainerClient(containerName);
        containerClient.CreateIfNotExists();

        services
            .AddDataProtection()
            .PersistKeysToAzureBlobStorage(
                blobUri: new Uri($"{blobUri}/{containerName}/keys.xml"),
                tokenCredential: new DefaultAzureCredential());

        return services;
    }

    public static IServiceCollection ConfigureOmniaEnvironmentSettings(this IServiceCollection services, AppSettings appSettings) => services
        .AddSingleton(x => new EnvironmentSettings
        {
            OmniaEnvironment = appSettings.OmniaEnvironment,
            PrintFolderPath = appSettings.PrintFolderPath
        });

    public static IServiceCollection AddViewModels(this IServiceCollection services)
    {
        services.AddTransient<IBatchesOverviewViewModel, BatchesOverviewViewModel>();
        services.AddTransient<ISchedulesOverviewViewModel, SchedulesOverviewViewModel>();

        return services;
    }
}
