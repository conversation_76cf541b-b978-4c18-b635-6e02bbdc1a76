#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base

ENV TZ "Europe/Amsterdam"

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV LC_ALL=nl_NL.UTF-8
ENV LANG=nl_NL.UTF-8

WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

## Start: Authenticate with custom feed ##
ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS \
    "{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/pondresnl/Omnia/_packaging/Omnia/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | bash
## End: Authenticate with custom feed ##

WORKDIR /src
COPY ["Pondres.Omnia.Control/Pondres.Omnia.Control.csproj", "Pondres.Omnia.Control/"]
COPY ./nuget.config .
RUN dotnet restore "Pondres.Omnia.Control/Pondres.Omnia.Control.csproj"
COPY . .
WORKDIR "/src/Pondres.Omnia.Control"
RUN dotnet build "Pondres.Omnia.Control.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Pondres.Omnia.Control.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Pondres.Omnia.Control.dll"]