﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Buttons
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Warehouse.Contracts.Response
@using static Telerik.Blazor.ThemeConstants.Button
@using Telerik.SvgIcons

@inherits SelectableGridBase<WarehouseResponseListItem>

<div class="card shadow mb-4">
    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <AuthorizeView Roles="ControlContributor, ControlOwner">            
            <ActionListButton Disabled="@(!HasSelectedItems)" Title="Reprocess file?" 
                              ButtonText="Reprocess" ThemeColor="@ThemeColor.Primary" 
                              StyleClass="float-right" Actions="ReprocessFilesAsync" OnCompletion="OnRefresh">
                You are about to reprocess <b>@DataGrid.SelectedItems.Count()</b> file(s), continue?
            </ActionListButton>            
        </AuthorizeView>
    </TableHeader>
    
    <TelerikGrid Data="@Responses" @ref="DataGrid" Class="grid-no-scroll"
                 OnRowDoubleClick="@OnRowDoubleClick"
                 SelectedItemsChanged="@(async (IEnumerable<WarehouseResponseListItem> items) => await InvokeAsync(StateHasChanged))"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="Responses" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />

            <SelectableGridColumn Field="@(nameof(WarehouseResponseListItem.FileName))" Title="File name" Width="600px" 
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(WarehouseResponseListItem.MessageType))" Title="Message Type" 
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(WarehouseResponseListItem.StateTypeName))" Title="Completed" 
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    @((context as WarehouseResponseListItem).StateTypeName) (@((context as WarehouseResponseListItem).ResultTypeName))
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@(nameof(WarehouseResponseListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(WarehouseResponseListItem.UpdatedOn))" Title="Updated On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        </GridColumns>
    </TelerikGrid>
</div>