﻿@page "/"
@using Pondres.Omnia.Control.Integrations.OpsGenie.Components

<PageHeader Title="Dashboard">
    <h4 class="mb-0 col-5">        
        @if (IsLoading)
        {
            <span class="spinner-border text-primary" style="display:inline-block;height:25px;width:25px;font-weight:100;" />
        }
    </h4>
</PageHeader>
<PageBody>
    <AuthorizeView Roles="ControlReader, ControlContributor, ControlOwner, ControlDataReader">
        @if (OpsGenieFailed)
        {
            <div class="alert alert-danger" role="alert">
                Could not retrieve OpsGenie Alerts.
            </div>
        }
        else
        {
            <div class="container-fluid">
                <AlertGroup Alerts="CustomerAlerts" Title="Customer Alerts"></AlertGroup>

                <AlertGroup Alerts="PlatformAlerts" Title="Platform Alerts"></AlertGroup>
            </div>
        }
    </AuthorizeView>
</PageBody>