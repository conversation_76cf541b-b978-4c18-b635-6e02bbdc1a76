﻿@using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Last status
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table">
            <tbody>
                <tr>
                    <th>@nameof(Document.LastStatus.ResponseFile)</th>
                    <td>@Document.LastStatus?.ResponseFile</td>
                </tr>
                <tr>
                    <th>@nameof(Document.LastStatus.StatusCode)</th>
                    <td>@Document.LastStatus?.StatusCode</td>
                </tr>
                <tr>
                    <th>@nameof(Document.LastStatus.StatusName)</th>
                    <td>@Document.LastStatus?.StatusName</td>
                </tr>
                <tr>
                    <th>@nameof(Document.LastStatus.Timestamp)</th>
                    <td>@Document.LastStatus?.Timestamp.LocalDateTime</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>

