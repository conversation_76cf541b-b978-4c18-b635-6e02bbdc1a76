﻿@page "/Orders/{Customer}/{OrderIdName}"

@using Pondres.Omnia.Control.Integrations.Common.Components
@using Pondres.Omnia.Control.Integrations.ESafe.Components.Delivery
@using Pondres.Omnia.Control.Integrations.Email.Components.Delivery
@using Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
@using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Order
@using Pondres.Omnia.Control.Integrations.Print.Components.Document
@using Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Document
@using Pondres.Omnia.OrderHub.Contracts.Api.Order

@inherits ControlPageBase

<PageHeader Title="@($"Order {OrderId}")" />
<PageBody>
    <div class="container-fluid row">
        <LoadingSpinner Show="PageLoading"></LoadingSpinner>
        @if (!PageLoading && Order == null)
        {
            <div class="alert alert-danger" role="alert">
                Could not retrieve order data for "@OrderId"
            </div>
        }
        else if(!PageLoading)
        {

            <div class="col-xl-6 col-12">

                <!-- Summary panel-->
                <OrderSummary Order="Order" OnActionHandled="RefreshPageAsync" CurrentUser="@CurrentUser" />

                <OrderConfirmationTriggers Order="Order" />
                
                <OrderRejectionTriggers Order="Order" />

                <!-- Files -->
                <AuthorizeView Roles="ControlContributor, ControlOwner, ControlDataReader">
                    <Files Order="Order" CurrentUser="@CurrentUser" />
                </AuthorizeView>

            </div>

            <div class="col-xl-6 col-12">

                <ActiveStatus Order="Order" />

                <OrderTaskGrid OrderTasks="Order?.Tasks" OrderId="@OrderId" OnActionHandled="RefreshPageAsync" />

                <NotificationTraceGridCard Customer="@Customer" OrderId="@OrderId" />

                <SecureMailDeliveryGridCard Customer="@Customer" OrderId="@OrderId" />

                <WarehouseDocumentGridCard OrderId="OrderId" />

                <PrintDocumentGridCard Customer="@Customer" OrderId="@OrderId" />

                <ESafeDeliveryGridCard Customer="@Customer" OrderId="@OrderId" />

                <EmailDeliveryGridCard Customer="@Customer" OrderId="@OrderId" />

                <IntersolveOrderGridCard Customer="@Customer" OrderId="@OrderId" />

            </div>
        }
    </div>
</PageBody>