﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Warehouse.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Models;

public class WarehouseDocumentDefaultFilterContext : QueryStringFilterContext<WarehouseDocumentListItem, WarehouseDocumentDefaultFilterFields>, IWarehouseDocumentFilterContext
{
    private readonly IWarehouseDocumentService documentService;

    public override string FilterTypeName => FilterType.Default.ToString();

    public WarehouseDocumentDefaultFilterContext(
        NavigationManager navigationManager,
        IWarehouseDocumentService documentService)
        : base(navigationManager)
    {
        this.documentService = documentService;
    }

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToWarehouseDocumentListWithDefaultFilter(Fields);

    protected override async Task<PagedResultModel<WarehouseDocumentListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await documentService.GetDocumentsByDefaultFilterAsync(Fields.ToApiFilter(nextContinuationToken));
}
