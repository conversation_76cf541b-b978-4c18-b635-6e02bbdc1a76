﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.SecureMail.Contracts.Api;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Services;

public class SecureMailDeliveryService : BaseApiService, ISecureMailDeliveryService
{
    public SecureMailDeliveryService(
        HttpClient httpClient)
        : base(httpClient)
    {
    }

    public async Task<PagedResultModel<DeliveryListItem>> GetAllDeliveriesByFilterAsync(DeliveryListFilter filter)
    {
        var deliveryListResult = await PostWithResultAsync<PagedList<DeliveryListItem>>(filter, "securemail/delivery/pagedList");

        return new PagedResultModel<DeliveryListItem>
        {
            Items = deliveryListResult.Items,
            ContinuationToken = deliveryListResult.ContinuationToken
        };
    }

    public async Task<PagedResultModel<DeliveryListItem>> GetAllDeliveriesByFilterAsync(DeliveryBatchSelectionFilter filter)
    {
        var deliveryListResult = await PostWithResultAsync<PagedList<DeliveryListItem>>(filter, "securemail/deliveryBatch/pagedList");

        return new PagedResultModel<DeliveryListItem>
        {
            Items = deliveryListResult.Items,
            ContinuationToken = deliveryListResult.ContinuationToken
        };
    }

    public async Task<DeliveryDetails> GetDeliveryDetailsAsync(string customer, Guid deliveryId) =>
        await GetAsync<DeliveryDetails>($"securemail/delivery/details?customer={customer}&deliveryId={deliveryId}");
}
