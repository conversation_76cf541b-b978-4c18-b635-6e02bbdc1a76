using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;

public partial class WarehouseDocumentGridCard
{
    [Parameter]
    public Guid OrderId { get; set; }

    [Inject]
    IWarehouseDocumentService WarehouseService { get; set; }

    [Inject]
    NavigationManager NavigationManager { get; set; }

    private void NavigateToDocument(string customer, string documentId) => 
        NavigationManager.NavigateTo($"WMS/Documents/{customer}/{documentId}");

    private static DateTimeOffset GetLastUpdatedOnTimestamp(WarehouseDocumentListItem listItem) => 
        listItem.LastStatus != null ? listItem.LastStatus.Timestamp : listItem.CreatedOn;
}
