﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Intersolve.Contracts.Configuration;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Services
{
    public class IntersolveService : BaseApiService, IIntersolveService
    {

        public IntersolveService(HttpClient httpClient) : base(httpClient)
        { }

        public async Task<List<Configuration>> GetAllConfigurationsAsync() =>
            await GetAsync<List<Configuration>>($"intersolve/configurations");

        public async Task<Configuration> GetConfigurationDetailsAsync(string customerId) =>
            await GetAsync<Configuration>($"intersolve/configurations/{customerId}");

        public async Task CreateConfigurationAsync(CreateConfiguration model) =>
            await PostAsync(model, "intersolve/configurations");

        public async Task UpdateConfigurationAsync(UpdateConfiguration model, string customerId) =>
            await PatchAsync(model, $"intersolve/configurations/{customerId}");
    }
}
