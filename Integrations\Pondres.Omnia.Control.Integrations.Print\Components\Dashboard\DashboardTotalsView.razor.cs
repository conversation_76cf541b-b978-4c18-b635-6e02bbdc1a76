using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Dashboard;

public partial class DashboardTotalsView
{
    private int[] AllHours { get; set; }

    private DateTime[] AllDays { get; set; }

    [Parameter]
    public PrintDashboardFilter Filter { get; set; }

    [Parameter]
    public PrintBundleTotalsResult BundleTotals { get; set; }

    protected override void OnParametersSet()
    {
        if (BundleTotals != null)
            AllHours = BundleTotals.TotalsPerHour.Keys.OrderBy(hour => hour).Select(x => int.Parse(x)).ToArray();

        if (Filter != null)
            AllDays = Enumerable.Range(0, 1 + Filter.CreatedOn.ToDate.Value.Subtract(Filter.CreatedOn.FromDate.Value).Days)
                                .Select(offset => Filter.CreatedOn.FromDate.Value.AddDays(offset).Date).ToArray();

        base.OnParametersSet();
    }

    private PrintBundleTotalsItem OverallTotal
    {
        get
        {
            var allTotalsPerHour = BundleTotals.TotalsPerHour.Select(x => x.Value);
            return new PrintBundleTotalsItem { DocumentCount = allTotalsPerHour.Sum(x => x.DocumentCount), SheetCount = allTotalsPerHour.Sum(x => x.SheetCount) };
        }
    }

    private string GetRowClassForDay(DateTime day)
    {
        var rowClass = string.Empty;

        if (day.DayOfWeek == DayOfWeek.Saturday)
            rowClass += " table-danger";
        else
            rowClass += " table-default";

        if (day.DayOfWeek == DayOfWeek.Sunday || AllDays.Last() == day)
            rowClass += " seperator-row";

        return rowClass;
    }

    private string FormatTotalsItem(PrintBundleTotalsItem item)
    {
        switch (Filter.DisplayOptionType)
        {
            case PrintBundleTotalDisplayOptionType.Sheets:
                return item.SheetCount.ToString();

            case PrintBundleTotalDisplayOptionType.Documents:
                return item.DocumentCount.ToString();

            default:
                return item.SheetCount.ToString();
        }
    }

    private static string FormatHour(int hour)
    {
        if (hour < 10)
            return $"0{hour}:00";
        else
            return $"{hour}:00";
    }
}
