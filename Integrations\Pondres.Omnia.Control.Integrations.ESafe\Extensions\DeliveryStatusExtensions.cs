﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Extensions;

public static class DeliveryStatusExtensions
{
    public static PagedResultModel<DeliveryStatus> ToPagedResultModel(this List<DeliveryStatus> triggers) =>
        new() { Items = triggers.OrderBy(x => x.Timestamp).ToList() };
}
