﻿@using Pondres.Omnia.ESafe.Contracts.Api.Delivery

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Vault Details
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table mb-0">
            <tbody>
                <tr>
                    <th>Vault Id</th>
                    <td>@Delivery.VaultDetails.Id</td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td>@Delivery.VaultDetails.Name</td>
                </tr>
                <tr>
                    <th>Vault Connector</th>
                    <td>@Delivery.VaultDetails.Connector</td>
                </tr>
                <tr>
                    <th>State</th>
                    <td>@Delivery.VaultDetails.StateTypeName</td>
                </tr>
                <tr>
                    <th>External State</th>
                    <td>@Delivery.VaultDetails.ExternalStateName</td>
                </tr>
                <tr>
                    <th>Created On</th>
                    <td>@Delivery.VaultDetails.CreatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>Last Updated On</th>
                    <td>@Delivery.VaultDetails.LastUpdatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>Uri</th>
                    <td>@Delivery.VaultDetails.Uri</td>
                </tr>
                <tr>
                    <th>Channel Types</th>
                    <td>@string.Join(", ", Delivery.VaultDetails.ChannelTypeNames)</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>