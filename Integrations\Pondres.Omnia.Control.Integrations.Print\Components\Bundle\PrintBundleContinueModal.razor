﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

@inherits ModalBase
    
<TelerikDialog Visible="@IsVisible" Width="350px">
    <DialogTitle>
        <strong>Continue bundle</strong>
    </DialogTitle>
    <DialogContent>
        @if (!Model.Bundle.Status.IsInFailedState)
        {
            <div class="alert alert-danger" role="alert">
                <strong>WARNING:</strong> Data will be lost if not all gathering threads have finished work.
            </div>
        }
        <TelerikForm Model="@Model" 
                     OnValidSubmit="@OnSubmitModalAsync"
                     ButtonsLayout="FormButtonsLayout.End">
            <FormValidation>
                <DataAnnotationsValidator />
            </FormValidation>
            <FormItems>
                <span>Continue bundle?</span>
                <AuthorizeView Roles="ControlOwner">           
                    <FormItem Field="@nameof(Model.Force)" LabelText="Force continue" />
                </AuthorizeView>
            </FormItems>
            <FormButtons>
                <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Ok</TelerikButton>
                <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
</TelerikDialog>