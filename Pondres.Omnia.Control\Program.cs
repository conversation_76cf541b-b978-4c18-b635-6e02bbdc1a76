using Pondres.Omnia.Control.Extensions;
using Serilog;
using Serilog.Events;
using System.Globalization;

namespace Pondres.Omnia.Control;

public static class Program
{
    public static IHostBuilder CreateHostBuilder(string[] args) => Host
        .CreateDefaultBuilder(args)
        .ConfigureAppConfiguration(builder => builder.AddKeyVaultIfAvailable())
        .ConfigureWebHostDefaults(webBuilder =>
        {
            webBuilder
                .UseStartup<Startup>()
                .UseSetting(WebHostDefaults.DetailedErrorsKey, "true");
        })
        .UseSerilog();

    public static int Main(string[] args)
    {
        SetCurrentCultureToDutch();

        CreateLogger();

        try
        {
            Log.Information("Building host");

            var host = CreateHostBuilder(args).Build();

            Log.Information("Starting host");

            host.Run();

            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static void CreateLogger()
    {
        var configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
        var isDevelopment = configuration["ASPNETCORE_ENVIRONMENT"]?.ToLower() == "development";

        var logConfiguration = new LoggerConfiguration()
            .MinimumLevel.Verbose()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("service", "Control")
            .Filter.ByExcluding(logEvent => logEvent.Properties.ContainsKey("RequestPath") && logEvent.Properties["RequestPath"].ToString().Contains("health"))
            .WriteTo.Console(restrictedToMinimumLevel: isDevelopment ? LogEventLevel.Debug : LogEventLevel.Warning)
            .WriteTo.ApplicationInsights(configuration["AppInsightsConnectionString"], TelemetryConverter.Traces, restrictedToMinimumLevel: LogEventLevel.Warning);

        Log.Logger = logConfiguration.CreateLogger();
    }

    private static void SetCurrentCultureToDutch()
    {
        CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("nl-NL");
        CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo("nl-NL");
    }
}