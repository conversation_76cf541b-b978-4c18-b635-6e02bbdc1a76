﻿using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.NotificationHub.Contracts.Api.Configuration;
using Pondres.Omnia.NotificationHub.Contracts.Internal.Model;
using Pondres.Omnia.NotificationHub.Contracts.Model;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class MappingModelExtensions
{
    public static MappingFormModel ToFormModel(this NotificationConfigurationMappingModel model) =>
        new()
        {
            DefinitionId = model.DefinitionId,
            DefinitionType = string.IsNullOrWhiteSpace(model.DefinitionType) ? null : Enum.Parse<NotificationDefinitionType>(model.DefinitionType),
            EventType = string.IsNullOrWhiteSpace(model.EventType) ? null : Enum.Parse<NotificationEventType>(model.EventType),
            Enabled = model.Enabled,
            Flow = model.Flow
        };

    public static NotificationConfigurationMappingModel ToDto(this MappingFormModel model) =>
        new()
        {
            DefinitionId = model.DefinitionId,
            DefinitionType = model.DefinitionType.ToString(),
            EventType = model.EventType.ToString(),
            Enabled = model.Enabled,
            Flow = model.Flow
        };

    public static void UpdateFromModel(this NotificationConfigurationMappingModel mapping, MappingFormModel model)
    {
        mapping.DefinitionId = model.DefinitionId;
        mapping.DefinitionType = model.DefinitionType.ToString();
        mapping.EventType = model.EventType.ToString();
        mapping.Enabled = model.Enabled;
        mapping.Flow = model.Flow;
    }
}
