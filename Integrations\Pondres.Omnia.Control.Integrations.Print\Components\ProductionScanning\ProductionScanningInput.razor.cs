using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization.Infrastructure;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Services;
using Telerik.Blazor;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning;

public partial class ProductionScanningInput
{
    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    private IPrintService PrintService { get; set; }

    [Inject]
    public AuthenticationStateProvider AuthenticationStateProvider { get; set; }

    [CascadingParameter]
    public ProductionScanningFilter Filter { get; set; }

    [Parameter]
    public EventCallback<ProductionScanningFilter> FilterSubmitted { get; set; }

    [CascadingParameter]
    public Dictionary<string, PrintBundleWithDetails> Bundles { get; set; }

    private EditContext editContext;

    private InputText scanField;

    public bool Loading { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    [CascadingParameter]
    public List<string> Batches { get; set; }

    public ProductionScanningAreThereFailedPrintsModal ProductionScanningAreThereFailedPrintsDialog { get; set; }
    public InputMultiLineBarcodeModal MultiLineBarcodeModalDialog { get; set; }

    protected override void OnInitialized()
    {
        editContext = new EditContext(Filter);
        editContext.OnFieldChanged += FilterForm_OnFieldChanged;

    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await SelectScanFieldAsync();
        }
    }

    public async Task SelectScanFieldAsync()
    {
        if (scanField?.Element != null)
        {
            await scanField.Element.Value.FocusAsync();
        }
    }

    private async void FilterForm_OnFieldChanged(object sender, FieldChangedEventArgs e)
    {
        editContext.MarkAsUnmodified();
        await SubmitFilterAsync();
        await SelectScanFieldAsync();
    }



    private async Task SubmitFilterAsync()
    {
        await FilterSubmitted.InvokeAsync(Filter);
    }

    private async Task ClearFilterAsync()
    {
        Filter.Reset();

        await SubmitFilterAsync();
        await InvokeAsync(StateHasChanged);

        await SelectScanFieldAsync();
    }

    private async Task CompleteScannedBundlesClickAsync()
    {
        Filter.BarCodesToBeReprinted = [];
        ProductionScanningAreThereFailedPrintsDialog.ShowDialog();
        await SelectScanFieldAsync();
    }

    private async Task PrintFailureAsync()
    {
        await MultiLineBarcodeModalDialog.OpenModalAsync();
    }

    private async Task NoPrintFailureAsync()
    {
        await CompleteScannedBundlesAsync();
    }

    private async Task CompleteScannedBundlesAsync()
    {
        try
        {
            Loading = true;
            var currentUser = await AuthenticationStateProvider.GetAuthenticationStateAsync();

            foreach (var bundleId in Bundles.Select(c => c.Value.PrintBundle.Id).Distinct().ToList())
            {
                try
                {
                    var bundle = Bundles[bundleId];

                    if (bundle.PrintBundle.Status.Status == MapPrintBundleState.Scanned)
                    {
                        await PrintService.CompletePrintBundleAsync(bundleId, currentUser.GetUsername());
                        ToastService.ShowInfo($"Bundle {bundleId} is afgemeld.");
                    }

                }
                catch (Exception)
                {
                    ToastService.ShowError($"Error: Afmelden van Bundel {bundleId} is mislukt.");
                }
            }
        }
        finally
        {
            Loading = false;
            await SubmitFilterAsync();
        }

    }

    private async Task FailedBundlesAsync(List<string> barcodesOfFailedPrints)
    {
        if (barcodesOfFailedPrints.Count == 0)
        {
            ToastService.ShowError("Geen barcodes opgegeven");
            return;
        }

        try
        {
            Loading = true;
            var allDocuments = new List<PrintDocumentListItem>();

            var firstBatch = await PrintService.GetAllDocumentsByBatchFilterAsync(
                new PrintDocumentBatchListFilter { Barcodes = barcodesOfFailedPrints, MaxPageSize = 100 });

            allDocuments.AddRange(firstBatch.Items);

            var continuationToken = firstBatch.ContinuationToken;


            while (!string.IsNullOrEmpty(continuationToken))
            {
                var nextBatch = await PrintService.GetAllDocumentsByBatchFilterAsync(
                    new PrintDocumentBatchListFilter
                    {
                        Barcodes = barcodesOfFailedPrints,
                        MaxPageSize = 100,
                        ContinuationToken = continuationToken
                    });

                allDocuments.AddRange(nextBatch.Items);
                continuationToken = nextBatch.ContinuationToken;
            }

            if (allDocuments.Any())
            {
                var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
                var currentUser = state.GetUsername();

                var groupsByCustomer = allDocuments.GroupBy(c => c.OrderMetadata.Customer).ToList();

                foreach (var groupByCustomer in groupsByCustomer)
                {
                    await PrintService.ReprintSelectedPrintDocumentsAsync(
                        orderIds: groupByCustomer.Select(c => c.OrderMetadata.OrderId).Distinct().ToList(),
                        groupByCustomer.Key,
                        username: currentUser);
                }
            }

            await CompleteScannedBundlesAsync();

            Filter.BarCodesToBeReprinted = [];
        }
        catch (Exception e)
        {
            ToastService.ShowError($"Uitval verwerken is gefaald ({e.Message})");
        }
        finally
        {
            Loading = false;
            await SubmitFilterAsync();
        }
    }
}