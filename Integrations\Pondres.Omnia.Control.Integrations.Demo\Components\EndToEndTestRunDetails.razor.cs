﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Demo.Services;
using Pondres.Omnia.Demo.Contracts.Api;
using Pondres.Omnia.Demo.Contracts.EndToEnd;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Demo.Components
{
    partial class EndToEndTestRunDetails
    {
        [Parameter] public Guid TestRunId { get; set; }

        [Inject]
        private IEndToEndTestService Service { get; set; }

        public ApiTestRunResultDetails Details { get; set; }

        protected override async Task OnParametersSetAsync()
        {
            Details = await Service.GetTestRunDetailsAsync(TestRunId);
        }

        private static void ApplyBackground(GridCellRenderEventArgs eventArgs)
        {
            if (eventArgs.Item is not TestResult testResult)
                return;

            eventArgs.Class = testResult.Success ? "table-success-light" : "table-danger";
        }
    }
}
