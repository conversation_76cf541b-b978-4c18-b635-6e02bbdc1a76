﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.ESafe.Extensions;
using Pondres.Omnia.Control.Integrations.ESafe.Services;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Models;

public class ESafeDeliveryDefaultFilterContext : QueryStringFilterContext<DeliveryListItem, ESafeDeliveryDefaultFilterFields>, IESafeDeliveryFilterContext
{
    private readonly IESafeDeliveryService deliveryService;

    public override string FilterTypeName => FilterType.Default.ToString();

    public ESafeDeliveryDefaultFilterContext(
        NavigationManager navigationManager,
        IESafeDeliveryService deliveryService)
        : base(navigationManager)
    {
        this.deliveryService = deliveryService;
    }

    protected override async Task<PagedResultModel<DeliveryListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await deliveryService.GetPagedListAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToESafeDeliveryListWithDefaultFilter(Fields);
}
