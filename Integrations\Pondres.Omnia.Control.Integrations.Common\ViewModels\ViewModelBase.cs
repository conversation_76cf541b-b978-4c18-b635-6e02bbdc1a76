﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Manager;

namespace Pondres.Omnia.Control.Integrations.Common.ViewModels
{
    public class ViewModelBase : NotifyPropertyChanged
    {
        [Inject]
        public NavigationManager NavigationManager { get; private set; }

        [Inject]
        public IClipboardManager ClipboardManager { get; private set; }

        [Inject]
        public IToastService ToastService { get; private set; }

        public bool IsLoading { get; protected set; }
        public bool IsInitialized { get; private set; }

        /// <summary>
        /// Override this method to initialize your viewmodel. This method automatically gets called by the component to which this viewmodel belongs.
        /// </summary>
        /// <returns>Task.</returns>
        public virtual Task InitializeAsync()
        {
            IsInitialized = true;

            return Task.CompletedTask;
        }

        public void ShowError(string message, Exception exception = null)
        {
            var exceptionMessage = exception?.Message;

            if (exception == null)
                exceptionMessage = message;

            ToastService.ShowError($"{message}: {exceptionMessage}");
        }
    }
}