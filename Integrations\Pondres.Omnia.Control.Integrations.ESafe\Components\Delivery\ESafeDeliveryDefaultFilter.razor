﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters
@using Pondres.Omnia.ESafe.Contracts.Delivery

<ListFilter Filter="Filter" FilterChanged="OnFilterChangedAsync">
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="Order Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.Connector" LabelText="Connector" />
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.CustomerReference" LabelText="Customer Reference" />        
        <ListFilterInputTemplate LabelText="State">
            <TelerikDropDownList Data="@Enum.GetNames(typeof(ESafeDeliveryStateType)).ToList()" @bind-Value="Filter.Fields.StateTypeName" />
        </ListFilterInputTemplate>
        <ListFilterInputTemplate LabelText="Result">
            <TelerikDropDownList Data="@Enum.GetNames(typeof(ESafeDeliveryResultType)).ToList()" @bind-Value="Filter.Fields.ResultTypeName" />
        </ListFilterInputTemplate>
        <ListFilterInputTemplate LabelText="Flow">
            <TelerikDropDownList Data="@Flows" @bind-Value="Filter.Fields.Flow" />
        </ListFilterInputTemplate>
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="created on" />
    </ListFilterRow>
    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />
</ListFilter>