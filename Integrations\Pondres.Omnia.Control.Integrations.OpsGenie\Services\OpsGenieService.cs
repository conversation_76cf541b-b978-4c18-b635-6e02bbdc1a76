﻿using Microsoft.Extensions.Logging;
using Pondres.Omnia.Control.Integrations.OpsGenie.Configuration;
using Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;
using Pondres.Omnia.Control.Integrations.OpsGenie.Models;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.OpsGenie.Services;

public class OpsGenieService : IOpsGenieService
{
    private readonly ILogger<OpsGenieService> Logger;
    private readonly HttpClient client;
    private readonly Uri baseUrl;
    private readonly string platformFilter;
    private readonly string customerFilter;
    private readonly string scheduleFilter;
    private readonly string alertsKey;
    private readonly string scheduleKey;

    public OpsGenieService(ILogger<OpsGenieService> logger, HttpClient client, OpsGenieSettings opsGenieSettings)
    {
        Logger = logger;
        this.client = client;

        baseUrl = new Uri(opsGenieSettings.BaseUrl);

        platformFilter = opsGenieSettings.PlatformFilterId.ToAlertFilter();
        customerFilter = opsGenieSettings.CustomerFilterId.ToAlertFilter();
        scheduleFilter = opsGenieSettings.ScheduleFilterId.ToScheduleFilter();

        alertsKey = opsGenieSettings.OpsGenieAlertsKey;
        scheduleKey = opsGenieSettings.OpsGenieScheduleKey;
    }

    public async Task<AlertsViewModel> GetPlatformAlertCountsAsync()
    {
        try
        {
            string alertResponse = await GetOpsGenieJsonResponseAsync(platformFilter, alertsKey);
            var alertModel = JsonSerializer.Deserialize<OpsGenieAlertModel>(alertResponse, GetJsonSerializerOptions());

            return alertModel.ToAlertsViewModel();
        }
        catch (Exception exception)
        {
            Logger.LogError(exception, "Could not retieve open alerts from OpsGenie");
            throw;
        }
    }

    public async Task<AlertsViewModel> GetCustomerAlertCountsAsync()
    {
        try
        {
            string alertResponse = await GetOpsGenieJsonResponseAsync(customerFilter, alertsKey);
            var alertModel = JsonSerializer.Deserialize<OpsGenieAlertModel>(alertResponse, GetJsonSerializerOptions());

            string scheduleResponse = await GetOpsGenieJsonResponseAsync(scheduleFilter, scheduleKey);
            var scheduleModel = GetScheduleModel(scheduleResponse);

            return alertModel.ToAlertsViewModel(scheduleModel);
        }
        catch (Exception exception)
        {
            Logger.LogError(exception, "Could not retieve open alerts from OpsGenie");
            throw;
        }
    }

    private static OpsGenieScheduleModel GetScheduleModel(string scheduleResponse)
    {
        return JsonSerializer.Deserialize<OpsGenieScheduleModel>(scheduleResponse, GetJsonSerializerOptions());
    }

    private async Task<string> GetOpsGenieJsonResponseAsync(string query, string key)
    {
        var requestUrl = new Uri(baseUrl, query);
        var request = new HttpRequestMessage(HttpMethod.Get, requestUrl);
        request.Headers.Add("Authorization", $"GenieKey {key}");

        var response = await client.SendAsync(request);
        return await response.Content.ReadAsStringAsync();
    }

    private static JsonSerializerOptions GetJsonSerializerOptions() =>
        new() { PropertyNameCaseInsensitive = true };
}