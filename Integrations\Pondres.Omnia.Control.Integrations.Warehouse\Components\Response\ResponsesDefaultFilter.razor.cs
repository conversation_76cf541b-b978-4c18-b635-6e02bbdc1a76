using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Response;

public partial class ResponsesDefaultFilter
{
    [Inject]
    protected IToastService ToastService { get; set; }

    [Inject]
    protected IWarehouseResponseService WarehouseServic { get; set; }

    [Parameter]
    public EventCallback<WarehouseResponseDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public WarehouseResponseDefaultFilterContext Filter { get; set; }

    private List<string> MessageTypes { get; set; } = new List<string>();

    private async Task OnFilterChangedAsync() =>
        await FilterChanged.InvokeAsync(Filter);

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await LoadMessageTypesAsync();
    }

    private async Task LoadMessageTypesAsync()
    {
        try
        {
            MessageTypes = await WarehouseServic.GetMessageTypesAsync();
        }
        catch (Exception)
        {
            ToastService.ShowError("Failed to load message types");
        }
    }
}
