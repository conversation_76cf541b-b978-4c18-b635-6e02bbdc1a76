﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Services;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintBundleDefaultFilterContext : QueryStringFilterContext<PrintBundleListItem, PrintBundleDefaultFilterFields>, IPrintBundleFilterContext
{
    private readonly IPrintService printService;
    public override string FilterTypeName => FilterType.Default.ToString();

    public PrintBundleDefaultFilterContext(NavigationManager navigationManager, IPrintService printService)
        : base(navigationManager)
    {
        this.printService = printService;
    }

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToCurrentPrintBundlesWithDefaultFilter(Fields);

    protected override async Task<PagedResultModel<PrintBundleListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await printService.GetAllBundlesByDefaultFilterAsync(Fields.ToApiFilter(nextContinuationToken));
}