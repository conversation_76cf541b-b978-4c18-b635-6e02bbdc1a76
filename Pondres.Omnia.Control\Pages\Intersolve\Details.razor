﻿@page "/Intersolve/OrderDetails/{Customer}/{ProductOwnerNr}/{IntersolveOrderId}"

@using Pondres.Omnia.Control.Integrations.Common.Components
@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link

@inherits ControlPageBase

<PageHeader Title="Audit Intersolve Order Details" />
<PageBody>
    <div class="container-fluid row">
        <LoadingSpinner Show="LoadingOrders" />

        @if (InvalidResponse)
        {
            <div class="alert alert-danger" role="alert">
                Could not retrieve Intersolve Order with OrderId "@ProductOwnerNr"
            </div>
        }

        @if (!LoadingOrders && !InvalidResponse)
        {
            <div class="col-xl-6 col-12">
                <IntersolveOrderSummary Order="Order"
                                        Customer="@Customer"
                                        OrderId="@ProductOwnerNr">
                    <OrderDetailsLink>
                        <OrderDetailsLink OrderId="@Order.OrderMetadataDefinition.OrderId.ToString()"
                                          Customer="@Customer">
                            @Order.OrderMetadataDefinition.OrderId
                        </OrderDetailsLink>
                    </OrderDetailsLink>
                </IntersolveOrderSummary>
            </div>

            <div class="col-xl-6 col-12">
                <IntersolveOrderArticles Order="@Order" />
            </div>
        }
    </div>
</PageBody>