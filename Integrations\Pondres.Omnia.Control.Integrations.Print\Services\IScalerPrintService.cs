﻿namespace Pondres.Omnia.Control.Integrations.Print.Services
{
    public interface IScalerPrintService
    {
        Task<Stream> GetBatchInstructionsPDFAsync(List<PrintBundleForInstructions> printBundlesForInstructions);
        Task<Stream> GetCombinedPrintInstructionsPDFAsync(List<PrintBundleForInstructions> printBundlesForInstructions);
        Task<Stream> GetPrintInstructionsPDFAsync(List<PrintBundleForInstructions> printBundlesForInstructions);
    }
}