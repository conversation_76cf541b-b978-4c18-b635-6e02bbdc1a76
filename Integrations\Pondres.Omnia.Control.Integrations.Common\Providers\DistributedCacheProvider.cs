﻿using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.Common.Provicers;

public class DistributedCacheProvider : IDistributedCacheProvider
{
    private const string cacheKeyPrefix = "control_cache_";

    private readonly IDistributedCache cache;

    public DistributedCacheProvider(IDistributedCache cache)
    {
        this.cache = cache;
    }

    public async Task<TObjectType> GetAsync<TObjectType>(string key, CancellationToken cancellationToken = default)
    {
        var bytes = await cache.GetAsync(GetCacheKey(key), cancellationToken);

        return bytes != null ? ConvertBytesToObject<TObjectType>(bytes) : default;
    }

    private static string GetCacheKey(string key) => $"{cacheKeyPrefix}{key}";

    public async Task SetWithSlidingExpirationAsync<TObjectType>(string key, TObjectType value, TimeSpan slidingExpiration, CancellationToken cancellationToken = default)
    {
        var options = GetCacheOptionsForSlidingExpiration(slidingExpiration);

        await SetCacheValueAsync(value, key, options, cancellationToken);
    }

    private static DistributedCacheEntryOptions GetCacheOptionsForSlidingExpiration(TimeSpan expiration) =>
        new()
        {
            SlidingExpiration = expiration
        };

    private async Task SetCacheValueAsync<TObjectType>(TObjectType value, string key, DistributedCacheEntryOptions options, CancellationToken cancellationToken)
    {
        var serializedValue = JsonSerializer.Serialize(value, options: null);

        await cache.SetStringAsync(GetCacheKey(key), serializedValue, options, cancellationToken);
    }

    private static TObjectType ConvertBytesToObject<TObjectType>(byte[] bytes) =>
        JsonSerializer.Deserialize<TObjectType>(new ReadOnlySpan<byte>(bytes));
}
