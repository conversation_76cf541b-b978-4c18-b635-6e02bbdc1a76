﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Email.Extensions;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Models;

public class EmailDeliveryBatchFilterContext : CachedFilterContext<DeliveryData, EmailDeliveryBatchFilterFields>, IEmailDeliveryFilterContext
{
    private readonly IEmailDeliveryService emailDeliveryService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public EmailDeliveryBatchFilterContext(
        NavigationManager navigationManager, 
        IFilterCacheService filterCacheService, 
        IEmailDeliveryService secureMailDeliveryService)
        : base(navigationManager, filterCacheService)
    {
        emailDeliveryService = secureMailDeliveryService;
    }

    protected override async Task<PagedResultModel<DeliveryData>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await emailDeliveryService.GetDeliveriesAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter(string filterId) =>
        navigationManager.NavigateToEmailDeliveryListWithBatchFilter(filterId);
}
