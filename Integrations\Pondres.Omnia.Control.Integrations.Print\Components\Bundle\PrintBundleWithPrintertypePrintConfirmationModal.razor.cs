﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Bundle
{
    public partial class PrintBundleWithPrintertypePrintConfirmationModal : ModalBase
    {
        public DGCodeBatchDetails Model { get; private set; }

        public IEnumerable<ActionListButtonResult> Result { get; private set; }

        public bool HasResult => Result?.Any() ?? false;

        public TelerikDialog Dialog { get; set; }

        [Parameter]
        public EventCallback OnSaveAction { get; set; }

        [Parameter]
        public EventCallback OnCloseAction { get; set; }

        public void ShowDialog(DGCodeBatchDetails model)
        {
            Model = model;

            IsVisible = true;
        }

        public async Task SetResultAsync(IEnumerable<ActionListButtonResult> result)
        {
            Result = result;

            await InvokeAsync(StateHasChanged);

            Dialog.Refresh();
        }

        public static string GetRowColor(bool success) => success ? "table-success-light" : "table-danger";

        public async Task OnSubmitModalAsync()
        {
            if (OnSaveAction.HasDelegate)
                await OnSaveAction.InvokeAsync();

            if (CloseOnSubmit)
                await CloseDialogAsync();
        }

        public async Task CloseDialogAsync()
        {
            if (OnCloseAction.HasDelegate)
                await OnCloseAction.InvokeAsync();

            Model = null;
            Result = null;
            IsVisible = false;
        }
    }
}
