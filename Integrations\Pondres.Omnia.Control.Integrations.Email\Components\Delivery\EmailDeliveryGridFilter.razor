﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab
@using Pondres.Omnia.Control.Integrations.Email.Models

<TabPanel TDataType="IEmailDeliveryFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IEmailDeliveryFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IEmailDeliveryFilterContext">
                <TabPanelTab TDataType="IEmailDeliveryFilterContext" Name="Default" Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <EmailDeliveryDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
                <TabPanelTab TDataType="IEmailDeliveryFilterContext" Name="Batch" Data="BatchFilter" Selected="FilterManager.CurrentFilter == BatchFilter">
                    <Template>
                        <EmailDeliveryBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>

