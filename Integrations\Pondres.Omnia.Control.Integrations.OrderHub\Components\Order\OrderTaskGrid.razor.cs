using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.OrderTask;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Order;

public partial class OrderTaskGrid
{
    [Inject]
    IOrderService OrderService { get; set; }

    [Parameter]
    public Guid OrderId { get; set; }

    [Parameter]
    public List<OrderTaskInformation> OrderTasks { get; set; }

    [Parameter]
    public EventCallback OnActionHandled { get; set; }

    private ActionModal<OrderTaskInformation> CancelTaskModal { get; set; }

    private ActionModal<OrderTaskInformation> RetryTaskModal { get; set; }

    private async Task CancelBatchAsync(OrderTaskInformation task) => await OrderService.CancelTaskAsync(task.TaskId);
    private async Task RetryBatchAsync(OrderTaskInformation task) => await OrderService.RetryTaskAsync(OrderId, task.TaskId);
    private async Task RefreshPageAsync() => await OnActionHandled.InvokeAsync();
}
