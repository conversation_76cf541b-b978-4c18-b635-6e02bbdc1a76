﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.ESafe.Contracts.Api;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Services;

public class ESafeDeliveryService : BaseApiService, IESafeDeliveryService
{
    public ESafeDeliveryService(HttpClient httpClient)
        : base(httpClient)
    {
    }

    public async Task<DeliveryDetails> GetDetailsAsync(string customer, Guid deliveryId) =>
        await GetAsync<DeliveryDetails>($"esafe/delivery/details?customer={customer}&deliveryId={deliveryId}");

    public async Task<PagedResultModel<DeliveryListItem>> GetPagedListAsync(DeliveryBatchSelectionFilter filter)
    {
        var listResult = await PostWithResultAsync<PagedList<DeliveryListItem>>(filter, "esafe/deliveryBatch/pagedList");

        return new PagedResultModel<DeliveryListItem>
        {
            Items = listResult.Items,
            ContinuationToken = listResult.ContinuationToken
        };
    }

    public async Task<PagedResultModel<DeliveryListItem>> GetPagedListAsync(DeliveryListFilter filter)
    {
        var listResult = await PostWithResultAsync<PagedList<DeliveryListItem>>(filter, "esafe/delivery/pagedList");

        return new PagedResultModel<DeliveryListItem>
        {
            Items = listResult.Items,
            ContinuationToken = listResult.ContinuationToken
        };
    }
}
