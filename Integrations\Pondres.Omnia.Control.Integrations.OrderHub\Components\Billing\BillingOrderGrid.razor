﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.OrderHub.Contracts.Api.Order
@using Telerik.Blazor.Components
@using Telerik.SvgIcons

<div class="card shadow mb-4">
    <TelerikGrid Data="@Rows"
                 Sortable="false"
                 Groupable="false"
                 Resizable="false"
                 Reorderable="false"
                 Pageable="false"
                 OnRowRender="OnRowRender">
        <GridExport>
            <GridCsvExport FileName="BillingOrders" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="SvgIcon.FileCsv" Enabled="@Rows.Any()">Export to CSV</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridColumn Field="@nameof(OrderStatisticsRow.Flow)" Title="Flow" />
            <GridColumn Field="@nameof(OrderStatisticsRow.CategoryOne)" Title="CategoryOne" />
            <GridColumn Field="@nameof(OrderStatisticsRow.CategoryTwo)" Title="CategoryTwo" />
            <GridColumn Field="@nameof(OrderStatisticsRow.CategoryThree)" Title="CategoryThree" />
            <GridColumn Field="@nameof(OrderStatisticsRow.ReceivedCount)" Title="Received" />
            <GridColumn Field="@nameof(OrderStatisticsRow.CompletedCount)" Title="Completed" />
            <GridColumn Field="@nameof(OrderStatisticsRow.ActiveCount)" Title="Active" />
            <GridColumn Field="@nameof(OrderStatisticsRow.FailedCount)" Title="Failed" />
        </GridColumns>
    </TelerikGrid>
</div>