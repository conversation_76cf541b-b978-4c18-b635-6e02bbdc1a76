﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Components;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Customer.Models;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using System.ComponentModel;

namespace Pondres.Omnia.Control.Integrations.Customer.Components
{
    public abstract class CustomerPageBase : ControlPageBase
    {
        private string currentCustomerId;

        public string CurrentCustomerId
        {
            get => currentCustomerId;
            set => SetValue(ref currentCustomerId, value);
        }

        [Inject]
        public ILocalStorageService LocalStorageService { get; private set; }

        [Inject]
        public ICustomerService CustomerService { get; private set; }

        public List<CustomerModel> Customers { get; private set; }

        protected override async Task OnInitializedAsync()
        {
            if (Customers == null || !Customers.Any())
                Customers = await CustomerService.GetAllCustomersAsync();

            if (CurrentCustomerId.IsNullOrWhiteSpace())
            {
                CurrentCustomerId = await LocalStorageService.GetItemAsync<string>(ControlConstants.CustomerStorageKey);

                if (CurrentCustomerId.IsNullOrWhiteSpace() && (Customers?.Any() ?? false))
                    CurrentCustomerId = Customers.First().Id;
            }

            await base.OnInitializedAsync();
        }

        protected override async void OnPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName.Equals(nameof(CurrentCustomerId)))
            {
                await LocalStorageService.SetItemAsync(ControlConstants.CustomerStorageKey, CurrentCustomerId);

                await OnCustomerChangedAsync(CurrentCustomerId);
            }

            base.OnPropertyChanged(sender, e);
        }

        protected virtual Task OnCustomerChangedAsync(string customerId)
            => Task.CompletedTask;
    }
}