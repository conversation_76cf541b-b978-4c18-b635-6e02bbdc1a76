﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.OrderHub;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddOrderHubServices(this IServiceCollection services, AppSettings appSettings)
    {
        var httpClientName = "OrderHub";

        services.AddHttpClient(name: httpClientName)
            .ConfigureBaseAddress(appSettings.OrderServiceUri)
            .ConfigureAuthenticationToken(appSettings.OrderServiceAuthToken);

        services.AddHttpClient<IOrderService, OrderService>(name: httpClientName);
        services.AddHttpClient<IBillingService, BillingService>(name: httpClientName);
        services.AddHttpClient<IFlowService, FlowService>(name: httpClientName);

        services.AddTransient<OrderBatchFilterContext>();
        services.AddTransient<OrderDefaultFilterContext>();

        return services;
    }
}