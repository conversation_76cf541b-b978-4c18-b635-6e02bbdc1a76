﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Modal;

public abstract class ModalBase : ComponentBase
{
    [Inject]
    private IJSRuntime jsRuntime { get; set; }

    private bool isVisible;

    [Parameter]
    public bool CloseOnSubmit { get; set; } = true;

    public bool IsVisible
    {
        get => isVisible;
        set
        {
            isVisible = value;

            if (isVisible)
                DisableScrolling();
            else
                EnableScrolling();
        }
    }

    protected void EnableScrolling()
    {
        Task.Run(async () =>
        {
            await ToggleScrollingAsync(true);
        }).ConfigureAwait(false);
    }

    protected void DisableScrolling()
    {
        Task.Run(async () =>
        {
            await ToggleScrollingAsync(false);
        }).ConfigureAwait(false);
    }

    private async Task ToggleScrollingAsync(bool enabled) =>
        await jsRuntime.InvokeVoidAsync("scroll.toggle", enabled);
}