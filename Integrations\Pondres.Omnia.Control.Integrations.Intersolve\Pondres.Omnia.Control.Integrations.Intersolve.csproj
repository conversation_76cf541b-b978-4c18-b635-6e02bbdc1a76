﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<OutputType>Library</OutputType>
	</PropertyGroup>

	<ItemGroup>
	  <Content Remove="OpenAPIs\intersolve_swagger.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="BlazorMonaco" Version="3.3.0" />
		<PackageReference Include="JsonSchema.Net" Version="7.3.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Pondres.Omnia.Intersolve.Contracts" Version="1.20240924.1" />
		<PackageReference Include="NSwag.ApiDescription.Client" Version="14.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<OpenApiReference Include="OpenAPIs\intersolve_swagger.json" CodeGenerator="NSwagCSharp" Namespace="Pondres.Omnia.Control.Integrations.Intersolve.Client" ClassName="IntersolveServiceClient">
			<SourceUri>https://api-omnia-test.pondres.nl/intersolve/swagger/v1/swagger.json</SourceUri>
			<Options>/ExcludedParameterNames:x-token /GenerateExceptionClasses:false /AdditionalNamespaceUsages:Pondres.Omnia.Control.Integrations.Common.Exceptions /OperationGenerationMode:SingleClientFromPathSegments /GeneratePrepareRequestAndProcessResponseAsAsyncMethods:true</Options>
		</OpenApiReference>
	</ItemGroup>

</Project>
