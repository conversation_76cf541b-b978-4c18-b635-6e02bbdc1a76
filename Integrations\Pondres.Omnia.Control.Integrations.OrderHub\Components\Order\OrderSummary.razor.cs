using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Order;

public partial class OrderSummary
{
    [Inject]
    private IOrderService OrderService { get; set; }

    [Parameter]
    public string CurrentUser { get; set; }

    [Parameter]
    public OrderFullInformation Order { get; set; }

    [Parameter]
    public EventCallback OnActionHandled { get; set; }

    private ActionModal<OrderFullInformation> CancelOrderModal { get; set; }

    private ActionModal<OrderFullInformation> RetryOrderModal { get; set; }

    private ActionModal<OrderFullInformation> StartOrderModal { get; set; }

    private ActionModal<OrderRejectModel> RejectOrderModal { get; set; }

    private async Task CancelOrderAsync(OrderFullInformation order) => await OrderService.CancelOrderAsync(Order.Customer, Guid.Parse(Order.OrderId), CurrentUser);

    private async Task StartOrderAsync(OrderFullInformation order) => await OrderService.StartOrderAsync(Order.Customer, Guid.Parse(Order.OrderId));

    private async Task RetryOrderAsync(OrderFullInformation order) => await OrderService.RetryOrderAsync(Order.Customer, Guid.Parse(Order.OrderId));

    private string GetConcatinatedCategories()
    {
        var categories = new string[] { Order.Categories.One, Order.Categories.Two, Order.Categories.Three };
        var jointCategories = string.Join("-", categories.Where(x => !string.IsNullOrEmpty(x)));
        return !string.IsNullOrEmpty(jointCategories) ? jointCategories : "None";
    }

    private async Task RejectOrderAsync(OrderRejectModel rejectModel)
    {
        await OrderService.RejectOrderAsync(new OrderRejectCommand
        {
            Customer = Order.Customer,
            OrderId = Order.OrderId,
            ErrorCode = rejectModel.ErrorCode,
            ErrorReason = rejectModel.ErrorReason,
            InitiatedBy = CurrentUser
        });
    }

    private async Task RefreshPageAsync() => await OnActionHandled.InvokeAsync();
}