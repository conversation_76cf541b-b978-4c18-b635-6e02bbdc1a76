﻿using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.ViewModels.Schedules
{
    public interface ISchedulesOverviewViewModel
    {
        Task<List<ReleaseSchedules>> LoadAllReleaseSchedulesAsync();

        Task<ReleaseScheduleResult> CreateReleaseSchedulesAsync(ReleaseSchedules schedule);

        Task<ReleaseScheduleResult> UpdateReleaseScheduleAsync(ReleaseSchedules schedule);

        Task DeleteReleaseScheduleAsync(string scheduleId);
    }
}
