﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Warehouse.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Models;

public class WarehouseResponseDefaultFilterContext : QueryStringFilterContext<WarehouseResponseListItem, WarehouseResponseDefaultFilterFields>, IWarehouseResponseFilterContext
{
    private readonly IWarehouseResponseService responseService;

    public override string FilterTypeName => FilterType.Default.ToString();

    public WarehouseResponseDefaultFilterContext(
        NavigationManager navigationManager,
        IWarehouseResponseService responseService)
        : base(navigationManager)
    {
        this.responseService = responseService;
    }

    protected override async Task<PagedResultModel<WarehouseResponseListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await responseService.GetAllResponsesByFilterAsync(Fields.ToApiFilter(nextContinuationToken));

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToWarehouseResponseListWithDefaultFilter(Fields);
}
