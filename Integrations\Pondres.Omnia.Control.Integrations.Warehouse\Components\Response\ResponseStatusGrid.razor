﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument

@inject NavigationManager navigationManager

<GridCard Title="Responses"
          GetRawColorFunc="@((response) => response.GetStatusColorStyle())"
          GetItemsAsync="(_) => Task.FromResult(Responses.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Response File</th>
        <th>Status</th>
        <th>Created on</th>
    </TableHeaderContent>
    <TableRowContent Context="response">
        <td><a class="link" @onclick="() => NavigateToResponseDetails(response.ResponseFile)" @onclick:stopPropagation="true">@response.ResponseFile</a></td>
        <td>@($"{response.StatusCode}-{response.StatusName}")</td>
        <td>@response.Timestamp.LocalDateTime</td>
    </TableRowContent>
</GridCard>


