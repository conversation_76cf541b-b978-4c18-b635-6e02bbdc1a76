﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid

<GridCard Title="Status history"
          GetRawColorFunc="(_) => Delivery.CurrentMailStatus.StateType.GetStatusColorStyle()"
          GetItemsAsync="(_) => Task.FromResult(Delivery.MailStatusHistory.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Type</th>
        <th>Timestamp</th>
        <th>Event Timestamp</th>
    </TableHeaderContent>
    <TableRowContent Context="statusEntry">
        <td>@statusEntry.StateType.GetStatusString(statusEntry.ResultType)</td>
        <td>@statusEntry.Timestamp.LocalDateTime</td>
        <td>@statusEntry.EventTimestamp.LocalDateTime</td>
    </TableRowContent>
</GridCard>


