﻿namespace Pondres.Omnia.Control.Extensions;

public static class IApplicationBuilderExtensions
{
    public static IApplicationBuilder UseEnvironmentSpecificRequestHandling(this IApplicationBuilder app, IHostEnvironment environment)
    {
        if (environment.IsDevelopment())
            app.UseDeveloperExceptionPage();
        else
        {
            app.UseExceptionHandler("/Error");

            app.Use((context, next) =>
            {
                context.Request.Scheme = "https";

                return next.Invoke();
            });
        }

        return app;
    }
}
