﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Customer;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddCustomerServices(this IServiceCollection services, AppSettings appSettings)
    {
        var httpClientName = "CustomerService";

        services.AddHttpClient(name: httpClientName)
            .ConfigureBaseAddress(appSettings.CustomerServiceUri)
            .ConfigureAuthenticationToken(appSettings.CustomerServiceAuthToken);

        services.AddHttpClient<ISecretService, SecretService>(name: httpClientName);
        services.AddHttpClient<ICustomerService, CustomerService>(name: httpClientName);
        services.AddHttpClient<IFileService, FileService>(name: httpClientName);

        return services;
    }
}