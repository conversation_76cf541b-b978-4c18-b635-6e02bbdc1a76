﻿<div style="display: flex; align-items: center; gap: 16px; min-width: 300px;">
    <div style="display: flex; align-items: center; gap: 6px;">
        <InputCheckbox @bind-Value="@ShowTestCustomers" @oninput="(e) => OnShowTestCustomersChangedAsync(e.Value != null && (bool)e.Value)"/>
        <span style="font-size: 12px; white-space: nowrap;">Show Test Customers</span>
    </div>

    <div style="min-width: 250px;">
        <TelerikDropDownList Data="@FilteredCustomers"
                             Value="@SelectedCustomerId"
                             TextField="Name"
                             ValueField="Id"
                             Width="250px"
                             ScrollMode="@DropDownScrollMode.Scrollable"
                             ItemHeight="45"
                             ValueChanged="(string customerId) => OnValueChangedAsync(customerId)">
            <ItemTemplate>
                @($"{(context as CustomerModel).Name} - {(context as CustomerModel).Id}")
            </ItemTemplate>
            <ValueTemplate>
                @($"{(context as CustomerModel).Name} - {(context as CustomerModel).Id}")
            </ValueTemplate>
            <DropDownListSettings>
                <DropDownListPopupSettings Height="auto" MaxHeight="350px" />
            </DropDownListSettings>
        </TelerikDropDownList>
    </div>
</div>
