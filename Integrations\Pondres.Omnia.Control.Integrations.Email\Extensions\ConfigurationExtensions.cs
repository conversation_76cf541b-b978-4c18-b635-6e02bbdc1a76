﻿using Pondres.Omnia.Control.Integrations.Email.Client;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.Email.Extensions;

public static class ConfigurationExtensions
{
    public static CreateCustomerConfigurationModel ToCreateDto(this string json) =>
        JsonSerializer.Deserialize<CreateCustomerConfigurationModel>(json);

    public static UpdateCustomerConfigurationModel ToUpdateDto(this string json) =>
        JsonSerializer.Deserialize<UpdateCustomerConfigurationModel>(json);

    public static string ToJson(this CustomerConfigurationModel configuration, JsonSerializerOptions options) =>
        JsonSerializer.Serialize(configuration, options);

    public static string ToJson(this CreateCustomerConfigurationModel configuration, JsonSerializerOptions options) =>
        JsonSerializer.Serialize(configuration, options);

    public static string ToJson(this UpdateCustomerConfigurationModel configuration, JsonSerializerOptions options) =>
        JsonSerializer.Serialize(configuration, options);
}
