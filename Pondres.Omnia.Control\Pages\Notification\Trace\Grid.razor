﻿@page "/Notifications/Traces"

@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace
@using Pondres.Omnia.Control.Integrations.NotificationHub.Models
@using Pondres.Omnia.NotificationHub.Contracts.Api.Trace

@inherits CustomerFilterGridPageBase<NotificationTraceListItem, INotificationTraceFilterContext>

<PageHeader Title="Audit Traces">
    <CustomerSelection Customers="@Customers" SelectedCustomerId="@CurrentCustomerId" CustomerChanged="OnCustomerChangedAsync" />
</PageHeader>
<PageBody>
    <div class="container-fluid">
        <NotificationTraceGridFilter FilterChanged="OnFilterChangedAsync" />

        <NotificationTraceGrid Traces="GridItems" Loading="LoadingGridItems" OnRefresh="OnRefresh" CustomerId="@CurrentCustomerId" />
    </div>
</PageBody>