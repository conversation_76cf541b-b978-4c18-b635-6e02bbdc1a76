﻿using Pondres.Omnia.NotificationHub.Contracts.Internal.Model;
using Pondres.Omnia.NotificationHub.Contracts.Model;
using System.ComponentModel.DataAnnotations;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Models;

public class MappingFormModel
{
    [Required(ErrorMessage = "Definition id is required")]
    public string DefinitionId { get; set; } = null;

    [Required(ErrorMessage = "Definition type is required"), EnumDataType(typeof(NotificationDefinitionType))]
    public NotificationDefinitionType? DefinitionType { get; set; }

    [Required(ErrorMessage = "Event type is required"), EnumDataType(typeof(NotificationEventType))]
    public NotificationEventType? EventType { get; set; }

    public string Flow { get; set; }

    public bool Enabled { get; set; } = true;
}
