using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning;

public partial class ProductionScanningInfo
{


    [CascadingParameter]
    private Dictionary<string, PrintBundleWithDetails> Bundles
    {
        set
        {
            var bundle = value.Values.FirstOrDefault();

            if (bundle != null)
            {
                Data.BatchName = bundle.PrintBundle.BatchName;
                Data.Customer = bundle.PrintBundle.Customer;
                Data.GordNumber = bundle.PrintBundle.GordNumber;
            }
            else
            {
                Data = new();
            }
        }
    }


    public ProductionScanningInfoData Data { get; set; } = new();

}