﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;

namespace Pondres.Omnia.Control.Pages.Notification.Configuration;

public partial class Configuration : CustomerPageBase
{
    [Inject]
    protected INotificationConfigurationService NotificationService { get; set; }

    [Inject]
    protected INotificationDefinitionService DefinitionService { get; set; }

    [Inject]
    protected IToastService ToastService { get; set; }

    private NotificationMappingGridContext MappingContext { get; set; } = new();

    public bool IsLoading { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        await LoadConfigurationAsync();
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await LoadConfigurationAsync();
        }
    }

    private async Task LoadConfigurationAsync()
    {
        IsLoading = true;

        MappingContext?.Config?.Mappings?.Clear();
        MappingContext?.Definitions?.Clear();

        await InvokeAsync(StateHasChanged);

        try
        {
            MappingContext = new NotificationMappingGridContext
            {
                Config = await NotificationService.GetConfigurationAsync(CurrentCustomerId),
                Definitions = await DefinitionService.GetDefinitionsListAsync(CurrentCustomerId)
            };
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
        finally
        {
            IsLoading = false;

            await InvokeAsync(StateHasChanged);
        }
    }
}