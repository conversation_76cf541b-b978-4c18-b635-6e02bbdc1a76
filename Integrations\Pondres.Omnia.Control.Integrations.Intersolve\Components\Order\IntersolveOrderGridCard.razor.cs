﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
{
    public partial class IntersolveOrderGridCard
    {
        [Inject]
        public NavigationManager Navigation { get; set; }

        [Inject]
        public IntersolveServiceClient IntersolveServiceClient { get; set; }

        [Parameter]
        public string Customer { get; set; }

        [Parameter]
        public Guid OrderId { get; set; }

        private void NavigateToOrder(IntersolveOrderListItem order) => 
            Navigation.NavigateTo($"Intersolve/OrderDetails/{order.Customer}/{order.ProductOwnerNr}/{order.IntersolveOrderId}");

        private IntersolveOrderListFilter CreateTraceListFilter(string continuationToken) => new()
        {
            ContinuationToken = continuationToken,
            Customer = Customer,
            OrderId = OrderId.ToString(),
            MaxPageSize = ControlConstants.DefaultPageSize
        };

        public async Task<PagedResultModel<IntersolveOrderListItem>> GetNextItemsAsync(string continuationToken)
        {
            var result = await IntersolveServiceClient.OrderPagedListAsync(CreateTraceListFilter(continuationToken));

            return new PagedResultModel<IntersolveOrderListItem>
            {
                ContinuationToken = result.ContinuationToken,
                Items = [.. result.Items]
            };
        }
    }
}
