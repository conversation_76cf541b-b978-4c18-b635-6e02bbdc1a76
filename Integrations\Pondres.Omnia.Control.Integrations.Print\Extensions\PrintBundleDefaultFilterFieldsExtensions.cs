﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions;

public static class PrintBundleDefaultFilterFieldsExtensions
{
    public static PrintBundleDefaultFilter ToApiFilter(this PrintBundleDefaultFilterFields fields, string continuationToken) =>
        new()
        {
            BundleId = fields.BundleId,
            MaxPageSize = fields.PageSize,
            BatchName = fields.BatchName,
            DgCode = fields.DGCode,
            GordNumber = fields.GordNumber,
            PrintMode = fields.PrintMode,
            PrinterType = fields.PrinterType.ToDropdownInvariantValue().ToNullableEnum<PrinterType>(),
            Carrier = fields.Carrier,
            TestCustomers = fields.ShowTestCustomers,
            IncludeEmptyBundles = fields.ShowEmptyBundles,
            MailDateFrom = fields.MailDate.GetFromDate(),
            MailDateTo = fields.MailDate.GetToDate(),
            CreatedOnFrom = fields.CreatedOn.GetFromDate(),
            CreatedOnTo = fields.CreatedOn.GetToDate(),
            ContinuationToken = continuationToken,
            Customer = fields.Customer,
            State = fields.StateTypeName.ToDropdownInvariantValue().ToNullableEnum<PrintBundleStateType>()
        };
}
