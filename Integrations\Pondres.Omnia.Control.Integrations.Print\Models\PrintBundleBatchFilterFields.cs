﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintBundleBatchFilterFields : FilterFieldsBase<PrintBundleBatchFilterFields>
{
    public List<string> PrintBundleIds { get; set; } = new List<string>();
    public List<string> BatchNames { get; set; } = new List<string>();
    public string StateTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
}
