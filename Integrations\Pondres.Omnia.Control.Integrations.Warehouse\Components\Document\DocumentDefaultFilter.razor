﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters

<ListFilter Filter="Filter" FilterChanged="OnFilterChanged">

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="Order Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.DocumentReference" LabelText="Document Reference" />
        <ListFilterInputText @bind-Value="Filter.Fields.StatusCode" LabelText="Status Code" />
        <ListFilterInputText @bind-Value="Filter.Fields.FileName" LabelText="File Name" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="created on" />
    </ListFilterRow>

    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />

</ListFilter>


