﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Models;

public class NotificationTraceDefaultFilterContext : QueryStringFilterContext<NotificationTraceListItem, NotificationTraceDefaultFilterFields>, INotificationTraceFilterContext
{
    private readonly INotificationTraceService traceService;

    public override string FilterTypeName => FilterType.Default.ToString();

    public NotificationTraceDefaultFilterContext(NavigationManager navigationManager, INotificationTraceService traceService)
        : base(navigationManager)
    {
        this.traceService = traceService;
    }

    protected override async Task<PagedResultModel<NotificationTraceListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await traceService.GetAllTracesForFilterAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToNotificationTraceListWithDefaultFilter(Fields);
}
