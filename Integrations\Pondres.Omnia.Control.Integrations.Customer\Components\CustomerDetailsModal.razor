﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@inherits ModalBase

<TelerikDialog Visible="@IsVisible" Width="500px">
    <DialogTitle>
        <strong>Customer details</strong>
    </DialogTitle>
    <DialogContent>        
        <TelerikForm Model="@Customer" 
                     OnValidSubmit="@OnSubmitModalAsync"
                     ButtonsLayout="FormButtonsLayout.End">
            <FormValidation>
                <DataAnnotationsValidator />
            </FormValidation>
            <FormItems>
                <FormItem Field="@nameof(Customer.Id)" Enabled="@IsNew" LabelText="Id" />
                <FormItem Field="@nameof(Customer.Name)" LabelText="Name" />
                <FormItem Field="@nameof(Customer.VaultName)" LabelText="Vault Name" />
                <FormItem Field="@nameof(Customer.StorageAccountName)" LabelText="Storage Account Name" />
            </FormItems>
            <FormButtons>
                <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Save</TelerikButton>
                <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
</TelerikDialog>