﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Flow;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Services
{
    public class FlowService : BaseApiService, IFlowService
    {
        public FlowService(HttpClient httpClient)
            : base(httpClient)
        { }

        public async Task<List<OrderFlowDefinitionDetails>> LoadOrderFlowDefinitionsAsync(string customer)
            => await GetAsync<List<OrderFlowDefinitionDetails>>($"order/flow/list?customer={customer}");

        public async Task<UpsertFlowDefinitionResult> CreateOrderFlowDefinitionDetailsAsync(UpsertFlowDefinition orderFlowDefinition)
            => await PostWithResultAsync<UpsertFlowDefinitionResult>(orderFlowDefinition, $"order/flow/create");

        public async Task<UpsertFlowDefinitionResult> UpdateOrderFlowDefinitionDetailsAsync(UpsertFlowDefinition orderFlowDefinition)
            => await PostWithResultAsync<UpsertFlowDefinitionResult>(orderFlowDefinition, $"order/flow/update");

        public async Task DisableOrderFlowDefinitionDetailsAsync(string customer, string flowName)
            => await PostWithParametersAsync($"order/flow/disable", new()
            {
                { "customer", customer },
                { "flowName", flowName }
            });
    }
}