﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Email.Models;

namespace Pondres.Omnia.Control.Integrations.Email.Extensions
{
    public static class NavigationManagerExtensions
    {
        public static void NavigateToEmailDeliveryListWithDefaultFilter(this NavigationManager navigationManager, EmailDeliveryDefaultFilterFields filter) =>
            navigationManager.NavigateTo($"Email/Deliveries?{FilterHelper.ConvertToQueryParameter(filter)}");

        public static void NavigateToEmailDeliveryListWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
            navigationManager.NavigateTo($"Email/Deliveries?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");
    }
}
