﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Services;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintDocumentDefaultFilterContext : QueryStringFilterContext<PrintDocumentListItem, PrintDocumentDefaultFilterFields>, IPrintDocumentFilterContext
{
    private readonly IPrintService printService;

    public override string FilterTypeName => FilterType.Default.ToString();

    public PrintDocumentDefaultFilterContext(
        NavigationManager navigationManager,
        IPrintService printService)
        : base(navigationManager)
    {
        this.printService = printService;
    }

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToPrintDocumentListWithDefaultFilter(Fields);

    protected override async Task<PagedResultModel<PrintDocumentListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await printService.GetAllDocumentsByDefaultFilterAsync(Fields.ToApiFilter(nextContinuationToken));
}
