﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.ViewModels;
using Pondres.Omnia.Control.Integrations.Customer.Models;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using System.ComponentModel;

namespace Pondres.Omnia.Control.Integrations.Customer.ViewModels
{
    public abstract class CustomerViewModelBase : ViewModelBase
    {
        private const string storageKey = "Customer";

        private string currentCustomerId;

        public string CurrentCustomerId
        {
            get => currentCustomerId;
            set => SetValue(ref currentCustomerId, value);
        }

        [Inject]
        public ILocalStorageService LocalStorageService { get; private set; }

        [Inject]
        public ICustomerService CustomerService { get; private set; }

        public List<CustomerModel> Customers { get; private set; }

        /// <summary>
        /// Initializes the viewmodel. Loads the current list of customers and sets the current customer from cache
        /// </summary>
        /// <returns>A Task representing the asynchronous operation.</returns>
        public override async Task InitializeAsync()
        {
            if (!Customers.Any())
                Customers = await CustomerService.GetAllCustomersAsync();

            if (CurrentCustomerId.IsNullOrWhiteSpace())
            {
                CurrentCustomerId = await LocalStorageService.GetItemAsync<string>(storageKey);

                if (CurrentCustomerId.IsNullOrWhiteSpace() && (Customers?.Any() ?? false))
                    CurrentCustomerId = Customers.First().Id;
            }

            PropertyChanged -= CustomerViewModelBasePropertyChanged;
            PropertyChanged += CustomerViewModelBasePropertyChanged;

            await base.InitializeAsync();
        }

        public virtual async Task OnCustomerChangedAsync(string customerId)
        {
            await LocalStorageService.SetItemAsync(storageKey, customerId);
        }

        private async void CustomerViewModelBasePropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e?.PropertyName == nameof(CurrentCustomerId))
                await OnCustomerChangedAsync(CurrentCustomerId);
        }
    }
}