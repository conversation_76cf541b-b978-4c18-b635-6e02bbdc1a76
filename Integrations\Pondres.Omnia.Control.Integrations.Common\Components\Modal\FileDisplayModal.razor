﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common

<ModalWindow @ref="Window">
    <ModalHeader>
        <h5 class="modal-title" id="fileModalLabel">@FileDetails.FileName</h5>
        <button type="button" class="close" aria-label="Close" @onclick="CloseAsync">
            <span aria-hidden="true">&times;</span>
        </button>
    </ModalHeader>
    <ModalBody>
        @if (HasFileContent)
        {
            <pre class="m-0" style="max-height: 65vh;">
                <code>@FileDetails.Contents</code>
            </pre>
        }
        else
        {
            <LoadingSpinner Show=@(!HasFileContent) />
        }
    </ModalBody>
    <ModalFooter>
        <button type="button" class="btn btn-primary" @onclick="CopyContentsAsync">Copy to clipboard</button>
        <button type="button" class="btn btn-secondary" @onclick="CloseAsync">Close</button>
    </ModalFooter>
</ModalWindow>
