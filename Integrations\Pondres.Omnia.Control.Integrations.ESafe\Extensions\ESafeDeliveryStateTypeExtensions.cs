﻿using Pondres.Omnia.ESafe.Contracts.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Extensions;

public static class ESafeDeliveryStateTypeExtensions
{
    public static string GetStatusColorStyle(this ESafeDeliveryStateType state) => state switch
    {
        ESafeDeliveryStateType.Active => "table-info",
        ESafeDeliveryStateType.Completed => "table-success-light",
        ESafeDeliveryStateType.Warning => "table-warning",
        _ => "table-light"
    };

    public static string GetStatusString(this ESafeDeliveryStateType state, ESafeDeliveryResultType result, bool? trackStatus = default) =>
        $"{state} ({result}){GetTrackStatusSuffix(trackStatus)}";

    private static string GetTrackStatusSuffix(bool? trackStatus = default) =>
        trackStatus.HasValue && !trackStatus.Value ? " - no tracking" : "";
}
