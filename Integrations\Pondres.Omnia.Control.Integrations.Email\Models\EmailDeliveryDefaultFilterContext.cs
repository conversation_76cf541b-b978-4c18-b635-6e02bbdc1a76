﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Email.Extensions;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Models
{
    public class EmailDeliveryDefaultFilterContext : QueryStringFilterContext<DeliveryData, EmailDeliveryDefaultFilterFields>, IEmailDeliveryFilterContext
    {
        private readonly IEmailDeliveryService deliveryService;

        public override string FilterTypeName => FilterType.Default.ToString();

        public EmailDeliveryDefaultFilterContext(
            NavigationManager navigationManager,
            IEmailDeliveryService deliveryService)
            : base(navigationManager)
        {
            this.deliveryService = deliveryService;
        }

        protected override async Task<PagedResultModel<DeliveryData>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
            await deliveryService.GetDeliveriesAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

        protected override void NavigateToFilter() =>
            navigationManager.NavigateToEmailDeliveryListWithDefaultFilter(Fields);
    }
}
