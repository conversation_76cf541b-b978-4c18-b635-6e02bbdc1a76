﻿@using Pondres.Omnia.SecureMail.Contracts.Api.Delivery

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Details
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table mb-0">
            <tbody>
                <tr>
                    <th>DeliveryId</th>
                    <td>@Delivery.DeliveryId</td>
                </tr>
                <tr>
                    <th>Connector</th>
                    <td>@(Delivery.Connector)</td>
                </tr>
                <tr>
                    <th>Reference</th>
                    <td>@Delivery.DeliveryMetadata.Reference</td>
                </tr>
                <tr>
                    <th>Customer</th>
                    <td>@Delivery.OrderMetadata.Customer</td>
                </tr>
                <tr>
                    <th>Created On</th>
                    <td>@Delivery.CreatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>Last Updated On</th>
                    <td>@Delivery.LastUpdatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>Order Reference</th>
                    <td>
                        @OrderDetailsLink
                    </td>
                </tr>
                <tr>
                    <th>Categories</th>
                    <td>@GetConcatinatedCategories()</td>
                </tr>
                <tr>
                    <th>Order Flow</th>
                    <td>@Delivery.OrderMetadata.Flow</td>
                </tr>
                <tr>
                    <th>Environment Name</th>
                    <td>@Delivery.DeliveryMetadata.EnvironmentName</td>
                </tr>
                <tr>
                    <th>Track Status</th>
                    <td>@(Delivery.DeliveryMetadata.TrackStatus ? "true" : "false")</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>