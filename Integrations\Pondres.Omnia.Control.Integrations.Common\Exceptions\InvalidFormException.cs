﻿using System.Runtime.Serialization;

namespace Pondres.Omnia.Control.Integrations.Common.Exceptions;

[Serializable]
public sealed class InvalidFormException : Exception
{
    public List<string> Errors { get; set; } = new List<string>();

    public InvalidFormException(List<string> errors) : base("Invalid form")
    {
        Errors = errors;
    }

    public InvalidFormException(List<string> errors, string message) : base(message)
    {
        Errors = errors;
    }

    public InvalidFormException(List<string> errors, string message, Exception innerException) : base(message, innerException)
    {
        Errors = errors;
    }
    private InvalidFormException(SerializationInfo info, StreamingContext context) : base(info, context) { }
}
