﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Email.Extensions;
using Pondres.Omnia.Control.Integrations.Email.Models;

namespace Pondres.Omnia.Control.Integrations.Email.Link
{
    public partial class BatchEmailDeliveryLink
    {
        [Inject]
        public IFilterCacheService FilterCacheService { get; set; }

        [Inject]
        public NavigationManager NavigationManager { get; set; }

        [Parameter]
        public string StyleClass { get; set; }

        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public IEnumerable<string> CustomerReferences { get; set; } = new List<string>();

        private async Task NavigateAsync()
        {
            var filterFields = new EmailDeliveryBatchFilterFields { CustomerReferences = CustomerReferences.ToList() };
            var filterId = await FilterCacheService.SaveFilterFieldsAsync(filterFields);
            NavigationManager.NavigateToEmailDeliveryListWithBatchFilter(filterId);
        }
    }
}
