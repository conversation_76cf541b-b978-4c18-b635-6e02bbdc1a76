@using Newtonsoft.Json

@inherits ModalBase

<TelerikDialog Visible="@IsVisible" Width="750px" Class="bundle-print-files-result" ShowCloseButton="false">
    <DialogTitle>
        <strong>Raw</strong>
    </DialogTitle>
    <DialogContent>
        <pre>@JsonConvert.SerializeObject(JsonConvert.DeserializeObject(Model.RawJson), Formatting.Indented)</pre>
    </DialogContent>
    <DialogButtons>
        <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Close</TelerikButton>
    </DialogButtons>
</TelerikDialog>
