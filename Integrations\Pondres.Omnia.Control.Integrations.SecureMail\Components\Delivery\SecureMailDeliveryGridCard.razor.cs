using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.SecureMail.Services;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery;

public partial class SecureMailDeliveryGridCard
{

    [Inject]
    NavigationManager NavigationManager { get; set; }

    [Inject]
    ISecureMailDeliveryService SecureMailService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public Guid OrderId { get; set; }

    private void NavigateToDelivery(string deliveryId) => NavigationManager.NavigateTo($"SecureMail/{Customer}/{deliveryId}");
    private DeliveryListFilter CreateFilter(string continuationToken) => new()
    { ContinuationToken = continuationToken, Customer = Customer, OrderId = OrderId };
}
