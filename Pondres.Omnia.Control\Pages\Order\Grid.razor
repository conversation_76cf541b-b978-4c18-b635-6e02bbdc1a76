﻿@page "/Orders"

@using Pondres.Omnia.Control.Integrations.Common.Components.Buttons
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Link
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Common.Extensions
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.ESafe.Components.Link
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Order
@using Pondres.Omnia.Control.Integrations.OrderHub.Models
@using Pondres.Omnia.Control.Integrations.Print.Components.Link
@using Pondres.Omnia.Control.Integrations.SecureMail.Components.Link
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Link
@using Pondres.Omnia.OrderHub.Contracts.Api.Order
@using static Telerik.Blazor.ThemeConstants.Button
@using Telerik.SvgIcons

@inherits CustomerFilterGridPageBase<OrderListItem, IOrderFilterContext>

<PageHeader Title="Orders">
    <CustomerSelection Customers="@Customers" SelectedCustomerId="@CurrentCustomerId" CustomerChanged="OnCustomerChangedAsync" />
</PageHeader>
<PageBody>
    <div class="container-fluid">
        <OrdersGridFilter FilterChanged="OnFilterChangedAsync" Flows="FilterFlows" />

        <TableHeader AutoRefresh="true" Refresh="OnRefresh">
            <AuthorizeView Roles="ControlContributor, ControlOwner">
                <ActionListButton Title="Cancel order?"
                                  ButtonText="Cancel"
                                  ThemeColor="@ThemeColor.Warning"
                                  StyleClass="float-right"
                                  Actions="CancelOrdersAsync"
                                  OnCompletion="OnRefresh"
                                  Disabled="!DataGrid.SelectedItems.Any() || DataGrid.SelectedItems.Any(x => !x.Actions.Contains(OrderActionType.Cancel.ToString()))">
                    You are about to cancel <b>@DataGrid.SelectedItems.Count()</b> order(s), continue?
                </ActionListButton>

                <ActionListButton Title="Retry order?"
                                  ButtonText="Retry"
                                  ThemeColor="@ThemeColor.Primary"
                                  StyleClass="float-right"
                                  Actions="RetryOrdersAsync"
                                  OnCompletion="OnRefresh"
                                  Disabled="!DataGrid.SelectedItems.Any() || DataGrid.SelectedItems.Any(x => !x.Actions.Contains(OrderActionType.Retry.ToString()))">
                    You are about to retry <b>@DataGrid.SelectedItems.Count()</b> order(s), continue?
                </ActionListButton>

                <ActionListButton Title="Start order?"
                                  ButtonText="Start"
                                  ThemeColor="@ThemeColor.Primary"
                                  StyleClass="float-right"
                                  Actions="StartOrdersAsync"
                                  OnCompletion="OnRefresh"
                                  Disabled="!DataGrid.SelectedItems.Any() || DataGrid.SelectedItems.Any(x => !x.Actions.Contains(OrderActionType.Start.ToString()))">
                    You are about to start <b>@DataGrid.SelectedItems.Count()</b> order(s), continue?
                </ActionListButton>

                <ActionListButton Title="Reject Order?"
                                  ButtonText="Reject"
                                  ThemeColor="@ThemeColor.Warning"
                                  StyleClass="float-right"
                                  Actions="RejectOrdersAsync"
                                  OnCompletion="OnRefresh"
                                  Disabled="!DataGrid.SelectedItems.Any() || DataGrid.SelectedItems.Any(x => !x.Actions.Contains(OrderActionType.Reject.ToString()))">
                    You are about to reject <b>@DataGrid.SelectedItems.Count()</b> order(s), continue?
                    <hr />
                    <div class="col-12">
                        <label for="errorCode" class="pr-4 col-form-label">Error code (optional):</label>
                        <input type="text" id="errorCode" class="form-control border-1 small" placeholder="920" name="errorCode" @bind="RejectionErrorCode">
                    </div>
                    <div class="col-12">
                        <label for="errorReason" class="pr-4 col-form-label">Reason (optional):</label>
                        <input type="text" id="errorReason" class="form-control border-1 small" placeholder="Order was rejected manually" name="errorReason" @bind="RejectionErrorReason">
                    </div>
                </ActionListButton>

                <ActionListButton Title="Retry Failed Tasks?"
                                  ButtonText="Retry Failed Tasks"
                                  ThemeColor="@ThemeColor.Primary"
                                  StyleClass="float-right"
                                  Actions="RetryFailedOrderTasksAsync"
                                  OnCompletion="OnRefresh"
                                  Disabled="!DataGrid.SelectedItems.Any() || DataGrid.SelectedItems.Any(x => !x.Actions.Contains(OrderActionType.RetryFailedTasks.ToString()))">
                    You are about to retry the failed tasks on <b>@DataGrid.SelectedItems.Count()</b> order(s), continue?
                </ActionListButton>
            </AuthorizeView>

            <GotoDropdown Disabled="!HasSelectedItems" StyleClass="float-right mx-1">
                <BatchPrintDocumentLink OrderIds="SelectedUniqueOrderIds" StyleClass="dropdown-item">Print Documents</BatchPrintDocumentLink>
                <BatchWarehouseDocumentLink OrderIds="SelectedUniqueOrderIds" StyleClass="dropdown-item">Warehouse Documents</BatchWarehouseDocumentLink>
                <BatchSecureMailDeliveryLink CustomerReferences="SelectedUniqueCustomerReferences" StyleClass="dropdown-item">Secure Mail Deliveries</BatchSecureMailDeliveryLink>
                <BatchESafeDeliveryLink CustomerReferences="SelectedUniqueCustomerReferences" StyleClass="dropdown-item">E-Safe Deliveries</BatchESafeDeliveryLink>
            </GotoDropdown>
        </TableHeader>

        <TelerikGrid Data="@GridItems" @ref="DataGrid" Class="grid-no-scroll"
                     OnRowDoubleClick="@OnRowDoubleClick"
                     SelectedItemsChanged="@(async (IEnumerable<OrderListItem> orders) => await InvokeAsync(StateHasChanged))"
                     SelectionMode="GridSelectionMode.Multiple">
            <GridExport>
                <GridCsvExport FileName="Orders" OnBeforeExport="@OnBeforeCsvExport" />
            </GridExport>
            <GridToolBarTemplate>
                <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
                <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
            </GridToolBarTemplate>
            <GridColumns>
                <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
                <SelectableGridColumn Field="@(nameof(OrderListItem.OrderId))" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                    <TitleFragment>
                        Order @SelectionCounterDisplayName
                    </TitleFragment>
                    <Template>
                        @((context as OrderListItem).OrderId.Shorten())
                    </Template>
                </SelectableGridColumn>
                <SelectableGridColumn Field="@(nameof(OrderListItem.CustomerReference))" Title="Customer Reference" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
                <SelectableGridColumn Field="@(nameof(OrderListItem.Status))" Title="Status" IsEnabled="false"
                                      SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                    <Template>
                        @{
                            var order = (context as OrderListItem);
                        }
                        @(order.Status.StateTypeName) (@(order.Status.ResultTypeName))
                    </Template>
                </SelectableGridColumn>
                <SelectableGridColumn Field="@(nameof(OrderListItem.Flow))" Title="Flow" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
                <SelectableGridColumn Field="@(nameof(OrderListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                                      SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
                <GridCommandColumn Width="90px" OnCellRender="@ApplyBackground">
                    <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand">Details</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
        </TelerikGrid>
    </div>
</PageBody>
