﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@inherits ModalBase

<TelerikDialog @bind-Visible="IsVisible" Width="600px">
    <DialogTitle>
        <strong>Edit secret details</strong>
    </DialogTitle>
    <DialogContent>
        <TelerikForm Model="@SecretModel" OnValidSubmit="SaveSecretModelAsync"
                     ButtonsLayout="FormButtonsLayout.End">
            <FormValidation>
                <DataAnnotationsValidator />
            </FormValidation>
            <FormItems>
                <FormItem Field="@nameof(SecretModel.CustomerId)" LabelText="Customer" Enabled="false" />
                <FormItem Field="@nameof(SecretModel.SecretName)" LabelText="Secret Name" Enabled="@IsNew" />
                <FormItem Field="@nameof(SecretModel.SecretValue)" LabelText="Secret Value" />
            </FormItems>
            <FormButtons>
                <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Save</TelerikButton>
                <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
</TelerikDialog>