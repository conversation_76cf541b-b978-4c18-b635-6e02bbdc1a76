﻿@using Pondres.Omnia.OrderHub.Contracts.Api.Order
@using static Telerik.Blazor.ThemeConstants.Button

<ActionModal @ref="CancelOrderModal"
             T="OrderFullInformation"
             OnSubmit="CancelOrderAsync"
             OnClose="RefreshPageAsync"
             Title="Cancel order?"
             ButtonText="Cancel"
             ButtonType="@ThemeColor.Warning">
    You are about to cancel order <b>@Order.OrderId</b>, Continue?
</ActionModal>

<ActionModal @ref="RetryOrderModal"
             T="OrderFullInformation"
             OnSubmit="RetryOrderAsync"
             OnClose="RefreshPageAsync"
             Title="Retry order?"
             ButtonText="Retry"
             ButtonType="@ThemeColor.Primary">
    You are about to retry order <b>@Order.OrderId</b>, Continue?
</ActionModal>

<ActionModal @ref="StartOrderModal"
             T="OrderFullInformation"
             OnSubmit="StartOrderAsync"
             OnClose="RefreshPageAsync"
             Title="Start order?"
             ButtonText="Start"
             ButtonType="@ThemeColor.Primary">
    You are about to start order <b>@Order.OrderId</b>, Continue?
</ActionModal>

<ActionModal @ref="RejectOrderModal" T="OrderRejectModel" Context="rejectModel" 
             OnSubmit="RejectOrderAsync" OnClose="RefreshPageAsync"
             Title="Reject order?" ButtonText="Reject" ButtonType="danger">
    You are about to reject order <b>@Order.OrderId</b>, Continue?
    <hr />
    <div class="col-12">
        <label for="errorCode" class="pr-4 col-form-label">Error code (optional):</label>
        <input type="text" id="errorCode" class="form-control border-1 small" placeholder="920" name="errorCode" @bind="rejectModel.ErrorCode">
    </div>
    <div class="col-12">
        <label for="errorReason" class="pr-4 col-form-label">Reason (optional):</label>
        <input type="text" id="errorReason" class="form-control border-1 small" placeholder="Order was rejected manually" name="errorReason" @bind="rejectModel.ErrorReason">
    </div>
</ActionModal>

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <h6 class="m-0 font-weight-bold text-primary pt-2">Status</h6>
            <AuthorizeView Roles="ControlContributor, ControlOwner">
                <div class="float-right">
                    @if (Order.Actions.Contains(OrderActionType.Cancel.ToString()))
                    {
                        <button class="btn btn-danger mx-1 float-right" @onclick="() => CancelOrderModal.OpenModal(Order)">Cancel</button>
                    }
                    @if (Order.Actions.Contains(OrderActionType.Start.ToString()))
                    {
                        <button class="btn btn-primary mx-1 float-right" @onclick="() => StartOrderModal.OpenModal(Order)">Start</button>
                    }
                    @if (Order.Actions.Contains(OrderActionType.Retry.ToString()))
                    {
                        <button class="btn btn-primary mx-1 float-right" @onclick="() => RetryOrderModal.OpenModal(Order)">Retry</button>
                    }
                    @if (Order.Actions.Contains(OrderActionType.Reject.ToString()))
                    {
                        <button class="btn btn-danger mx-1 float-right" @onclick="() => RejectOrderModal.OpenModal(new OrderRejectModel())">Reject</button>
                    }
                </div>
            </AuthorizeView>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table">
            <tbody>
                <tr class="@Order.Status.ToStatusColorClass()">
                    <th>Status</th>
                    <td>@Order.Status.StateTypeName</td>
                </tr>
                <tr>
                    <th>Result</th>
                    <td>@Order.Status.ResultTypeName</td>
                </tr>
                <tr>
                    <th>Last update</th>
                    <td>@Order.Status.UpdatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>Attempts</th>
                    <td>@Order.Status.AttemptCount</td>
                </tr>
                @if (!string.IsNullOrWhiteSpace(Order.Status.Message))
                {
                    <tr>
                        <th>Message</th>
                        <td>@Order.Status.Message</td>
                    </tr>
                }
            </tbody>
        </table>
    </CardBody>
</TelerikCard>

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Details
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table">
            <tbody>
                <tr>
                    <th>@nameof(Order.OrderId)</th>
                    <td>@Order.OrderId</td>
                </tr>
                <tr>
                    <th>@nameof(Order.CustomerReference)</th>
                    <td>@Order.CustomerReference</td>
                </tr>
                <tr>
                    <th>@nameof(Order.Customer)</th>
                    <td>@Order.Customer</td>
                </tr>
                <tr>
                    <th>@nameof(Order.DataContainerName)</th>
                    <td>@Order.DataContainerName</td>
                </tr>
                <tr>
                    <th>@nameof(Order.CreatedOn)</th>
                    <td>@Order.CreatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>@nameof(Order.OrderFlow)</th>
                    <td>@Order.OrderFlow</td>
                </tr>
                <tr>
                    <th>@nameof(Order.Categories)</th>
                    <td>@GetConcatinatedCategories()</td>
                </tr>
                <tr>
                    <th>@nameof(Order.OriginRequestId)</th>
                    <td>@Order.OriginRequestId</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>