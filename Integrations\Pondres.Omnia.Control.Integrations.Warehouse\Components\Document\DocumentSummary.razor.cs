using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;

public partial class DocumentSummary
{
    [Inject]
    NavigationManager NavigationManager { get; set; }

    [Parameter]
    public WarehouseDocumentInformation Document { get; set; }

    private void NavigateToOrder(string customer, string orderId) => 
        NavigationManager.NavigateTo($"Orders/{customer}/{orderId}");
}
