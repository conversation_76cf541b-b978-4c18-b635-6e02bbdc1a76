﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters

<ListFilter Filter="Filter" FilterChanged="() => FilterChanged.InvokeAsync(Filter)">

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="Order Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.CustomerReference" LabelText="Customer Reference" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.Status" LabelText="Status" Options="statusTypes" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.IntersolveOrderId" LabelText="Intersolve Order Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.ProductOwnerNr" LabelText="Product Owner Nr" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="Created on" />
    </ListFilterRow>

    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />

</ListFilter>


