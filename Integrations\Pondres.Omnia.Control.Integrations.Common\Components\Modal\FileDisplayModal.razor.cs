﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Modal;

public partial class FileDisplayModal
{
    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    private IClipboardManager ClipboardManager { get; set; }

    private ModalWindow Window { get; set; }

    private bool HasFileContent => !FileDetails?.Contents.IsNullOrEmpty() ?? false;

    private FileModel FileDetails { get; set; }

    public async Task OpenAsync(FileModel fileDetails)
    {
        FileDetails = fileDetails;

        await Window.OpenAsync();
    }

    public async Task CopyContentsAsync()
    {
        try
        {
            await ClipboardManager.CopyToClipboardAsync(FileDetails?.Contents);
            ToastService.ShowSuccess("Copied to clipboard");
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    public async Task CloseAsync()
    {
        FileDetails = null;

        await Window.CloseAsync();
    }
}