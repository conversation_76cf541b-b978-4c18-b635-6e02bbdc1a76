﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Print;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddPrintServices(this IServiceCollection services, AppSettings appSettings)
    {
        services.AddHttpClient(name: "printClient")
            .ConfigureBaseAddress(appSettings.PrintServiceUri)
            .ConfigureAuthenticationToken(appSettings.PrintServiceAuthToken);

        services.AddHttpClient<PrintServiceClient>("printClient");
        services.AddTransient<IPrintService, PrintService>();

        services.AddHttpClient<IScalerPrintService, ScalerPrintService>()
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                // Temporary fix, remove this once certificates are fixed:
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                return handler;
            });

        services.AddTransient<PrintBatchDefaultFilterContext>();
        services.AddTransient<PrintBundleBatchFilterContext>();
        services.AddTransient<PrintBundleDefaultFilterContext>();
        services.AddTransient<PrintDocumentBatchFilterContext>();
        services.AddTransient<PrintDocumentDefaultFilterContext>();

        return services;
    }
}
