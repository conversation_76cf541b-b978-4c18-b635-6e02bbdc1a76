﻿@page "/WMS/Documents/{Customer}/{DocumentId}"
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Document
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Response

<PageHeader Title="@($"Warehouse document details")" />
<PageBody>
    <div class="container-fluid row">
        <LoadingSpinner Show="IsLoading"></LoadingSpinner>
        @if (!IsLoading && Document == null)
        {
            <div class="alert alert-danger" role="alert">
                Could not retrieve warehouse document "@Document.DocumentId"
            </div>
        }
        else if (!IsLoading)
        {
            <div class="col-xl-6 col-12">

                <DocumentSummary Document="Document" />

                <ResponseLastInsertSummary Document="Document" />

            </div>

            <div class="col-xl-6 col-12">

                <DocumentLastStatusSummary Document="Document" />

                <ResponseStatusGrid Responses="Document.Responses" />

                <DocumentArticlesGrid ArticleCodes="Document.ArticleCodes" />

            </div>
        }
    </div>
</PageBody>