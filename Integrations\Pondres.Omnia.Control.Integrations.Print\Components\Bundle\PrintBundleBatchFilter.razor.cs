using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Bundle;

public partial class PrintBundleBatchFilter
{
    private EditContext EditContext;

    [Parameter]
    public PrintBundleBatchFilterContext Filter { get; set; }

    [Parameter]
    public EventCallback<PrintBundleBatchFilterContext> FilterChanged { get; set; }

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
        EditContext.OnFieldChanged += OnFormFieldChanged;
        base.OnInitialized();
    }

    private async void OnFormFieldChanged(object sender, FieldChangedEventArgs e)
    {
        EditContext.MarkAsUnmodified();
        await FilterChanged.InvokeAsync(Filter);
    }

    private async Task ClearFilterAsync()
    {
        await Filter.ResetAsync();
        await FilterChanged.InvokeAsync(Filter);
    }
}
