﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Extensions
@using Pondres.Omnia.SecureMail.Contracts.Api.Delivery

<GridCard Title="Secure mail deliveries"
          GetRawColorFunc="(delivery) => delivery.StateType.GetStatusColorStyle()"
          GetItemsAsync="async(continuationToken) => await SecureMailService.GetAllDeliveriesByFilterAsync(CreateFilter(continuationToken))">
    <TableHeaderContent>
        <th>Id</th>
        <th>Status</th>
        <th>Updated On</th>
    </TableHeaderContent>
    <TableRowContent>
        <td><a class="link" @onclick="() => NavigateToDelivery(context.Id)" @onclick:stopPropagation="true">@context.Id.Shorten()</a></td>
        <td>@context.StateType.GetStatusString(context.ResultType, context.TrackStatus)</td>
        <td>@context.LastUpdatedOn.LocalDateTime</td>
    </TableRowContent>
</GridCard>


