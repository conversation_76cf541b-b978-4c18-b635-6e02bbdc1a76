﻿@using Pondres.Omnia.Control.Integrations.Common.Extensions

@if (!BundleTotals.Days.Any())
{
    <h4 class="mb-0">No data found</h4>
}
else
{
    <table class="table table-hover table-bordered mb-0">
        <thead class="thead-light">
            <tr>
                <th scope="col">Date</th>
                <th scope="col">Week</th>
                <th scope="col" colspan="@BundleTotals.TotalsPerHour.Count">Hours</th>
                <th scope="col">Total</th>
                <th scope="col">Week Total</th>
            </tr>
            <tr>
                <th scope="col" colspan="2"></th>
                @foreach (var hour in AllHours)
                {
                    <th scope="col">@FormatHour(hour)</th>
                }
                <th scope="col" colspan="2"></th>
            </tr>
        </thead>
        <tbody>
            @foreach (var day in AllDays)
            {
                var totalsPerDay = BundleTotals.Days.SingleOrDefault(x => x.Date == day.Date);

                if (totalsPerDay == null)
                {
                    <tr class="@GetRowClassForDay(day)">
                        <th scope="row">@day.ToShortDateString()</th>
                        <th scope="row">
                            @if (day.DayOfWeek == DayOfWeek.Monday)
                            {
                                <span>@day.ToIso8601WeekOfYear()</span>
                            }
                        </th>
                        @foreach (var hour in AllHours)
                        {
                            <td></td>
                        }
                        <th class="table-primary"></th>
                        <th>
                            @if (day.DayOfWeek == DayOfWeek.Monday && BundleTotals.TotalsPerWeek.ContainsKey(@day.ToIso8601WeekOfYear().ToString()))
                            {
                                <span>@FormatTotalsItem(BundleTotals.TotalsPerWeek[@day.ToIso8601WeekOfYear().ToString()])</span>
                            }
                        </th>
                    </tr>
                }
                else
                {
                    <tr class="@GetRowClassForDay(day)">
                        <th scope="row">@totalsPerDay.Date.LocalDateTime.ToShortDateString()</th>
                        <th scope="row">
                            @if (day.DayOfWeek == DayOfWeek.Monday)
                            {
                                <span>@totalsPerDay.WeekNumber</span>
                            }
                        </th>
                        @foreach (var hour in AllHours)
                        {
                            if (totalsPerDay.Hours.ContainsKey(hour.ToString()))
                            {
                                <td>@FormatTotalsItem(totalsPerDay.Hours[hour.ToString()])</td>
                            }
                            else
                            {
                                <td></td>
                            }
                        }
                        <th class="table-primary">@FormatTotalsItem(totalsPerDay.Totals)</th>
                        <th>
                            @if (day.DayOfWeek == DayOfWeek.Monday)
                            {
                                <span>@FormatTotalsItem(BundleTotals.TotalsPerWeek[totalsPerDay.WeekNumber.ToString()])</span>
                            }
                        </th>
                    </tr>
                }
            }
            <tr>
                <th scope="col" colspan="2">Totaal</th>
                @foreach (var hour in AllHours)
                {
                    <th>
                        @FormatTotalsItem(BundleTotals.TotalsPerHour[hour.ToString()])
                    </th>
                }
                <th class="table-primary">@FormatTotalsItem(OverallTotal)</th>
                <th></th>
            </tr>
        </tbody>
    </table>
}


