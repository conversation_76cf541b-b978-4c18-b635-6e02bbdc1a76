﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Buttons
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Print.Client
@using Pondres.Omnia.Control.Integrations.Print.Components.Link
@using static Telerik.Blazor.ThemeConstants.Button
@using Telerik.SvgIcons

@inherits SelectableGridBase<PrintDocumentListItem>

<div class="card shadow mb-4">
    <PrintDocumentReprintSelectedModal @ref="PrintDocumentReprintSelectedDialog" OnSaveAction="@ReprintDocumentsAsync" />

    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <GotoDropdown StyleClass="float-right mx-1" Disabled="!HasSelectedItems">
            <BatchPrintBundleLink BundleIds="SelectedUniqueDocumentBundleIds" StyleClass="dropdown-item">Bundles</BatchPrintBundleLink>
        </GotoDropdown>
        <AuthorizeView Roles="PrintContributor, ControlOwner">
            <TelerikButton ThemeColor="@ThemeColor.Success" OnClick="OnReprintConfirmationCommand"
                           Enabled="SelectedDocumentsCanBeConfirmed">Reprint</TelerikButton>
        </AuthorizeView>
    </TableHeader>

    <TelerikGrid Data="@Documents" @ref="DataGrid" Class="grid-no-scroll"
                 OnRowDoubleClick="@OnRowDoubleClick"
                 SelectedItemsChanged="@(async (IEnumerable<PrintDocumentListItem> items) => await InvokeAsync(StateHasChanged))"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="Documents" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@OnCellRender" />
            <SelectableGridColumn Field="OrderMetadata.Flow" SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender">
                <TitleFragment>
                    Flow @(HasSelectedItems ? $"({DataGrid.SelectedItems.Count()})" : null)
                </TitleFragment>
            </SelectableGridColumn>
            <SelectableGridColumn Field="Id" Title="Document Id" SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender" />
            <SelectableGridColumn Field="CustomerDocumentReference" Title="Document Reference"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender" />

            <SelectableGridColumn Field="@nameof(PrintDocumentListItem.LastBundle.BundleId)" Title="Batch Name" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender">
                <Template>
                    @if ((context as PrintDocumentListItem).LastBundle != null)
                    {
                        <SinglePrintBundleListLink BundleId="@((context as PrintDocumentListItem).LastBundle.BundleId.ToString())">@((context as PrintDocumentListItem).LastBundle.BatchName)</SinglePrintBundleListLink>
                    }
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@nameof(PrintDocumentListItem.OrderMetadata.OrderId)" Title="Order Reference" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender">
                <Template>
                    @OrderDetailsLink(context as PrintDocumentListItem)
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@nameof(PrintDocumentListItem.LastBundle.GordNumber)" Title="Gord Number" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender">
                <Template>
                    @if ((context as PrintDocumentListItem).LastBundle != null)
                    {
                        <SinglePrintBundleListLink BundleId="@((context as PrintDocumentListItem).LastBundle.GordNumber)">@((context as PrintDocumentListItem).LastBundle.GordNumber)</SinglePrintBundleListLink>
                    }
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@nameof(PrintDocumentListItem.LastBundle.TaskNumber)" Title="Task Number" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender">
                <Template>
                    @if (((context as PrintDocumentListItem).LastBundle != null) && ((context as PrintDocumentListItem).LastBundle.TaskNumber > 0))
                    {
                        <SinglePrintBundleListLink BundleId="@((context as PrintDocumentListItem).LastBundle.TaskNumber.ToString())">@((context as PrintDocumentListItem).LastBundle.TaskNumber)</SinglePrintBundleListLink>
                    }
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@nameof(PrintDocumentListItem.LastBundle.SequenceId)" Title="Sequence Id" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender">
                <Template>
                    @if (((context as PrintDocumentListItem).LastBundle != null) && ((context as PrintDocumentListItem).LastBundle.SequenceId > 0))
                    {
                        <SinglePrintBundleListLink BundleId="@((context as PrintDocumentListItem).LastBundle.SequenceId.ToString())">@((context as PrintDocumentListItem).LastBundle.SequenceId)</SinglePrintBundleListLink>
                    }
                </Template>
            </SelectableGridColumn>

            <SelectableGridColumn Field="@(nameof(PrintDocumentListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@OnCellRender" />
        </GridColumns>
    </TelerikGrid>
</div>