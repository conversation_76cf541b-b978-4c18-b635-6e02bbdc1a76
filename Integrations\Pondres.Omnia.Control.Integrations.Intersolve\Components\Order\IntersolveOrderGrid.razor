﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Buttons
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using static Telerik.Blazor.ThemeConstants.Button
@using Pondres.Omnia.Control.Integrations.Intersolve.Client
@using Telerik.SvgIcons

@inherits SelectableGridBase<IntersolveOrderListItem>

<TableHeader AutoRefresh="true" Refresh="OnRefresh">
    @* Add actions here *@
</TableHeader>

<TelerikGrid Data="@Orders" @ref="DataGrid" Class="grid-no-scroll"
             OnRowDoubleClick="@OnRowDoubleClick"
             SelectedItemsChanged="@(async (IEnumerable<IntersolveOrderListItem> items) => await InvokeAsync(StateHasChanged))"
             SelectionMode="GridSelectionMode.Multiple">
    <GridExport>
        <GridCsvExport FileName="IntersolveOrders" OnBeforeExport="@OnBeforeCsvExport" />
    </GridExport>
    <GridToolBarTemplate>
        <GridCommandButton Command="CsvExport" Icon="SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
        <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
    </GridToolBarTemplate>
    <GridColumns>
        <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.FlowName))" Title="Flow Name"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
            <Template>
                @(GetFlowOrderTypesString(context as IntersolveOrderListItem))
            </Template>
        </SelectableGridColumn>

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.OrderId))" Title="Order Id"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.CustomerReference))" Title="Customer Reference"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.ProductOwnerNr))" Title="Product Owner Number"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.IntersolveOrderId))" Title="Intersolve Order Id"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field=@(nameof(IntersolveOrderListItem.CustomerReference)) Title="Customer Reference" Width="300px"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.Status))" Title="Status" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <SelectableGridColumn Field="@(nameof(IntersolveOrderListItem.LastUpdatedOn))" Title="Updated On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />

        <GridCommandColumn Width="90px" OnCellRender="@ApplyBackground">
            <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand">Details</GridCommandButton>
        </GridCommandColumn>
    </GridColumns>
</TelerikGrid>