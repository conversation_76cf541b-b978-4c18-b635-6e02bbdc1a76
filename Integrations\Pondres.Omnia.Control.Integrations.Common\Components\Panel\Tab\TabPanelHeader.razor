﻿@typeparam TDataType

<ul class="nav nav-@(NavigationType)">
	@if (TitleHasBeenSet())
	{
		<a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">@Title</a>
	}
	@foreach (var tab in Panel.Tabs)
	{
		<li class="nav-item" role="presentation">
			<button class="nav-link @(Panel.IsSelected(tab) ? "active" : "")"
				id="@(tab.Name)-tab"
				type="button"
				role="tab"
				aria-controls="@tab.Name"
				aria-selected="@(Panel.IsSelected(tab) ? "true" : "false")"
				@onclick="x => Panel.SelectTabAsync(tab)">@tab.Name</button>
		</li>
	}
</ul>

