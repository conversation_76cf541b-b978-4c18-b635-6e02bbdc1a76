﻿using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions;

public static class PrintBundleListItemExtensions
{
    public static string GetStatusColorStyle(this PrintBundleListItem bundle)
    {
        if (bundle.Status.Message == PrintBundleStateType.Completed.ToString()) // completed
            return "table-success-light";
        else if (bundle.Status.IsInFailedState)
            return "table-danger";
        else if (bundle.Status.WaitingForInput)
            return "table-warning";
        else
            return "table-info";
    }

    public static string ToPrintFolderPath(this PrintBundleListItem bundle, string basePath) =>
        $"{basePath}\\{bundle.Customer}\\{bundle.BatchName}\\{bundle.Metadata.SheetArticleCode}";
}
