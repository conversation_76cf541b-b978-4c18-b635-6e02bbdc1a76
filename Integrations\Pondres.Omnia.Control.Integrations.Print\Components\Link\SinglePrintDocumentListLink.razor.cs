using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Link;

public partial class SinglePrintDocumentListLink
{
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public string DocumentId { get; set; }

    private void Navigate()
    {
        var filter = new PrintDocumentDefaultFilterFields { DocumentId = DocumentId };
        NavigationManager.NavigateToPrintDocumentListWithDefaultFilter(filter);
    }
}
