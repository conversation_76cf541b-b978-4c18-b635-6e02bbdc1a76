﻿@using Pondres.Omnia.Control.Integrations.Email.Extensions
@using Pondres.Omnia.Email.Contracts.Delivery
@using Telerik.Blazor.Components

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <h6 class="m-0 font-weight-bold text-primary pt-2">Current status</h6>
            <div class="float-right">
                <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Enabled="(Delivery.Result == EmailDeliveryResultType.Bounced)"
                               OnClick="@HandleBouncedAsync">Complete</TelerikButton>
            </div>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table mb-0">
            <tbody>
                <tr class="@Delivery.State.GetStatusColorStyle()">
                    <th>@nameof(Delivery.State)</th>
                    <td>@Delivery.State.GetStatusString(Delivery.Result, Delivery.TrackStatus)</td>
                </tr>
                <tr>
                    <th>@nameof(Delivery.DeliveryRequestMessage)</th>
                    <td>@Delivery.DeliveryRequestMessage</td>
                </tr>
                <tr>
                    <th>@nameof(Delivery.LastCheckStarted)</th>
                    <td>@(Delivery.LastCheckStarted.HasValue ? Delivery.LastCheckStarted.Value.LocalDateTime : "N/A")</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>


