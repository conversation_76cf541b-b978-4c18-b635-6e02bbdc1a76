﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Extensions
@using Pondres.Omnia.Control.Integrations.Print.Components.Link


<GridCard Title="Print documents"
          GetRawColorFunc="@((document) => document.GetStatusColorStyle())"
          GetItemsAsync="async(continuationToken) => await PrintService.GetAllDocumentsForOrderIdAsync(OrderId, continuationToken)">
    <TableHeaderContent>
        <th>Id</th>
        <th>Batch name</th>
        <th>Document reference</th>
        <th>Updated on</th>
    </TableHeaderContent>
    <TableRowContent>
        <td><SinglePrintDocumentListLink DocumentId="@context.Id">@context.Id.Shorten()</SinglePrintDocumentListLink></td>
        <td>
            @if (context.LastBundle != null)
            {
                <SinglePrintBundleListLink BundleId="@context.LastBundle.BundleId.ToString()">@context.LastBundle?.BatchName</SinglePrintBundleListLink>
            }
        </td>
        <td>@context.CustomerDocumentReference</td>
        <td>@context.LastUpdatedOn.LocalDateTime</td>
    </TableRowContent>
</GridCard>


