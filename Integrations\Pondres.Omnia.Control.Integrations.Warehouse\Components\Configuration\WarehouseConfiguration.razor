﻿@using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration

<UpsertCustomerConfigurationModal @ref="ConfigurationModal" OnSubmit="OnModalSubmit" />

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary pt-2">Customer Configuration</h6>
            @if (ConfigurationModel != null)
            {
                <div class="float-right">
                    <button class="btn btn-primary" type="button" @onclick="OpenConfigurationEditModalAsync">
                        Edit Configuration
                    </button>
                </div>
            }
        </div>
    </div>
    @if (ConfigurationModel == null)
    {
        <div class="card-body">
            No configuration available for customer. <br />
            <button class="btn btn-success" type="button" @onclick="OpenConfigurationCreateModalAsync">
                Add Configuration
            </button>
        </div>
    }
    else
    {
        <div class="card-body">
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-6 col-xl-2 my-auto"><span class="font-weight-bold">Sender Address Mapping</span></div>
                    <input class="form-control col-6 col-xl-3" value="@ConfigurationModel.SenderAddressMapping" disabled />
                </div>
                <div class="mb-2">
                    <span class="font-weight-bold">Address Mappings</span>
                </div>
                @if (ConfigurationModel.AddressMappings.Count() == 0)
                {
                    <span>No address mappings available.</span>
                }
                else
                {
                    foreach (var mapping in ConfigurationModel.AddressMappings)
                    {
                        <div class="row mb-2">
                            <div class="col-3 my-auto col-xl-2">Agent Code:</div>
                            <input class="form-control col-3" value="@mapping.AgentCode" disabled />
                            <div class="col-3 my-auto col-xl-2">Sender Address:</div>
                            <input class="form-control col-3" value="@mapping.SenderAddress" disabled />
                        </div>
                    }
                }
            </div>
        </div>
    }
</div>
