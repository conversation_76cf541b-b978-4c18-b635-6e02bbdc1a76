﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.ESafe.Extensions;
using Pondres.Omnia.Control.Integrations.ESafe.Models;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.ESafe;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Grid : CustomerFilterGridPageBase<DeliveryListItem, IESafeDeliveryFilterContext>
{
    [Inject]
    protected IOrderService OrderService { get; set; }

    public List<string> FilterFlows { get; private set; }

    private void NavigateToDetails(string id) =>
        NavigationManager.NavigateTo($"ESafe/{CurrentCustomerId}/{id}");

    private void OnDetailsCommand(GridCommandEventArgs e) =>
         NavigateToDetails((e.Item as DeliveryListItem).Id.ToString());

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
         NavigateToDetails((e.Item as DeliveryListItem).Id.ToString());

    private async Task CopySelectedESafeConnectorsToClipboardAsync()
    {
        IEnumerable<string> SelectedESafeConnectors = DataGrid.SelectedItems.Select(bundle => bundle.Connector);
        var connectorsJoined = string.Join(Environment.NewLine, SelectedESafeConnectors);

        await ClipboardManager.CopyToClipboardAsync(connectorsJoined);

        ToastService.ShowInfo($"Copied {SelectedESafeConnectors.Count()} connectors to clipboard");
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await TryLoadFilterFlowsAsync();

        await LoadGridItemsAsync(refresh: true);
    }

    private async Task TryLoadFilterFlowsAsync()
    {
        try
        {
            FilterFlows?.Clear();
            FilterFlows = await OrderService.GetFlowsAsync(CurrentCustomerId);
        }
        catch
        {
            ToastService.ShowError($"Flows could not be loaded for customer {CurrentCustomerId}");
        }
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await TryLoadFilterFlowsAsync();

            await LoadGridItemsAsync(refresh: true);
        }
    }

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as DeliveryListItem).StateType.GetStatusColorStyle();
}