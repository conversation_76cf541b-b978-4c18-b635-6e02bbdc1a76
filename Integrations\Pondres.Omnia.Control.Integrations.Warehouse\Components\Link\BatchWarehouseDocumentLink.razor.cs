using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Warehouse.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Link;

public partial class BatchWarehouseDocumentLink
{
    [Inject]
    public IFilterCacheService FilterCacheService { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    [Parameter]
    public string StyleClass { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public IEnumerable<string> OrderIds { get; set; } = new List<string>();

    private async Task NavigateAsync()
    {
        var filterFields = new WarehouseDocumentBatchFilterFields { OrderIds = OrderIds.ToList() };
        var filterId = await FilterCacheService.SaveFilterFieldsAsync(filterFields);
        NavigationManager.NavigateToWarehouseDocumentListWithBatchFilter(filterId);
    }
}
