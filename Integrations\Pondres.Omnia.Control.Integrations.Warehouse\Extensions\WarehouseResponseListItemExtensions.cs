﻿using Pondres.Omnia.Warehouse.Contracts.Api.Response;
using Pondres.Omnia.Warehouse.Contracts.Response;
using System;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Extensions;

public static class WarehouseResponseListItemExtensions
{
    public static string GetStatusColorStyle(this WarehouseResponseListItem item) => item.StateType switch
    {
        ResponseStateType.Active => "table-primary",
        ResponseStateType.Completed => "table-success-light",
        ResponseStateType.Failed => "table-danger",
        _ => "table-light",
    };
}
