﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>


  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
		<PackageReference Include="BlazorMonaco" Version="3.3.0" />
		<PackageReference Include="JsonSchema.Net" Version="7.3.1" />
		<packageReference Include="Microsoft.Extensions.Http" version="9.0.1" />
		<packageReference Include="Microsoft.Extensions.Configuration.Abstractions" version="9.0.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Pondres.Common" Version="3.20221025.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
    <PackageReference Include="Pondres.Omnia.Demo.Contracts" Version="1.20240805.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
  </ItemGroup>

</Project>
