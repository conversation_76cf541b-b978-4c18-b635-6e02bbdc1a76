﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Control.Integrations.OrderHub.Extensions

@if (RetrieveFilesFailed)
{
    <div class="alert alert-danger" role="alert">
        Could not retrieve file data for "@Order.OrderId"
    </div>
}
else
{
    <TelerikCard>
        <CardHeader>
            <CardTitle>
                Files
            </CardTitle>
        </CardHeader>
        <CardBody>
            

            @if (FilesList != null)
            {
                <table class="table table-striped mb-0" id="files-table">
                    <tbody>
                        @foreach (var file in FilesList.Files)
                        {
                            <tr>
                                <td>@file.RelativePath</td>
                                <td>
                                    <div class="d-flex float-right">
                                        <button type="button" class="btn btn-primary view-file float-right mx-1"
                                    disabled="@file.DownloadOnly()"
                                    @onclick="() => OpenFileModalAsync(file.FullPath)">
                                            View
                                        </button>
                                        <button type="button" class="btn btn-primary text-white float-right mx-1"
                                    @onclick="() => DownloadFileAsync(file.FullPath, file.DownloadOnly())">
                                            Download
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
        </CardBody>
    </TelerikCard>

    <FileDisplayModal @ref="FileDetailsModal" />
}


