﻿@page "/Configuration/ArticleMappings"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Constants
@using Pondres.Omnia.Control.Integrations.Common.Models.Common
@using Pondres.Omnia.Control.Integrations.Common.Models.Filter
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Warehouse.Contracts.Api.ArticleMapping
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Configuration
@using Telerik.SvgIcons

@inherits SelectableCustomerGridPageBase<ArticleMapping>

<PageHeader Title="Warehouse article mappings">
    <CustomerSelection CustomerChanged="@OnCustomerChangedAsync" Customers="Customers" SelectedCustomerId="@CurrentCustomerId" />
</PageHeader>
<PageBody>
    <AuthorizeView Roles="ControlOwner">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <div class="d-flex justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Warehouse article mappings</h6>
                    <div class="float-right">
                        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnAddCommand">Add</TelerikButton>
                    </div>
                </div>
            </div>
            <TelerikTabStrip Class="m-2">
                <TabStripTab Title="Default">
                    <TelerikForm Model="@DefaultFilter"
                                 OnSubmit=@LoadArticleMappingsAsync
                                 Columns="3" ColumnSpacing="20px"
                                 ButtonsLayout="FormButtonsLayout.End">
                        <FormItems>
                            <FormItem LabelText="Article" Field="@nameof(ArticleMappingListFilter.Key)" />
                            <FormItem LabelText="Attachment Article" Field="@nameof(ArticleMappingListFilter.Article)" />
                            <FormItem Field="@nameof(ArticleMappingListFilter.Enabled)" />
                        </FormItems>
                        <FormButtons>
                            <label for="pagesizes" class="k-label k-form-label">Page size</label>
                            <TelerikDropDownList Id="pagesizes" @bind-Value="@DefaultFilter.MaxPageSize"
                                                 Data="@ControlConstants.PageSizes"
                                                 DefaultText="Page size" Width="120px">
                            </TelerikDropDownList>
                            <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Filter</TelerikButton>
                            <TelerikButton ButtonType="ButtonType.Button" OnClick="@LoadDefaultItemsAsync">Reset</TelerikButton>
                        </FormButtons>
                    </TelerikForm>
                </TabStripTab>
                <TabStripTab Title="Batch">
                    <TelerikForm Model="@BatchFilter"
                                 OnSubmit=@LoadArticleMappingsBatchAsync
                                 Columns="2" ColumnSpacing="20px"
                                 ButtonsLayout="FormButtonsLayout.End">
                        <FormItems>
                            <FormItem Field="@nameof(ArticleMappingBatchSelectionFilter.Keys)">
                                <Template>
                                    <label for="keys" class="k-label k-form-label">Articles</label>
                                    <TelerikTextArea Id="keys" @bind-Value="@BatchFilterKeys"
                                                     AutoSize="false" Class="batchfilterinput" />
                                </Template>
                            </FormItem>
                        </FormItems>
                        <FormButtons>
                            <label for="pagesizes" class="k-label k-form-label">Page size</label>
                            <TelerikDropDownList Id="pagesizes" @bind-Value="@BatchFilter.MaxPageSize"
                                                 Data="@ControlConstants.PageSizes"
                                                 DefaultText="Page size" Width="120px">
                            </TelerikDropDownList>
                            <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Filter</TelerikButton>
                            <TelerikButton ButtonType="ButtonType.Button" OnClick="@ResetBatchFilterAsync">Reset</TelerikButton>
                        </FormButtons>
                    </TelerikForm>
                </TabStripTab>
            </TelerikTabStrip>
        </div>

        <TelerikGrid Data=@ArticleMappingsList @ref="DataGrid" Class="grid-no-scroll mb-2"
                     SelectedItemsChanged="@(async (IEnumerable<ArticleMapping> articles) => await InvokeAsync(StateHasChanged))"
                     SelectionMode="GridSelectionMode.Multiple">
            <GridExport>
                <GridCsvExport FileName="ArticleMappings" OnBeforeExport="@OnBeforeCsvExport" />
            </GridExport>
            <GridToolBarTemplate>
                <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
                <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
            </GridToolBarTemplate>
            <GridColumns>
                <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" />
                <SelectableGridColumn Field="@(nameof(ArticleMapping.Article))" SelectionChanged="@OnColumnSelect" Title="Article" />
                <SelectableGridColumn Field="@(nameof(ArticleMapping.AttachmentArticle))" SelectionChanged="@OnColumnSelect" Title="Attachment Article" />
                <SelectableGridColumn Field="@(nameof(ArticleMapping.Enabled))" SelectionChanged="@OnColumnSelect" Title="Enabled" />
                <GridCommandColumn Width="90px" Context="mapping">
                    <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand" Command="Edit">Edit</GridCommandButton>
                </GridCommandColumn>
            </GridColumns>
        </TelerikGrid>

        <MappingEditor @ref="DialogEditor" OnSaveAction="@OnSaveAction" />
    </AuthorizeView>
</PageBody>