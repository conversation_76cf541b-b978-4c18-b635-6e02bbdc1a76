﻿@page "/Print/Dashboard"

@using Pondres.Omnia.Control.Integrations.Print.Components.Dashboard

<PageHeader Title="Print Dashboard" />
<PageBody>
    <div class="card shadow mb-4">
        <DashboardFilter Filter="Filter" FilterChanged="OnFilterChanged"></DashboardFilter>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary pt-2">Bundle Totals</h6>
            </div>
        </div>
        <div class="card-body">
            @if (Loading)
            {
                <LoadingSpinner />
            }
            else if (TotalsResult != null)
            {
                <DashboardTotalsView BundleTotals="TotalsResult" Filter="Filter"></DashboardTotalsView>
            }
        </div>
    </div>
</PageBody>


