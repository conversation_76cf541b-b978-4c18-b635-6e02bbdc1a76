using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Response;

public partial class ResponseStatusGrid
{
    [Parameter]
    public List<WarehouseDocumentResponseStatus> Responses { get; set; }

    private void NavigateToResponseDetails(string fileName) => navigationManager.NavigateTo($"/WMS/Responses/{fileName}/Details");
}
