﻿@inherits ModalBase

<TelerikDialog Width="500px"
               Visible="@IsVisible">
    <DialogTitle>
        <strong>Articlemapping details</strong>
    </DialogTitle>            
    <DialogContent>
        <TelerikForm Model="@ArticleMapping" 
                     OnValidSubmit="@SaveArticleMappingAsync"
                     ButtonsLayout="FormButtonsLayout.End">
            <FormValidation>
                <DataAnnotationsValidator />
            </FormValidation>
            <FormItems>
                <FormItem Field="@nameof(ArticleMapping.Customer)" Enabled="false" />
                <FormItem Field="@nameof(ArticleMapping.Article)" Enabled="@IsNew" />
                <FormItem Field="@nameof(ArticleMapping.AttachmentArticle)" LabelText="Attachment Article" />
                <FormItem Field="@nameof(ArticleMapping.Enabled)" />
            </FormItems>
            <FormButtons>
                <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Save</TelerikButton>
                <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
</TelerikDialog>