﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.ESafe.Models;
using Pondres.Omnia.Control.Integrations.ESafe.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.ESafe;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddESafeServices(this IServiceCollection services, AppSettings appSettings)
    {
        services.AddHttpClient<IESafeDeliveryService, ESafeDeliveryService>()
            .ConfigureBaseAddress(appSettings.ESafeServiceUri)
            .ConfigureAuthenticationToken(appSettings.ESafeServiceAuthToken);

        services.AddTransient<ESafeDeliveryBatchFilterContext>();
        services.AddTransient<ESafeDeliveryDefaultFilterContext>();

        return services;
    }
}
