﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters

<ListFilter Filter="Filter" FilterChanged="OnFilterChangedAsync">

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="Order Id" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.EventType" LabelText="Event Type" Options="eventTypes" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.Status" LabelText="Status" Options="statusTypes" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="created on" />
    </ListFilterRow>

    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />

</ListFilter>


