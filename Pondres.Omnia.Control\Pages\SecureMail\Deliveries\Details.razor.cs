using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;
using Pondres.Omnia.Control.Integrations.SecureMail.Services;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Pages.SecureMail.Deliveries;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Details
{
    [Inject]
    private ISecureMailDeliveryService Service { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public string DeliveryId { get; set; }

    private DeliveryDetails Delivery { get; set; }

    private bool PageLoading { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await RefreshPageAsync();
        }
    }

    private async Task RefreshPageAsync()
    {
        PageLoading = true;

        try
        {
            Delivery = await Service.GetDeliveryDetailsAsync(Customer, Guid.Parse(DeliveryId));
            PageLoading = false;
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    private List<SecureMailDeliveryTimeoutModel> CreateDeliveryTimeoutModels()
    {
        var result = new List<SecureMailDeliveryTimeoutModel>();

        for (var i = 0; i < Math.Max(Delivery.ExpectedTimeOuts.Count, Delivery.ReceivedTimeOuts.Count); i++)
        {
            var expectedTime = GetItemAtIndexOrNull(Delivery.ExpectedTimeOuts, i);
            var receivedTime = GetItemAtIndexOrNull(Delivery.ReceivedTimeOuts, i);

            result.Add(new SecureMailDeliveryTimeoutModel { ExpectedOn = expectedTime, ReceivedOn = receivedTime });
        }

        return result;
    }

    private static DateTimeOffset? GetItemAtIndexOrNull(List<DateTimeOffset> items, int index) =>
        index < items.Count ? items[index] : null;
}
