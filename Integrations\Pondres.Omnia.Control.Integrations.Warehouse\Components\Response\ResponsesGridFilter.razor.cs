using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Response;

public partial class ResponsesGridFilter
{
    [Inject] 
    IApiFilterManager FilterManager { get; set; }

    [Inject] 
    WarehouseResponseDefaultFilterContext DefaultFilter { get; set; }

    [Parameter]
    public EventCallback<IWarehouseResponseFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<IWarehouseResponseFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);
        await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(IWarehouseResponseFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);
        await FilterChanged.InvokeAsync(filter);
    }
}
