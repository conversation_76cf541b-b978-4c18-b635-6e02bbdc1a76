﻿namespace Pondres.Omnia.Control.Settings
{
    public class AppSettings
    {
        public string AppInsightsConnectionString { get; set; } = string.Empty;
        public string WebBasePath { get; set; } = "/";
        public string SessionStorageAccountName { get; set; } = string.Empty;
        public string OmniaEnvironment { get; set; } = string.Empty;
        public string PrintFolderPath { get; set; } = string.Empty;
        public string AzureSignalRConnectionString { get; set; } = string.Empty;
        public string RedisConnectionString { get; set; } = string.Empty;
        public string WebToOmniaServiceAuthToken { get; set; } = string.Empty;
        public string WebToOmniaServiceUri { get; set; } = string.Empty;
        public string IntersolveServiceUri { get; set; } = string.Empty;
        public string IntersolveServiceAuthToken { get; set; } = string.Empty;
        public string WarehouseServiceUri { get; set; } = string.Empty;
        public string WarehouseServiceAuthToken { get; set; } = string.Empty;
        public string SecureMailServiceAuthToken { get; set; } = string.Empty;
        public string SecureMailServiceUri { get; set; } = string.Empty;
        public string PrintServiceUri { get; set; } = string.Empty;
        public string PrintServiceAuthToken { get; set; } = string.Empty;
        public string CustomerServiceUri { get; set; } = string.Empty;
        public string CustomerServiceAuthToken { get; set; } = string.Empty;
        public string DemoServiceUri { get; set; } = string.Empty;
        public string DemoServiceAuthToken { get; set; } = string.Empty;
        public string EmailServiceUri { get; set; } = string.Empty;
        public string EmailServiceAuthToken { get; set; } = string.Empty;
        public string ESafeServiceUri { get; set; } = string.Empty;
        public string ESafeServiceAuthToken { get; set; } = string.Empty;
        public string NotificationServiceUri { get; set; } = string.Empty;
        public string NotificationServiceAuthToken { get; set; } = string.Empty;
        public string OpsGenieAlertsApiKeyReadOnly { get; set; } = string.Empty;
        public string OpsGenieScheduleApiKeyReadOnly { get; set; } = string.Empty;
        public string OpsGeniePlatformFilterId { get; set; } = string.Empty;
        public string OpsGenieCustomerFilterId { get; set; } = string.Empty;
        public string OpsGenieScheduleFilterId { get; set; } = string.Empty;
        public string OrderServiceUri { get; set; } = string.Empty;
        public string OrderServiceAuthToken { get; set; } = string.Empty;
    }
}
