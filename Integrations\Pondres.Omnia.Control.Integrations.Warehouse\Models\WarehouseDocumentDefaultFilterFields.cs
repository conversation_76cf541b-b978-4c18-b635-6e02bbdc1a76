﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Models;

public class WarehouseDocumentDefaultFilterFields : FilterFieldsBase<WarehouseDocumentDefaultFilterFields>
{
    public string OrderId { get; set; }
    public string DocumentReference { get; set; }
    public string StatusCode { get; set; }
    public string FileName { get; set; }
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
}
