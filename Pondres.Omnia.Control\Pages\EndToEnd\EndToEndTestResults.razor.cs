﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Demo.Services;
using Pondres.Omnia.Demo.Contracts.Api;
using Pondres.Omnia.Demo.Contracts.EndToEnd;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.EndToEnd
{
    [Authorize(Roles = "ControlReader, ControlContributor, ControlOwner")]
    partial class EndToEndTestResults
    {
        [Inject]
        private IEndToEndTestService Service { get; set; }

        public List<ApiTestRunResultItem> AvailableTestRunResults { get; set; } = new List<ApiTestRunResultItem>();
        public TelerikGrid<ApiTestRunResultItem> GridRef { get; set; }
        public bool Loading { get; set; }

        private static void ApplyBackground(GridCellRenderEventArgs eventArgs)
        {
            if (eventArgs.Item is not ApiTestRunResultItem resultItem)
                return;

            if (resultItem.Completed)
                eventArgs.Class = resultItem.Success ? "table-success-light" : "table-danger";
            else
                eventArgs.Class = "table-primary";
        }


        protected override async Task OnInitializedAsync()
        {
            await LoadAllAsync();
        }
        
        private async Task StartLoadingAsync()
        {
            Loading = true;
            await InvokeAsync(StateHasChanged);
        }

        private async Task StopLoadingAsync()
        {
            Loading = false;
            await InvokeAsync(StateHasChanged);
        }

        private async Task LoadAllAsync()
        {
            await StartLoadingAsync();
            AvailableTestRunResults = (await Service.GetTestRunsAsync(0, 999)).OrderByDescending(x => x.StartedOn).ToList();
            await StopLoadingAsync();
        }
    }
}
