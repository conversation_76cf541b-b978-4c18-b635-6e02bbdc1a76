using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.SecureMail.Extensions;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Components.Link;

public partial class BatchSecureMailDeliveryLink
{
    [Inject]
    public IFilterCacheService FilterCacheService { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    [Parameter]
    public string StyleClass { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public IEnumerable<string> CustomerReferences { get; set; } = new List<string>();

    private async Task NavigateAsync()
    {
        var filterFields = new SecureMailDeliveryBatchFilterFields { CustomerReferences = CustomerReferences.ToList() };
        var filterId = await FilterCacheService.SaveFilterFieldsAsync(filterFields);
        NavigationManager.NavigateToSecureMailDeliveryListWithBatchFilter(filterId);
    }
}
