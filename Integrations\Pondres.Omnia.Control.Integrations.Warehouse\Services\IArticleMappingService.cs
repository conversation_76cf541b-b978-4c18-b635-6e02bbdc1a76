﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Warehouse.Contracts.Api.ArticleMapping;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Services;

public interface IArticleMappingService
{
    Task<PagedResultModel<ArticleMapping>> LoadArticleMappingsAsync(ArticleMappingListFilter filter);

    Task<PagedResultModel<ArticleMapping>> LoadArticleMappingsAsync(ArticleMappingBatchSelectionFilter filter);

    Task CreateArticleMappingAsync(ArticleMapping mapping);

    Task UpdateArticleMappingAsync(ArticleMapping mapping);

    Task DisableArticleMappingAsync(ArticleMapping mapping);
}
