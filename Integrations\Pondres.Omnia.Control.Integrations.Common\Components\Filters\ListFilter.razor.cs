using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Filters;

public partial class ListFilter
{
    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public EventCallback FilterChanged { get; set; }

    [Parameter]
    public IFilterContext Filter { get; set; }

    private EditContext EditContext { get; set; }

    public async Task ClearFilterAsync()
    {
        await Filter.ResetAsync();
        await SubmitFilterAsync();
    }

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
        EditContext.OnFieldChanged += FilterFormOnFieldChanged;
    }

    private async void FilterFormOnFieldChanged(object sender, FieldChangedEventArgs e)
    {
        await SubmitChangedFilterAsync();
    }

    public async Task SubmitChangedFilterAsync()
    {
        EditContext.MarkAsUnmodified();
        await SubmitFilterAsync();
    }

    private async Task SubmitFilterAsync()
    {
        await FilterChanged.InvokeAsync(Filter);
    }
}
