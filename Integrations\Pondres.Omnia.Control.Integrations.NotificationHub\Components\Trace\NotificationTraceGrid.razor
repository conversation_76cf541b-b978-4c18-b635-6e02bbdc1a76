﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Buttons
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.NotificationHub.Contracts.Api.Trace
@using static Telerik.Blazor.ThemeConstants.Button
@using Telerik.SvgIcons

@inherits SelectableGridBase<NotificationTraceListItem>

<TableHeader AutoRefresh="true" Refresh="OnRefresh">
    @if (HasActionOfType(NotificationTraceAction.RetryFailedOutputs))
    {
        <AuthorizeView Roles="ControlContributor, ControlOwner">
            <ActionListButton Title="Retry notifications?"
                              ButtonText="Retry"
                              ThemeColor="@ThemeColor.Primary"
                              StyleClass="float-right"
                              Actions="RetryTraceFailedOutputsAsync"
                              OnCompletion="OnRefresh"
                              Disabled="!HasSelectedItems">
                You are about to retry <b>@DataGrid.SelectedItems.Count()</b> notification(s) failed outputs, continue?
            </ActionListButton>
        </AuthorizeView>
    }
</TableHeader>

<TelerikGrid Data="@Traces" @ref="DataGrid" Class="grid-no-scroll"
             OnRowDoubleClick="@OnRowDoubleClick"
             SelectedItemsChanged="@(async (IEnumerable<NotificationTraceListItem> items) => await InvokeAsync(StateHasChanged))"
             SelectionMode="GridSelectionMode.Multiple">
    <GridExport>
        <GridCsvExport FileName="NotificationTraces" OnBeforeExport="@OnBeforeCsvExport" />
    </GridExport>
    <GridToolBarTemplate>
        <GridCommandButton Command="CsvExport" Icon="SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
        <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
    </GridToolBarTemplate>
    <GridColumns>
        <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="@(nameof(NotificationTraceListItem.EventType))" Title="EventType" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="OrderMetadata.OrderId" Title="Order Id" IsEnabled="false"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
            <Template>
                @((context as NotificationTraceListItem).OrderMetadata.OrderId)
            </Template>
        </SelectableGridColumn>
        <SelectableGridColumn Field="OrderMetadata.CustomerReference" Title="Customer reference" Width="300px" IsEnabled="false"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="@(nameof(NotificationTraceListItem.SourceReference))" Title="Source reference"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="@(nameof(NotificationTraceListItem.Status))" Title="Status" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="@(nameof(NotificationTraceListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <SelectableGridColumn Field="@(nameof(NotificationTraceListItem.UpdatedOn))" Title="Updated On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}"
                              SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
        <GridCommandColumn Width="90px" OnCellRender="@ApplyBackground">
            <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand">Details</GridCommandButton>
        </GridCommandColumn>
    </GridColumns>
</TelerikGrid>