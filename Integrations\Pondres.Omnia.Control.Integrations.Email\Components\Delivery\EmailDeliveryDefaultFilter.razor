﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters
@using Pondres.Omnia.Email.Contracts.Delivery

<ListFilter Filter="Filter" FilterChanged="OnFilterChangedAsync">
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="Order Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.DeliveryId" LabelText="Delivery Id" />
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.CustomerReference" LabelText="Customer Reference" />              
        <ListFilterInputTemplate LabelText="State">
            <TelerikDropDownList Data="@Enum.GetNames(typeof(EmailDeliveryStateType)).ToList()" @bind-Value="Filter.Fields.StateTypeName" />
        </ListFilterInputTemplate>
        <ListFilterInputTemplate LabelText="Flow">
            <TelerikDropDownList Data="@Flows" @bind-Value="Filter.Fields.Flow" />
        </ListFilterInputTemplate>
    </ListFilterRow>
    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="Created on" />
    </ListFilterRow>
    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />
</ListFilter>