﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Json.Schema;
using System.Text.Json;
using Telerik.Blazor.Components;
using BlazorMonaco.Editor;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Extensions;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Configuration
{
    public partial class ReleaseSchedulesEditor : ModalBase
    {
        private string validationMessage;

        [Inject]
        IJSRuntime JS { get; set; }

        private StandaloneCodeEditor Editor { get; set; }

        private TelerikDialog Dialog { get; set; }

        public string ReleaseSchedule { get; set; }

        public bool IsNew { get; set; }

        public bool IsView { get; set; }

        [Parameter]
        public EventCallback OnSaveAction { get; set; }

        [Parameter]
        public EventCallback OnCloseAction { get; set; }

        public string ValidationMessage
        {
            get => validationMessage;
            set
            {
                validationMessage = value;
                Dialog.Refresh();
            }
        }

        public void ShowDialog(string releaseSchedule, bool isView, bool isNew = false)
        {
            IsNew = isNew;
            IsView = isView;
            ReleaseSchedule = releaseSchedule;
            ValidationMessage = null;

            IsVisible = true;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
                await JS.InvokeVoidAsync("monacoJsonSchema.setJsonSchema", ReleaseSchedule);

            await base.OnAfterRenderAsync(firstRender);
        }

        public async Task MonacoEditorInitializedAsync()
        {
            if (Editor != null)
            {
                await Editor.SetValue(ReleaseSchedule);
            }
        }

        public async Task SaveDialogAsync()
        {
            ReleaseSchedule = await Editor.GetValue();
            var schema = JsonSchema.FromText(ReleaseSchedule);
            var value = JsonSerializer.Deserialize<JsonElement>(ReleaseSchedule);
            var validationResult = schema.Evaluate(value, new EvaluationOptions { RequireFormatValidation = true });

            if (!validationResult.IsValid)
                ValidationMessage = validationResult.ErrorMessage();

            if (OnSaveAction.HasDelegate)
                await OnSaveAction.InvokeAsync();
        }

        public async Task CloseDialogAsync()
        {
            if (OnCloseAction.HasDelegate)
                await OnCloseAction.InvokeAsync();

            await Editor.SetValue("{\n\n}");

            ReleaseSchedule = null;
            ValidationMessage = null;

            IsVisible = false;

            await InvokeAsync(StateHasChanged);
        }

        private StandaloneEditorConstructionOptions EditorConstructionOptions(Editor editor) =>
            new()
            {
                AutomaticLayout = true,
                FixedOverflowWidgets = true,
                Language = "json",
                AutoIndent = "true",
                ReadOnly = IsView
            };
    }
}