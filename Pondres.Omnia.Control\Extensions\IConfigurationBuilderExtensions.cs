﻿using Azure.Identity;
using Serilog;

namespace Pondres.Omnia.Control.Extensions;

public static class IConfigurationBuilderExtensions
{
    public static IConfigurationBuilder AddKeyVaultIfAvailable(this IConfigurationBuilder builder)
    {
        var configuration = new ConfigurationBuilder().AddEnvironmentVariables().Build();
        var vaultName = configuration["VaultName"];

        if (string.IsNullOrWhiteSpace(vaultName))
        {
            Log.Warning("No 'VaultName' set, skipping key vault configuration");
            return builder;
        }

        Log.Information("Using vault {vaultName}", vaultName);

        return builder.AddAzureKeyVault(
            vaultUri: new Uri($"https://{vaultName}.vault.azure.net"),
            credential: new DefaultAzureCredential());
    }
}
