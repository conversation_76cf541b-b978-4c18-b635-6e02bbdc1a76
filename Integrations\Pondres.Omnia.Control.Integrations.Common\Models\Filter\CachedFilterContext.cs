﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Services;

namespace Pondres.Omnia.Control.Integrations.Common.Models.Filter;

public abstract class CachedFilterContext<TFilterResultType, TFilterFields> : FilterContextBase<TFilterResultType, TFilterFields>
    where TFilterFields : FilterFieldsBase<TFilterFields>, new()
{
    protected readonly IFilterCacheService filterCacheService;

    protected abstract void NavigateToFilter(string filterId);

    protected CachedFilterContext(NavigationManager navigationManager, IFilterCacheService filterCacheService)
        : base(navigationManager)
    {
        this.filterCacheService = filterCacheService;
    }

    public override async Task MergeFromCurrentUriAsync()
    {
        var filterId = FilterHelper.GetFilterIdFromUri(navigationManager.ToAbsoluteUri(navigationManager.Uri));

        if (!string.IsNullOrWhiteSpace(filterId))
        {
            var filter = await filterCacheService.GetFilterFieldsAsync<TFilterFields>(filterId);

            if (filter != null)
                Fields.Merge(filter);
        }
    }

    public override async Task NavigateToFilterAsync()
    {
        var filterId = await filterCacheService.SaveFilterFieldsAsync(Fields);

        NavigateToFilter(filterId);
    }
}
