﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common

<div class="card shadow h-100">
    <div class="card-body d-flex justify-content-center align-items-center flex-row flex-column p-0 pb-2 @BackgroundColor">
        <LoadingSpinner Show=@Loading></LoadingSpinner>
        @if (!Loading)
        {
            <h4>@HeaderText</h4>
            <h1 class="display-@Size">@Value</h1>
        }
    </div>
</div>

