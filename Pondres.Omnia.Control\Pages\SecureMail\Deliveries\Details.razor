﻿@page "/SecureMail/{Customer}/{DeliveryId}"

@using Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery

<PageHeader Title="@($"SecureMail Delivery {DeliveryId}")" />
<PageBody>
    <div class="container-fluid row">
        @if (PageLoading)
        {
            <LoadingSpinner />
        }
        else
        {
            <CascadingValue Value="Delivery">
                <div class="col-xl-6 col-12">
                    <DeliveryInformation />
                </div>
                <div class="col-xl-6 col-12">
                    <DeliveryCurrentStatus />

                    <DeliveryStatusHistory />
                    
                    <DeliveryTimeouts ExpectedTimeouts="CreateDeliveryTimeoutModels()"/>
                </div>
            </CascadingValue>
        }
    </div>
</PageBody>