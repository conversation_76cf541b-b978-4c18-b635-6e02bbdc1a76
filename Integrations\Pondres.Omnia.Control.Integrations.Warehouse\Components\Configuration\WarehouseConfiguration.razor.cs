using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Configuration;

public partial class WarehouseConfiguration
{
    [Parameter]
    public CustomerConfiguration ConfigurationModel { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public EventCallback OnModalSubmit { get; set; }

    private UpsertCustomerConfigurationModal ConfigurationModal { get; set; }

    private async Task OpenConfigurationCreateModalAsync()
    {
        await ConfigurationModal.OpenNewModalAsync(Customer);
    }

    private async Task OpenConfigurationEditModalAsync()
    {
        await ConfigurationModal.OpenEditModalAsync(ConfigurationModel);
    }
}
