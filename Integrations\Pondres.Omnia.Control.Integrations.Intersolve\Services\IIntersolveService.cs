﻿using Pondres.Omnia.Intersolve.Contracts.Configuration;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Services
{
    public interface IIntersolveService
    {
        Task<List<Configuration>> GetAllConfigurationsAsync();

        Task<Configuration> GetConfigurationDetailsAsync(string customerId);

        Task CreateConfigurationAsync(CreateConfiguration model);

        Task UpdateConfigurationAsync(UpdateConfiguration model, string customerId);
    }
}
