﻿using Pondres.Omnia.OrderHub.Contracts.Api.Flow;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Services
{
    public interface IFlowService
    {
        Task<List<OrderFlowDefinitionDetails>> LoadOrderFlowDefinitionsAsync(string customer);

        Task<UpsertFlowDefinitionResult> CreateOrderFlowDefinitionDetailsAsync(UpsertFlowDefinition orderFlowDefinition);

        Task<UpsertFlowDefinitionResult> UpdateOrderFlowDefinitionDetailsAsync(UpsertFlowDefinition orderFlowDefinition);

        Task DisableOrderFlowDefinitionDetailsAsync(string customer, string flowName);
    }
}