﻿using Json.Schema;

namespace Pondres.Omnia.Control.Extensions
{
    public static class JsonExtensions
    {
        public static string ErrorMessage(this EvaluationResults evaluationResults)
        {
            var errors = SearchErrors(evaluationResults);

            return errors.Any() ? string.Join(", ", errors) : "Validation failed";
        }

        public static List<string> ErrorMessages(this EvaluationResults evaluationResults)
        {
            return SearchErrors(evaluationResults);
        }

        private static List<string> SearchErrors(EvaluationResults evaluationResults)
        {
            var errors = new List<string>();

            errors.AddRange(evaluationResults.GetErrorsFromSingleEvalutionResult());

            if(evaluationResults.HasDetails)
            {
                foreach (var detail in  evaluationResults.Details)
                {
                    errors.AddRange(SearchErrors(detail));
                }                
            }

            return errors;
        }

        private static List<string> GetErrorsFromSingleEvalutionResult(this EvaluationResults evaluationResults)
        {
            if (evaluationResults.HasErrors)
            {
                return evaluationResults.Errors!.Select(c => $"{string.Join(".", evaluationResults.InstanceLocation.Select(d => d).ToList())}: {c.Value}").ToList();
            }

            return new List<string>();
        }

    }
}
