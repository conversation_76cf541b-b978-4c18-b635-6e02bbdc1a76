﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintDocumentDefaultFilterFields : FilterFieldsBase<PrintDocumentDefaultFilterFields>
{
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
    public string Customer { get; set; }
    public string DocumentId { get; set; }
    public string CustomerDocumentReference { get; set; }
    public string OrderId { get; set; }
    public string BundleId { get; set; }
    public string BatchName { get; set; }
    public string GordNumber { get; set; }
    public string TaskNumber { get; set; }
    public string SequenceId { get; set; }
    public string BarCode { get; set; }
}
