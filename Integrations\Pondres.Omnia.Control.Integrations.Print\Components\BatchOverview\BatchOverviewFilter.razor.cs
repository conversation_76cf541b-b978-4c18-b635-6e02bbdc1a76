﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.BatchOverview
{
    public partial class BatchOverviewFilter
    {
        [Parameter]
        public PrintBatchDefaultFilterContext Filter { get; set; }

        [Parameter]
        public EventCallback<PrintBatchDefaultFilterContext> FilterChanged { get; set; }

        private async Task OnFilterChangedAsync()
        {
            if (FilterChanged.HasDelegate)
                await FilterChanged.InvokeAsync(Filter);
        }
    }
}
