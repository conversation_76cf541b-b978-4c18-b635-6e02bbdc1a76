﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.SecureMail.Extensions;
using Pondres.Omnia.Control.Integrations.SecureMail.Services;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Models;

public class SecureMailDeliveryDefaultFilterContext : QueryStringFilterContext<DeliveryListItem, SecureMailDeliveryDefaultFilterFields>, ISecureMailDeliveryFilterContext
{
    private readonly ISecureMailDeliveryService deliveryService;

    public override string FilterTypeName => FilterType.Default.ToString();

    public SecureMailDeliveryDefaultFilterContext(
        NavigationManager navigationManager,
        ISecureMailDeliveryService deliveryService)
        : base(navigationManager)
    {
        this.deliveryService = deliveryService;
    }

    protected override async Task<PagedResultModel<DeliveryListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await deliveryService.GetAllDeliveriesByFilterAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToSecureMailDeliveryListWithDefaultFilter(Fields);
}
