﻿@page "/Email/Deliveries"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Components.Link
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Email.Components.Delivery
@using Pondres.Omnia.Control.Integrations.Email.Extensions
@using Pondres.Omnia.Control.Integrations.Email.Models
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.Email.Contracts.Api.Delivery

@inherits CustomerFilterGridPageBase<DeliveryData, IEmailDeliveryFilterContext>
@using Telerik.SvgIcons

<PageHeader Title="E-Mail Deliveries">
    <CustomerSelection CustomerChanged="@OnCustomerChangedAsync" SelectedCustomerId="@CurrentCustomerId" Customers="Customers" />
</PageHeader>
<PageBody>
    <EmailDeliveryGridFilter FilterChanged="OnFilterChangedAsync" Flows="@FilterFlows" />

    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <GotoDropdown Disabled="!HasSelectedItems" StyleClass="float-right mx-1">
            <BatchOrderLink CustomerReferences="DataGrid.SelectedItems.Select(x => x.OrderMetadata.CustomerReference).Distinct()" StyleClass="dropdown-item">Deliveries</BatchOrderLink>
        </GotoDropdown>
        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Enabled="HasBouncedDeliveries"
                       OnClick="HandleBouncedAsync">Complete</TelerikButton>
    </TableHeader>

    <TelerikGrid Data="@GridItems" @ref="DataGrid" Class="grid-no-scroll"
                 SelectedItemsChanged="@(async (IEnumerable<DeliveryData> deliveries) => await InvokeAsync(StateHasChanged))"
                 OnRowDoubleClick="@OnRowDoubleClick"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="EmailDeliveries" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryData.DeliveryId))" Title="Id" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryData.OrderMetadata.CustomerReference))" Title="Customer reference" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    @((context as DeliveryData).OrderMetadata.CustomerReference)
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@(nameof(DeliveryData.Reference))" Title="Reference" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="@(nameof(DeliveryData.State))" Title="Status" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    @((context as DeliveryData).State.GetStatusString((context as DeliveryData).Result,(context as DeliveryData).TrackStatus))
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@(nameof(DeliveryData.OrderMetadata.Flow))" Title="Flow" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <Template>
                    @((context as DeliveryData).OrderMetadata.Flow)
                </Template>
            </SelectableGridColumn>
            <SelectableGridColumn Field="@(nameof(DeliveryData.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <GridCommandColumn Width="90px" OnCellRender="@ApplyBackground">
                <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand">Details</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>
</PageBody>