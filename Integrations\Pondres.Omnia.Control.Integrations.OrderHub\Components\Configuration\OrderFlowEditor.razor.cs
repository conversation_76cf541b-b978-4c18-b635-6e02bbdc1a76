﻿using BlazorMonaco.Editor;
using Json.Schema;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Pondres.Omnia.Control.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using System.Text.Json;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Configuration;

public partial class OrderFlowEditor : ModalBase
{
    private string validationMessage;

    [Inject]
    private IJSRuntime JS { get; set; }

    private StandaloneCodeEditor Editor { get; set; }

    private TelerikDialog Dialog { get; set; }

    public string DefinitionDetails { get; private set; }

    public bool IsNew { get; private set; }

    [Parameter]
    public EventCallback OnSaveAction { get; set; }

    [Parameter]
    public EventCallback OnCloseAction { get; set; }

    public string ValidationMessage
    {
        get => validationMessage;
        set
        {
            validationMessage = value;
            Dialog.Refresh();
        }
    }

    public void ShowDialog(string definitionDetails, bool isNew = false)
    {
        IsNew = isNew;
        DefinitionDetails = definitionDetails;
        ValidationMessage = null;

        IsVisible = true;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await JS.InvokeVoidAsync("monacoJsonSchema.setJsonSchema", DefinitionDetails);

        await base.OnAfterRenderAsync(firstRender);
    }

    public async Task MonacoEditorInitializedAsync()
    {
        if (Editor != null)
        {
            await Editor.SetValue(DefinitionDetails);
        }
    }

    public async Task SaveDialogAsync()
    {
        DefinitionDetails = await Editor.GetValue();
        var schema = JsonSchema.FromText(DefinitionDetails);
        var value = JsonSerializer.Deserialize<JsonElement>(DefinitionDetails);
        var validationResult = schema.Evaluate(value, new EvaluationOptions { RequireFormatValidation = true });

        if (!validationResult.IsValid)
            ValidationMessage = validationResult.ErrorMessage();

        if (OnSaveAction.HasDelegate)
            await OnSaveAction.InvokeAsync();
    }

    public async Task CloseDialogAsync()
    {
        if (OnCloseAction.HasDelegate)
            await OnCloseAction.InvokeAsync();

        await Editor.SetValue("{\n\n}");

        DefinitionDetails = null;
        ValidationMessage = null;

        IsVisible = false;

        await InvokeAsync(StateHasChanged);
    }

    private static StandaloneEditorConstructionOptions EditorConstructionOptions(Editor editor) =>
        new()
        {
            AutomaticLayout = true,
            FixedOverflowWidgets = true,
            Language = "json",
            AutoIndent = "true",
        };
}