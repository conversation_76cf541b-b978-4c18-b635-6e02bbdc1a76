{"openapi": "3.0.1", "info": {"title": "Pondres.Omnia.Intersolve.Api", "version": "1.0"}, "servers": [{"url": "/intersolve"}], "paths": {"/configurations": {"post": {"tags": ["Configuration"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateConfiguration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateConfiguration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateConfiguration"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateConfiguration"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Configuration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Configuration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Configuration"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "get": {"tags": ["Configuration"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Configuration"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Configuration"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Configuration"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/configurations/{customer}": {"get": {"tags": ["Configuration"], "parameters": [{"name": "customer", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Configuration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Configuration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Configuration"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["Configuration"], "parameters": [{"name": "customer", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "patch": {"tags": ["Configuration"], "parameters": [{"name": "customer", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateConfiguration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateConfiguration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateConfiguration"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateConfiguration"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Configuration"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Configuration"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Configuration"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/configurations/{customer}/fulfillmentApiCredentialKeys": {"post": {"tags": ["Configuration"], "parameters": [{"name": "customer", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/configurations/{customer}/fulfillmentApiCredentialKeys/{type}": {"put": {"tags": ["Configuration"], "parameters": [{"name": "customer", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/IntersolveProductType"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateFulfillmentApiCredentialKeys"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateFulfillmentApiCredentialKeys"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateFulfillmentApiCredentialKeys"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateFulfillmentApiCredentialKeys"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["Configuration"], "parameters": [{"name": "customer", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/IntersolveProductType"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "409": {"description": "Conflict", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/order/details": {"get": {"tags": ["IntersolveOrder"], "parameters": [{"name": "productOwnerNr", "in": "query", "schema": {"type": "string"}}, {"name": "customer", "in": "query", "schema": {"type": "string"}}, {"name": "id", "in": "query", "schema": {"type": "string"}}, {"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntersolveOrderDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderDetails"}}}}, "404": {"description": "Not Found"}}}}, "/order/pagedList": {"post": {"tags": ["IntersolveOrder"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListFilter"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListFilter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListFilter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListFilter"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListItemPagedList"}}}}}}}, "/order/pagedBatchList": {"post": {"tags": ["IntersolveOrder"], "parameters": [{"name": "x-token", "in": "header", "description": "Access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderBatchListFilter"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderBatchListFilter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderBatchListFilter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderBatchListFilter"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListItemPagedList"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListItemPagedList"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IntersolveOrderListItemPagedList"}}}}}}}}, "components": {"schemas": {"Configuration": {"type": "object", "properties": {"customer": {"type": "string", "nullable": true}, "intersolveTradeApiEndpoint": {"type": "string", "nullable": true}, "intersolveTradeApiUsernameKey": {"type": "string", "nullable": true}, "intersolveTradeApiPasswordKey": {"type": "string", "nullable": true}, "fulfillmentApiCredentialKeys": {"type": "array", "items": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}, "nullable": true}, "productOwnerNr": {"type": "integer", "format": "int32"}, "vaultName": {"type": "string", "nullable": true}, "flow": {"type": "string", "nullable": true}, "eTag": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateConfiguration": {"type": "object", "properties": {"customer": {"type": "string", "nullable": true}, "intersolveTradeApiEndpoint": {"type": "string", "nullable": true}, "intersolveTradeApiUsernameKey": {"type": "string", "nullable": true}, "intersolveTradeApiPasswordKey": {"type": "string", "nullable": true}, "fulfillmentApiCredentialKeys": {"type": "array", "items": {"$ref": "#/components/schemas/FulfillmentApiCredentialKeys"}, "nullable": true}, "productOwnerNr": {"type": "integer", "format": "int32"}, "flow": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeliveryMetadata": {"type": "object", "properties": {"customerType": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FulfillmentApiCredentialKeys": {"type": "object", "properties": {"productType": {"$ref": "#/components/schemas/IntersolveProductType"}, "endpoint": {"type": "string", "nullable": true}, "usernameKey": {"type": "string", "nullable": true}, "passwordKey": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "IntersolveOrderArticle": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "value": {"type": "number", "format": "double"}, "currencyCode": {"type": "string", "nullable": true}, "orderLineId": {"type": "integer", "format": "int32"}, "packageCode": {"type": "string", "nullable": true}, "markedAsCompleted": {"type": "boolean"}, "cards": {"type": "array", "items": {"$ref": "#/components/schemas/IntersolveOrderCard"}, "nullable": true}, "extItemReference": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntersolveOrderBatchListFilter": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "maxPageSize": {"type": "integer", "format": "int32"}, "customer": {"type": "string", "nullable": true}, "customerReferences": {"type": "array", "items": {"type": "string"}, "nullable": true}, "orderIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "IntersolveOrderCard": {"type": "object", "properties": {"cardNr": {"type": "string", "nullable": true}, "expirationDate": {"type": "string", "nullable": true}, "trackInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntersolveOrderDetails": {"type": "object", "properties": {"createdOn": {"type": "string", "format": "date-time"}, "lastUpdatedOn": {"type": "string", "format": "date-time"}, "orderMetadataDefinition": {"$ref": "#/components/schemas/OrderMetadata"}, "customer": {"type": "string", "nullable": true}, "deliveryMetadata": {"$ref": "#/components/schemas/DeliveryMetadata"}, "privateCustomerNr": {"type": "string", "nullable": true}, "orderTypes": {"type": "array", "items": {"$ref": "#/components/schemas/IntersolveOrderType"}, "nullable": true}, "intersolveArticles": {"type": "array", "items": {"$ref": "#/components/schemas/IntersolveOrderArticle"}, "nullable": true}, "status": {"$ref": "#/components/schemas/IntersolveOrderStatus"}, "statusHistory": {"type": "array", "items": {"$ref": "#/components/schemas/IntersolveOrderStatus"}, "nullable": true}, "productOwnerNr": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntersolveOrderListFilter": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "maxPageSize": {"type": "integer", "format": "int32"}, "createdFromDate": {"type": "string", "format": "date-time", "nullable": true}, "createdToDate": {"type": "string", "format": "date-time", "nullable": true}, "customerReference": {"type": "string", "nullable": true}, "orderId": {"type": "string", "nullable": true}, "intersolveOrderId": {"type": "string", "nullable": true}, "productOwnerNr": {"type": "string", "nullable": true}, "customer": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntersolveOrderListItem": {"type": "object", "properties": {"createdOn": {"type": "string", "format": "date-time"}, "lastUpdatedOn": {"type": "string", "format": "date-time"}, "customerReference": {"type": "string", "nullable": true}, "flowName": {"type": "string", "nullable": true}, "orderId": {"type": "string", "nullable": true}, "customer": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "productOwnerNr": {"type": "string", "nullable": true}, "intersolveOrderId": {"type": "string", "nullable": true}, "orderTypes": {"type": "array", "items": {"$ref": "#/components/schemas/IntersolveOrderType"}, "nullable": true}}, "additionalProperties": false}, "IntersolveOrderListItemPagedList": {"type": "object", "properties": {"continuationToken": {"type": "string", "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/IntersolveOrderListItem"}, "nullable": true}}, "additionalProperties": false}, "IntersolveOrderStatus": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntersolveOrderType": {"enum": ["None", "Card", "Digital"], "type": "string"}, "IntersolveProductType": {"enum": ["Physical", "Digital"], "type": "string"}, "OrderCategories": {"type": "object", "properties": {"one": {"type": "string", "nullable": true}, "two": {"type": "string", "nullable": true}, "three": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderMetadata": {"type": "object", "properties": {"customer": {"type": "string", "nullable": true}, "flow": {"type": "string", "nullable": true}, "customerReference": {"type": "string", "nullable": true}, "orderId": {"type": "string", "format": "uuid"}, "categories": {"$ref": "#/components/schemas/OrderCategories"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "UpdateConfiguration": {"type": "object", "properties": {"customer": {"type": "string", "nullable": true}, "intersolveTradeApiEndpoint": {"type": "string", "nullable": true}, "intersolveTradeApiUsernameKey": {"type": "string", "nullable": true}, "intersolveTradeApiPasswordKey": {"type": "string", "nullable": true}, "productOwnerNr": {"type": "integer", "format": "int32"}, "flow": {"type": "string", "nullable": true}, "eTag": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateFulfillmentApiCredentialKeys": {"type": "object", "properties": {"endpoint": {"type": "string", "nullable": true}, "usernameKey": {"type": "string", "nullable": true}, "passwordKey": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}}}}