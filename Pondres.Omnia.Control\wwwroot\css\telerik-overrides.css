﻿/*Telerik Grid*/
.grid-no-scroll .k-grid-content {
    overflow-y: auto;
    min-height: 50px;
}

.grid-no-scroll {
    min-width: 800px;
}

.grid-no-scroll .k-grid-header,
.grid-no-scroll .k-grid-footer {
    padding-right: 0; /* version 2.26 and older requires !important here */
}

.grid-no-scroll .k-grid-header-wrap,
.grid-no-scroll .k-grid-footer-wrap {
    border-right-width: 0;
}

.k-grid-header {
    font-weight: bold;
}

.k-master-row {
    height: 50px;
}

.k-grid-table td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/*Overrrides the selected row color when applicable*/
.k-grid tr.k-state-selected td.table-primary,
.k-grid tr.k-alt.k-state-selected td.table-primary {
    background-color: #b8daff;
}

.k-grid tr.k-state-selected td.table-info,
.k-grid tr.k-alt.k-state-selected td.table-info {
    background-color: #bee5eb;
}

.k-grid tr.k-state-selected td.table-success-light,
.k-grid tr.k-alt.k-state-selected td.table-success-light {
    background-color: #daffe2;
}

.k-grid tr.k-state-selected td.table-warning,
.k-grid tr.k-alt.k-state-selected td.table-warning {
    background-color: #ffeeba;
}

.k-grid tr.k-state-selected td.table-danger,
.k-grid tr.k-alt.k-state-selected td.table-danger {
    background-color: #f5c6cb;
}

.k-grid tr.k-state-selected td.table-light,
.k-grid tr.k-alt.k-state-selected td.table-light {
    background-color: #fdfdfe;
}

.k-button-solid-primary {
    border-color: #1b6ec2;
    background-color: #1b6ec2;
}

    .k-button-solid-primary:hover, .k-button-solid-primary.k-hover {
        border-color: #155595;
        background-color: #165ba0;
    }

    .k-button-solid-primary:focus, .k-button-solid-primary.k-focus {
        box-shadow: 0 0 0px 2px rgba(27, 110, 194, 0.5);
    }

    .k-button-solid-primary:active, .k-button-solid-primary.k-active {
        border-color: #134e8a;
        background-color: #155595;
    }

    .k-button-solid-primary.k-selected {
        border-color: #134e8a;
        background-color: #155595;
    }

/*Telerik Card*/
.k-card {
    margin-bottom: 1.5rem;
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: .25rem;
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
    overflow: visible;
}

.k-card-header {
    padding: .75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
    overflow: visible;
}

.k-card-title {
    color: #007bff;
    font-weight: 700;
    justify-content: space-between;
    display: -ms-flexbox;
    display: flex;
}

.k-card-body {
    padding: 0px;
}

.large-scale {
    scale: 1;
}

.k-badge {
    font-size: 14px;
}