using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Document;

public partial class DocumentsGridFilter
{
    [Inject]
    IApiFilterManager FilterManager { get; set; }

    [Inject]
    PrintDocumentBatchFilterContext BatchFilter { get; set; }

    [Inject]
    PrintDocumentDefaultFilterContext DefaultFilter { get; set; }

    [Parameter]
    public EventCallback<IPrintDocumentFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilter, BatchFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<IPrintDocumentFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);
        await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(IPrintDocumentFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);
        await FilterChanged.InvokeAsync(filter);
    }
}
