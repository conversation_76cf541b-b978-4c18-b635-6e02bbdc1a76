﻿@page "/Notifications/TraceDetails/{Customer}/{TraceId}"

@using Pondres.Omnia.Control.Integrations.Common.Components
@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link

@inherits ControlPageBase

<PageHeader Title="Audit Trace Details" />
<PageBody>
    <div class="container-fluid row">
        <LoadingSpinner Show="LoadingTraces" />

        @if (InvalidResponse)
        {
            <div class="alert alert-danger" role="alert">
                Could not retrieve notification audit trace with TraceId "@TraceId"
            </div>
        }

        @if (!LoadingTraces && !InvalidResponse)
        {
            <div class="col-xl-6 col-12">
                <NotificationTraceSummary OnFileOpened="async (fileName) => await OpenFileModalAsync(fileName)"
                                      OnFileDownloaded="async (fileName) => await DownloadFileAsync(fileName)"
                                      Trace="Trace"
                                      Customer="@Customer"
                                      TraceId="@TraceId"
                                      OnActionHandled="RefreshPageAsync">
                    <OrderDetailsLink>
                        <OrderDetailsLink OrderId="@Trace.OrderMetadata.OrderId?.ToString()" Customer="@Customer">@Trace.OrderMetadata.OrderId</OrderDetailsLink>
                    </OrderDetailsLink>
                </NotificationTraceSummary>

            </div>

            <div class="col-xl-6 col-12">
                <NotificationTraceOutputGrid OnFileOpened="async (fileName) => await OpenFileModalAsync(fileName)"
                                         OnFileDownloaded="async (fileName) => await DownloadFileAsync(fileName)"
                                         Outputs="Trace.Outputs"
                                         TraceId="@TraceId"
                                         Customer="@Customer" />

            </div>

            <FileDisplayModal @ref="FileDetailsModal" />
        }
    </div>
</PageBody>