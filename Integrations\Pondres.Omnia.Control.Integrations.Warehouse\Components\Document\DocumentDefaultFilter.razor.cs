using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;

public partial class DocumentDefaultFilter
{
    [Parameter]
    public EventCallback<WarehouseDocumentDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public WarehouseDocumentDefaultFilterContext Filter { get; set; }

    private async void OnFilterChanged()
    {
        await FilterChanged.InvokeAsync(Filter);
    }
}
