﻿using Pondres.Omnia.WebToOmnia.Contracts.Api.Configuration;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.Web2Omnia.Extensions
{
    public static class CustomerConfigurationDtoExtensions
    {
        public static CreateCustomerConfigurationDto ToCreateDto(this string json) =>
            JsonSerializer.Deserialize<CreateCustomerConfigurationDto>(json);

        public static UpdateCustomerConfigurationDto ToUpdateDto(this string json) =>
            JsonSerializer.Deserialize<UpdateCustomerConfigurationDto>(json);

        public static string ToJson(this CustomerConfigurationDto configuration) =>
            JsonSerializer.Serialize(configuration, new JsonSerializerOptions { WriteIndented = true });

        public static string ToJson(this CreateCustomerConfigurationDto configuration) =>
            JsonSerializer.Serialize(configuration, new JsonSerializerOptions { WriteIndented = true });
    }
}
