﻿namespace Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;

public static class StringExtensions
{
    public static string ToCapitalizedFirstName(this string name) =>
        char.ToUpper(name[0]) + name[1..];

    public static string ToAlertFilter(this string filterId) =>
        $"v2/alerts?searchIdentifier={filterId}&limit=100";

    public static string ToScheduleFilter(this string filterId) =>
        $"v2/schedules/{filterId}/on-calls?flat=true";
}
