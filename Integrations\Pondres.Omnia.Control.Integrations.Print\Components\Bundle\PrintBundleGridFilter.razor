﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

<TabPanel TDataType="IPrintBundleFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IPrintBundleFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IPrintBundleFilterContext">
                <TabPanelTab TDataType="IPrintBundleFilterContext" Name="Default" Data="DefaultFilter"
                             Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <PrintBundleDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
                <TabPanelTab TDataType="IPrintBundleFilterContext" Name="Batch" Data="BatchFilter"
                             Selected="FilterManager.CurrentFilter == BatchFilter">
                    <Template>
                        <PrintBundleBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>
