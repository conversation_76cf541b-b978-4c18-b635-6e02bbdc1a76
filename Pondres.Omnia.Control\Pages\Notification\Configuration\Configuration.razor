﻿@page "/Configuration/Notification"

@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration

@inherits CustomerPageBase

<PageHeader Title="Notification Configuration">
    <CustomerSelection CustomerChanged="@OnCustomerChangedAsync" SelectedCustomerId="@CurrentCustomerId" Customers="Customers" />
</PageHeader>
<PageBody>
    <AuthorizeView Roles="ControlOwner">
        <div class="container-fluid">

            <LoadingSpinner Show="IsLoading" />

            @if (!IsLoading)
            {
                <CascadingValue Value="@MappingContext">
                    <NotificationMappingGrid OnConfigurationChanged="LoadConfigurationAsync" CustomerId="@CurrentCustomerId" />

                    <NotificationDefinitionGrid OnDefinitionEdit="LoadConfigurationAsync" CustomerId="@CurrentCustomerId"  />
                </CascadingValue>
            }
        </div>
    </AuthorizeView>
</PageBody>