﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Intersolve.Models;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
{
    public partial class IntersolveOrderDefaultFilter
    {
        [Parameter]
        public EventCallback<IntersolveOrderDefaultFilterContext> FilterChanged { get; set; }

        [Parameter]
        public IntersolveOrderDefaultFilterContext Filter { get; set; }

        // Maybe get from the service in a future update? Don't expect much itteration here though.
        private readonly List<string> statusTypes = ["Created", "Found", "Acknowledged"];
    }
}
