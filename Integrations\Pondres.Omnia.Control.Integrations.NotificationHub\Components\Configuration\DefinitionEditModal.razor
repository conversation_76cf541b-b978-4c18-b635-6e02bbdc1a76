﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using BlazorMonaco;
@using BlazorMonaco.Editor
@using Pondres.Omnia.NotificationHub.Contracts.Api.Definition
@using static Telerik.Blazor.ThemeConstants.Button

<ActionModal @ref="ActionModal"
             T="NotificationDefinitionModel"
             Title="@GetDialogTitle()"
             ButtonType="@Telerik.Blazor.ThemeConstants.Button.ThemeColor.Success"
             ButtonText="Save"
             OnSubmit="SubmitModalAsync"
             OnClose="ResetDialogAsync"
             ShowEmptyContext=true
             Width="800px">

    @if (Type == DefinitionModalType.Create)
    {
        <label for="definitonNameInput" class="k-label k-form-label">Name</label>
        <TelerikTextBox Id="definitonNameInput" @bind-Value="@Definition.Id" class="mb-4"></TelerikTextBox>
    }

    <label for="failSilentlyUntilInput" class="k-label k-form-label">Fail silently until:</label>
    <TelerikDateTimePicker @bind-Value="@FailSilentlyUntil" Format="dd MMM yyyy HH:mm" Id="failSilentlyUntilInput"></TelerikDateTimePicker>
    <span>* <i>No failure notifications will be sent until this date</i></span>

    <div class="full-width-modal-body pt-4">
        <StandaloneCodeEditor @ref="Editor" Id="monaco-definition-details" ConstructionOptions="EditorConstructionOptions" />
    </div>

</ActionModal>