﻿using Pondres.Omnia.Control.Integrations.Print.Client;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions
{
    public static class ReleaseSchedulesExtensions
    {
        public static ReleaseSchedules ToDto(this string json) =>
            JsonSerializer.Deserialize<ReleaseSchedules>(json);

        public static string ToJson(this ReleaseSchedules releaseSchedule) =>
            JsonSerializer.Serialize(releaseSchedule, new JsonSerializerOptions { WriteIndented = true });
    }
}
