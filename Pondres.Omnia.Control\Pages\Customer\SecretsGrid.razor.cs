﻿using Blazored.Toast.Configuration;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Customer.Extensions;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Pondres.Omnia.Customer.Contracts.Api;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Customer;

public partial class SecretsGrid : SelectableCustomerGridPageBase<VaultSecretListItem>
{
    [Inject]
    private ISecretService SecretService { get; set; }

    private List<VaultSecretListItem> VaultSecrets { get; set; }

    private CustomerSecretEditor DialogEditor;

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await LoadSecretsAsync();
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await LoadSecretsAsync();
        }
    }

    public async void OnSaveAction()
    {
        var secret = DialogEditor.SecretModel.ToCreateOrUpdateSecretModel();

        try
        {
            if (DialogEditor.IsNew)
                await SecretService.CreateSecretAsync(secret);
            else
                await SecretService.UpdateSecretAsync(secret);
        }
        catch (Exception exception)
        {
            ToastService.ShowError($"The secret could not be saved: {exception.Message}");
        }
        finally
        {
            await LoadSecretsAsync();
        }
    }

    private async Task LoadSecretsAsync()
    {
        VaultSecrets = null;

        try
        {
            VaultSecrets = await SecretService.GetSecretsAsync(CurrentCustomerId);
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private void OnAddCommand()
    {
        DialogEditor.ShowDialog(new() { CustomerId = CurrentCustomerId }, true);
    }

    private void OnEditCommand(GridCommandEventArgs e)
    {
        var model = (e.Item as VaultSecretListItem).ToModel();
        model.CustomerId = CurrentCustomerId;

        DialogEditor.ShowDialog(model);
    }
}