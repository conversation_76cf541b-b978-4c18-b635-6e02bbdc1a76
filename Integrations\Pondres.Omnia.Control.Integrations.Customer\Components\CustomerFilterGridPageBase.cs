﻿using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using System.Collections.ObjectModel;
using Telerik.DataSource.Extensions;

namespace Pondres.Omnia.Control.Integrations.Customer.Components;

public class CustomerFilterGridPageBase<TItem, TFilterContext> : SelectableCustomerGridPageBase<TItem>
    where TItem : class, new()
    where TFilterContext : IFilterContext<PagedResultModel<TItem>>
{
    [Inject]
    private InfiniteScrollHelper InfiniteScrollHelper { get; set; }

    protected ObservableCollection<TItem> GridItems { get; } = new ObservableCollection<TItem>();

    protected TFilterContext CurrentFilter { get; set; }

    protected bool LoadingGridItems { get; set; }

    protected bool MoreGridItemsAvailable { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
            InfiniteScrollHelper.AddHandler(PageBottomReachedAsync);
    }

    protected async Task LoadGridItemsAsync(bool refresh = false)
    {
        if (refresh)
            ResetList();

        if (!LoadingGridItems && MoreGridItemsAvailable && !CurrentCustomerId.IsNullOrWhiteSpace())
        {
            try
            {
                LoadingGridItems = true;
                PagedResultModel<TItem> result = null;

                if (CurrentFilter != null)
                    result = await CurrentFilter.GetNextResultsAsync(CurrentCustomerId, refresh);

                if (result != null)
                {
                    MoreGridItemsAvailable = result.HasMore();
                    GridItems.AddRange(result.Items);
                }
            }
            catch (Exception exception)
            {
                ToastService.ShowError(exception.Message);
            }
            finally
            {
                await OnGridItemsLoadedAsync();

                LoadingGridItems = false;

                await InvokeAsync(StateHasChanged);
            }
        }
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
            CurrentCustomerId = customerId;

        await LoadGridItemsAsync(refresh: true);

        await base.OnCustomerChangedAsync(customerId);
    }

    protected virtual Task OnGridItemsLoadedAsync() =>
        Task.CompletedTask;

    protected async Task OnFilterChangedAsync(TFilterContext filter)
    {
        CurrentFilter = filter;
        await LoadGridItemsAsync(refresh: true);
    }

    protected async void OnRefresh() =>
        await LoadGridItemsAsync(refresh: true);

    protected async Task PageBottomReachedAsync() =>
        await LoadGridItemsAsync(refresh: false);

    protected void ResetList()
    {
        GridItems?.Clear();
        MoreGridItemsAvailable = true;
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
            InfiniteScrollHelper.RemoveHandler(PageBottomReachedAsync);

        base.Dispose(disposing);
    }
}