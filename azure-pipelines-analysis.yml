# Docker
# Build a Docker image 
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
- master

resources:
- repo: self

variables:
- name: sonarProjectKey
  value: 'pondresnl_Pondres.Omnia.Control'
- name: sonarProjectName
  value: 'Pondres.Omnia.Control'

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: BuildAndTest
  displayName: Build and Test
  jobs:  
  - job: BuildAndTest
    displayName: Build and Test .NET projects
    steps:
    - task: SonarCloudPrepare@1
      inputs:
        SonarCloud: 'SonarCloud'
        organization: 'pondresnl'
        scannerMode: 'MSBuild'
        projectKey: '$(sonarProjectKey)'
        projectName: '$(sonarProjectName)'
        extraProperties: |
          # Additional properties that will be passed to the scanner, 
          # Put one key=value per line, example:
          # sonar.exclusions=**/*.bin
          sonar.cs.opencover.reportsPaths=$(Agent.TempDirectory)/**/coverage.opencover.xml
    - task: UseDotNet@2
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: 'restore'
        projects: '**/*.sln'
        feedsToUse: 'config'
        nugetConfigPath: 'nuget.config'
        verbosityRestore: minimal
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        projects: '**/*.sln'
        arguments: '--no-restore'
    - task: DotNetCoreCLI@2
      displayName: Test
      inputs:
        command: 'test'
        projects: '**/*.sln'
        arguments: '--no-build --settings coverletArgs.runsettings -- xunit.parallelizeAssembly=true'
    - task: SonarCloudAnalyze@1
      inputs:
        jdkversion: 'JAVA_HOME_17_X64'
    - task: SonarCloudPublish@1
      inputs:
        pollingTimeoutSec: '300'