﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Components.Configuration;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Flow;
using Telerik.Blazor.Components;
using Telerik.DataSource;

namespace Pondres.Omnia.Control.Pages.Order.Configuration;

public partial class OrderFlow : SelectableCustomerGridPageBase<OrderFlowDefinitionDetails>
{
    [Inject]
    private IFlowService FlowService { get; set; }

    public OrderFlowEditor DialogEditor { get; set; }

    public List<OrderFlowDefinitionDetails> OrderFlowDefinitions { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        await LoadOrderFlowDefinitionsAsync();
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await LoadOrderFlowDefinitionsAsync();
        }
    }

    public async Task OnSaveActionAsync()
    {
        var orderFlowDefinition = DialogEditor.DefinitionDetails.ToDto();

        UpsertFlowDefinition model = new()
        {
            Customer = CurrentCustomerId,
            Flow = orderFlowDefinition
        };

        try
        {
            UpsertFlowDefinitionResult result = null;

            if (DialogEditor.IsNew)
                result = await FlowService.CreateOrderFlowDefinitionDetailsAsync(model);
            else
                result = await FlowService.UpdateOrderFlowDefinitionDetailsAsync(model);

            if (result.IsValid)
            {
                await LoadOrderFlowDefinitionsAsync();

                await DialogEditor.CloseDialogAsync();
            }
            else
                DialogEditor.ValidationMessage = result.Message;
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private async Task LoadOrderFlowDefinitionsAsync()
    {
        OrderFlowDefinitions = null;

        try
        {
            OrderFlowDefinitions = await FlowService.LoadOrderFlowDefinitionsAsync(CurrentCustomerId);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    private void OnAddCommand()
    {
        DialogEditor.ShowDialog(CreateEmptyOrderFlowDefinition().ToJson(), true);
    }

    private void OnDetailsCommand(GridCommandEventArgs e)
    {
        DialogEditor.ShowDialog((e.Item as OrderFlowDefinition).ToJson());
    }

    private static void OnGridStateInit(GridStateEventArgs<OrderFlowDefinitionDetails> args)
    {
        var state = new GridState<OrderFlowDefinitionDetails>
        {
            SortDescriptors = new List<SortDescriptor>
            {
                new SortDescriptor{ Member = nameof(OrderFlowDefinitionDetails.UpdatedOn), SortDirection = ListSortDirection.Descending }
            }
        };

        args.GridState = state;
    }

    private static OrderFlowDefinition CreateEmptyOrderFlowDefinition()
    {
        return new OrderFlowDefinition()
        {
            Tasks = new List<OrderTaskDefinition>()
            {
                new OrderTaskDefinition()
                {
                    Destination = new OrderTaskDestinationDefinition(),
                    Input = new OrderTaskInputDefinition(),
                    Output = new OrderTaskOutputDefinition(),
                    SkipTriggers = new List<OrderTaskTriggerDefinition>()
                    {
                        new OrderTaskTriggerDefinition()
                    },
                    StartTriggers = new List<OrderTaskTriggerDefinition>()
                    {
                        new OrderTaskTriggerDefinition()
                    }
                }
            },
            Confirmations = new List<string>(),
            RejectionTriggers = new List<string>()
        };
    }
}