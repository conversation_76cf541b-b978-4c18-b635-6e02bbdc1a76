﻿window.countdown = {
    start: function (elementId, duration) {
        var bar = document.getElementById(elementId);
        var progress = bar.firstElementChild;

        bar.style.display = 'flex'

        progress.style.transitionDuration = '0s';
        progress.style.width = '100%';

        progress.offsetHeight; // forces the page to reflow (https://gist.github.com/paulirish/5d52fb081b3570c81e3a)

        progress.style.transitionDuration = duration + 's';
        progress.style.width = '0px';
    },
    stop: function (elementId) {
        var bar = document.getElementById(elementId);
        bar.style.display = 'none'
    }
}

