﻿@using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Details
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table">
            <tbody>
                <tr>
                    <th>@nameof(Document.DocumentId)</th>
                    <td>@Document.DocumentId</td>
                </tr>
                <tr>
                    <th>@nameof(Document.OrderMetadata.OrderId)</th>
                    <td>
                        <a class="link" @onclick="() => NavigateToOrder(Document.OrderMetadata.Customer, Document.OrderMetadata.OrderId.ToString())"
                           @onclick:stopPropagation="true">@Document.OrderMetadata.OrderId</a>
                    </td>
                </tr>
                <tr>
                    <th>@nameof(Document.OrderMetadata.Customer)</th>
                    <td>@Document.OrderMetadata.Customer</td>
                </tr>
                <tr>
                    <th>@nameof(Document.OrderMetadata.CustomerReference)</th>
                    <td>@Document.OrderMetadata.CustomerReference</td>
                </tr>
                <tr>
                    <th>@nameof(Document.OrderMetadata.Flow)</th>
                    <td>@Document.OrderMetadata.Flow</td>
                </tr>
                <tr>
                    <th>@nameof(Document.CreatedOn)</th>
                    <td>@Document.CreatedOn.LocalDateTime</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>


