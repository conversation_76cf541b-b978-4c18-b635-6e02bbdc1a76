﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Customer.Models;

namespace Pondres.Omnia.Control.Integrations.Customer.Components;

public partial class CustomerSecretEditor : ModalBase
{
    public SecretModel SecretModel { get; private set; }

    public bool IsNew { get; private set; }

    [Parameter]
    public EventCallback OnSaveAction { get; set; }

    [Parameter]
    public EventCallback OnCloseAction { get; set; }

    public void ShowDialog(SecretModel secretModel, bool isNew = false)
    {
        SecretModel = secretModel;
        IsNew = isNew;

        IsVisible = true;
    }

    public async Task SaveSecretModelAsync()
    {
        await OnSaveAction.InvokeAsync();

        await CloseDialogAsync();
    }

    public async Task CloseDialogAsync()
    {
        await OnCloseAction.InvokeAsync();

        SecretModel = null;

        IsVisible = false;
    }
}