﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Warehouse.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Models;

public class WarehouseDocumentBatchFilterContext : CachedFilterContext<WarehouseDocumentListItem, WarehouseDocumentBatchFilterFields>,
    IWarehouseDocumentFilterContext
{
    private readonly IWarehouseDocumentService documentService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public WarehouseDocumentBatchFilterContext(
        NavigationManager navigationManager,
        IWarehouseDocumentService documentService,
        IFilterCacheService filterCacheService)
        : base(navigationManager, filterCacheService)
    {
        this.documentService = documentService;
    }

    protected override async Task<PagedResultModel<WarehouseDocumentListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await documentService.GetDocumentsByBatchFilterAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter(string filterId) =>
        navigationManager.NavigateToWarehouseDocumentListWithBatchFilter(filterId);
}
