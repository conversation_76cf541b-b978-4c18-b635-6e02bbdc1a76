using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;
using System.Text.Json;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Order;

public partial class ActiveStatus
{
    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    private IOrderService OrderService { get; set; }

    [Parameter]
    public OrderFullInformation Order { get; set; }

    private string OrderActiveStatus { get; set; } = null;

    private readonly JsonSerializerOptions serializerOptions = new JsonSerializerOptions { WriteIndented = true };


    private async Task LoadActiveStatusAsync()
    {
        try
        {
            var result = await OrderService.GetActiveStatusAsync(Order.Customer, Guid.Parse(Order.OrderId));
            using var jsonDoc = JsonDocument.Parse(result.Message.State);
            var formattedJson = JsonSerializer.Serialize(jsonDoc.RootElement, serializerOptions);

            OrderActiveStatus = formattedJson;

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
    }
}