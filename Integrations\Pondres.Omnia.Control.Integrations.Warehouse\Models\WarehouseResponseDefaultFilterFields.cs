﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Models;

public class WarehouseResponseDefaultFilterFields : FilterFieldsBase<WarehouseResponseDefaultFilterFields>
{
    public string FileName { get; set; }
    public string MessageType { get; set; } = ControlConstants.DefaultDropdownValue;
    public string StateTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    public string ResultTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
}
