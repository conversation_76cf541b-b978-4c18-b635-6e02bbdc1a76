﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Services;

public interface IESafeDeliveryService
{
    Task<PagedResultModel<DeliveryListItem>> GetPagedListAsync(DeliveryBatchSelectionFilter filter);
    Task<PagedResultModel<DeliveryListItem>> GetPagedListAsync(DeliveryListFilter filter);
    Task<DeliveryDetails> GetDetailsAsync(string customer, Guid deliveryId);
}
