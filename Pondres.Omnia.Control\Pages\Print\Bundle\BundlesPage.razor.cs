﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Components.Bundle;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Services;
using System;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Print.Bundle;

[Authorize(Roles = "PrintReader, PrintContributor, ControlOwner")]
public partial class BundlesPage : CustomerFilterGridPageBase<PrintBundleListItem, IPrintBundleFilterContext>
{
    [Inject]
    private IJSRuntime JSRuntime { get; set; }

    [Inject]
    private IPrintService PrintService { get; set; }

    [Inject]
    private EnvironmentSettings EnvironmentSettings { get; set; }

    [Parameter]
    public bool MoreItemsAvailable { get; set; }

    private string BundleCount { get; set; }

    private string SheetCount { get; set; }

    private string DocumentCount { get; set; }

    private PrintBundleContinueModal PrintBundleContinueDialog { get; set; }

    private PrintBundleCancelModal PrintBundleCancelDialog { get; set; }

    private PrintBundleRemoveModal PrintBundleRemoveDialog { get; set; }

    private PrintBundleRawDetailsModal PrintBundleRawDetailsDialog { get; set; }

    private PrintBundleReleaseModal PrintBundleReleaseDialog { get; set; }

    private PrintBundlePrintConfirmationModal PrintBundlePrintConfirmationDialog { get; set; }

    private PrintBundleCompleteModal PrintBundleCompleteDialog { get; set; }

    private IEnumerable<string> SelectedBundlesFullFilePaths => DataGrid.SelectedItems
            .SelectMany(bundle => bundle.Files
            .Select(file => GetFullFilePath(bundle, file)));

    private bool SelectedBundlesCanBeConfirmed
    {
        get
        {
            return HasSelectedItems && DataGrid.SelectedItems.All(x => x.Actions.Contains(PrintBundleInputOption.PrintConfirmation));
        }
    }

    private bool SelectedBundlesCanBeReleased
    {
        get
        {
            return HasSelectedItems && DataGrid.SelectedItems.All(x => x.Actions.Contains(PrintBundleInputOption.Release));
        }
    }

    private bool SelectedBundlesCanBeCompleted
    {
        get
        {
            return HasSelectedItems && DataGrid.SelectedItems.All(x => x.Status.Status == MapPrintBundleState.Printed);
        }
    }

    private async Task CopySelectedBundleFilePathsToClipboardAsync()
    {
        var fullFilePathsJoined = string.Join(Environment.NewLine, SelectedBundlesFullFilePaths);

        await ClipboardManager.CopyToClipboardAsync(fullFilePathsJoined);

        ToastService.ShowInfo($"Copied {SelectedBundlesFullFilePaths.Count()} file paths to clipboard");
    }

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as PrintBundleListItem).GetStatusColorStyle();

    private void OnContinueBundleCommand(GridCommandEventArgs e)
    {
        PrintBundleContinueDialog.ShowDialog(new() { Bundle = (e.Item as PrintBundleListItem) });
    }

    protected async Task OnSelectedItemsChangedAsync(IEnumerable<PrintBundleListItem> items)
    {
        SetHeaderCounters(items);

        await InvokeAsync(StateHasChanged);
    }

    private async Task ContinueBundleAsync()
    {
        var bundleModel = PrintBundleContinueDialog.Model;

        await PrintService.ContinuePrintBundleAsync(bundleModel.Bundle.Id, bundleModel.Force, CurrentUser);

        await LoadGridItemsAsync(refresh: true);
    }

    private void OnCancelBundleCommand(GridCommandEventArgs e)
    {
        PrintBundleCancelDialog.ShowDialog(new() { Bundle = (e.Item as PrintBundleListItem) });
    }

    private async Task CancelBundleAsync()
    {
        var bundleModel = PrintBundleCancelDialog.Model;

        await PrintService.CancelPrintBundleAsync(bundleModel.Bundle.Id, bundleModel.Force, bundleModel.Reprint, CurrentUser);

        await LoadGridItemsAsync(refresh: true);
    }

    private void OnRemoveDocumentCommand(GridCommandEventArgs e)
    {
        PrintBundleRemoveDialog.ShowDialog(new() { Bundle = (e.Item as PrintBundleListItem) });
    }

    private async Task RemoveDocumentAsync()
    {
        var bundleModel = PrintBundleRemoveDialog.Model;

        await PrintService.RemoveDocumentPrintBundleAsync(bundleModel.Bundle.Id, bundleModel.DocumentId);

        await LoadGridItemsAsync(refresh: true);
    }

    private void OnReleaseBundleCommand()
    {
        PrintBundleReleaseDialog.ShowDialog(DataGrid.SelectedItems);
    }

    private async void OnGetRawBundleDetailsCommand(GridCommandEventArgs e)
    {
        var item = e.Item as PrintBundleListItem;

        var rawDetails = await PrintService.GetRawBundleDetailsAsync(item.Id);

        await PrintBundleRawDetailsDialog.ShowDialogAsync(rawDetails);
    }

    private async Task ReleaseBundleAsync()
    {
        var bundles = PrintBundleReleaseDialog.Model;
        var result = new List<ActionListButtonResult>();
        var releaseTime = DateTimeOffset.Now;

        foreach (var bundleId in bundles.Select(x => x.Id))
        {
            try
            {
                await PrintService.ReleasePrintBundleAsync(
                    bundleId: Guid.Parse(bundleId),
                    username: CurrentUser,
                    bundleOptions: new PrintBundleOptions { BundleMode = PrintBundleReleaseDialog.SelectedPrintBundleMode.Value },
                    releaseTime: releaseTime);
                result.Add(new() { Key = bundleId, IsSuccessful = true });
            }
            catch (Exception exception)
            {
                result.Add(new() { Key = bundleId, FailedResultMessage = exception.Message });
            }
        }

        await PrintBundleReleaseDialog.SetResultAsync(result);

        await LoadGridItemsAsync(refresh: true);
    }

    private void OnPrintConfirmationCommand()
    {
        PrintBundlePrintConfirmationDialog.ShowDialog(DataGrid.SelectedItems);
    }

    protected override async Task OnGridItemsLoadedAsync()
    {
        SetHeaderCounters();

        await base.OnGridItemsLoadedAsync();
    }

    private void OnCompleteBundleCommand()
    {
        PrintBundleCompleteDialog.ShowDialog(DataGrid.SelectedItems);
    }

    private async Task CompleteAsync()
    {
        var items = PrintBundleCompleteDialog.Model;
        var result = new List<ActionListButtonResult>();

        foreach (var bundleId in items.Select(item => item.Id))
        {
            try
            {
                await PrintService.CompletePrintBundleAsync(bundleId, CurrentUser);
                result.Add(new() { Key = bundleId, IsSuccessful = true });
            }
            catch (Exception exception)
            {
                result.Add(new() { Key = bundleId, FailedResultMessage = exception.Message });
            }
        }

        await PrintBundleCompleteDialog.SetResultAsync(result);

        await LoadGridItemsAsync(refresh: true);
    }

    private async Task ConfirmPrintAsync()
    {
        var items = PrintBundlePrintConfirmationDialog.Model;
        var result = new List<ActionListButtonResult>();

        foreach (var bundleId in items.Select(item => item.Id))
        {
            try
            {
                await PrintService.ConfirmPrintBundleAsync(bundleId, CurrentUser);
                result.Add(new() { Key = bundleId, IsSuccessful = true });
            }
            catch (Exception exception)
            {
                result.Add(new() { Key = bundleId, FailedResultMessage = exception.Message });
            }
        }

        await PrintBundlePrintConfirmationDialog.SetResultAsync(result);

        await LoadGridItemsAsync(refresh: true);
    }

    private async Task OnCopyPathCommandAsync(GridCommandEventArgs e)
    {
        await ClipboardManager.CopyToClipboardAsync((e.Item as PrintBundleListItem)
                              .ToPrintFolderPath(EnvironmentSettings.PrintFolderPath));

        ToastService.ShowInfo("Copied path to clipboard");
    }

    private string GetPrintFolderPath(PrintBundleListItem bundle) =>
        bundle.ToPrintFolderPath(EnvironmentSettings.PrintFolderPath);

    private string GetFullFilePath(PrintBundleListItem bundle, string filename) =>
        $"{bundle.ToPrintFolderPath(EnvironmentSettings.PrintFolderPath)}\\{filename}";

    private void SetHeaderCounters(IEnumerable<PrintBundleListItem> items = null)
    {
        if (items?.Any() ?? false)
        {
            BundleCount = $"({items.Count()})";
            SheetCount = $"({items.Sum(x => x.Sheets)})";
            DocumentCount = $"({items.Sum(x => x.Documents)})";
        }
        else if (GridItems?.Any() ?? false)
        {
            BundleCount = $"({GridItems.Count})";
            SheetCount = $"({GridItems.Sum(x => x.Sheets)})";
            DocumentCount = $"({GridItems.Sum(x => x.Documents)})";
        }
        else
        {
            BundleCount = null;
            SheetCount = null;
            DocumentCount = null;
        }
    }
}