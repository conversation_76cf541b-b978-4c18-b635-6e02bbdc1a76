﻿using Pondres.Omnia.Control.Integrations.Common.Provicers;

namespace Pondres.Omnia.Control.Integrations.Common.Services;

public class FilterCacheService : IFilterCacheService
{
    private static readonly TimeSpan FilterSlidingExpiration = TimeSpan.FromDays(14);

    private readonly IDistributedCacheProvider cacheProvider;

    public FilterCacheService(IDistributedCacheProvider cacheProvider)
    {
        this.cacheProvider = cacheProvider;
    }

    public async Task<string> SaveFilterFieldsAsync<TFilterFieldsType>(TFilterFieldsType filter) where TFilterFieldsType : class =>
        await CreateCacheEntryAsync(filter);

    public async Task<TFilterFieldsType> GetFilterFieldsAsync<TFilterFieldsType>(string identifier) where TFilterFieldsType : class =>
        await cacheProvider.GetAsync<TFilterFieldsType>(identifier.ToString());

    private async Task<string> CreateCacheEntryAsync<TFilterType>(TFilterType filter) where TFilterType : class
    {
        var cacheKey = CreateCacheKey();

        await cacheProvider.SetWithSlidingExpirationAsync(cacheKey, filter, FilterSlidingExpiration);

        return cacheKey;
    }

    private static string CreateCacheKey() =>
        $"filter_{Guid.NewGuid()}";
}
