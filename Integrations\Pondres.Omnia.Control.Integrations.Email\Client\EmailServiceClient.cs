﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Exceptions;
using System.Net;
using System.Text;

namespace Pondres.Omnia.Control.Integrations.Email.Client;

public partial class EmailServiceClient
{
    protected static async Task ProcessResponseAsync(HttpClient client, HttpResponseMessage response, CancellationToken cancellationToken)
    {
        if (!response.IsSuccessStatusCode)
            try
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                if (response.StatusCode == HttpStatusCode.NotFound)
                    throw new ApiException(response.StatusCode, $"Not Found. {responseContent}");
                else if (responseContent.IsNullOrWhiteSpace())
                    throw new ApiException(response.StatusCode, $"No response content, status code {response.StatusCode} was returned");
                else
                    throw new ApiException(response.StatusCode, responseContent);
            }
            catch (Exception exception)
            {
                throw new ApiException(response.StatusCode, $"Api returned status code {response.StatusCode} with no content", exception);
            }
    }

    protected static Task PrepareRequestAsync(HttpClient client_, HttpRequestMessage request_, string url_, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    protected static Task PrepareRequestAsync(HttpClient client_, HttpRequestMessage request_, StringBuilder urlBuilder_, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    static partial void UpdateJsonSerializerSettings(JsonSerializerSettings settings)
    {
        settings.Converters.Add(new StringEnumConverter());
    }
}
