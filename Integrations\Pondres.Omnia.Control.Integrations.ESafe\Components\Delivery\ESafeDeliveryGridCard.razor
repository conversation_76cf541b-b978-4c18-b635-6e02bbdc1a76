﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Extensions

<GridCard Title="E-Safe deliveries"
          GetRawColorFunc="(delivery) => delivery.StateType.GetStatusColorStyle()"
          GetItemsAsync="GetNextItemsAsync">
    <TableHeaderContent>
        <th>Id</th>
        <th>Status</th>
        <th>Updated On</th>
    </TableHeaderContent>
    <TableRowContent>
        <td><a class="link" @onclick="() => NavigateToDelivery(context.Id)" @onclick:stopPropagation="true">@context.Id.Shorten()</a></td>
        <td>@context.StateType.GetStatusString(context.ResultType, context.TrackVaultStatus)</td>
        <td>@context.LastUpdatedOn.LocalDateTime</td>
    </TableRowContent>
</GridCard>