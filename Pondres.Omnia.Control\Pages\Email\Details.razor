﻿@page "/Email/{Customer}/{DeliveryId}"

@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Email.Components
@using Pondres.Omnia.Control.Integrations.Email.Components.Delivery
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.Email.Contracts.Api.Delivery;

<PageHeader Title="@($"E-Mail Delivery {DeliveryId}")" />
<PageBody>
<div class="container-fluid row">
    @if (PageLoading)
    {
        <LoadingSpinner />
    }
    else
    {
        <CascadingValue Value="Delivery">
            <div class="col-xl-6 col-12">
                <DeliveryInformation>
                    <OrderDetailsLink>
                        <OrderDetailsLink OrderId="@Delivery.OrderMetadata.OrderId.ToString()" Customer="@Delivery.Customer">
                            @Delivery.OrderMetadata.CustomerReference
                        </OrderDetailsLink>
                    </OrderDetailsLink>
                </DeliveryInformation>

            </div>
            <div class="col-xl-6 col-12">
                <DeliveryCurrentStatus />
            </div>
        </CascadingValue>
    }
</div>
</PageBody>