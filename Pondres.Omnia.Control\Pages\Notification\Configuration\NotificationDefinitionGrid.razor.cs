﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.NotificationHub.Contracts.Api.Definition;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Notification.Configuration;

public partial class NotificationDefinitionGrid : SelectableGridBase<NotificationDefinitionModel>
{
    [CascadingParameter]
    public NotificationMappingGridContext MappingContext { get; set; }

    [Parameter]
    public EventCallback OnDefinitionEdit { get; set; }

    [Parameter]
    public string CustomerId { get; set; }

    private DefinitionEditModal DefinitionEditModal { get; set; }

    private async Task OpenNewDefinitionModalAsync(string definitionType)
    {
        await DefinitionEditModal.OpenModalAsync(DefinitionModalType.Create, new NotificationDefinitionModel
        {
            Type = definitionType,
            Customer = CustomerId
        });
    }

    private async Task OnEditModalSubmittedAsync()
    {
        await OnDefinitionEdit.InvokeAsync();
    }

    private async Task EditDefinitionAsync(GridCommandEventArgs args)
    {
        await DefinitionEditModal.OpenModalAsync(DefinitionModalType.Edit, args.Item as NotificationDefinitionModel);
    }
}