using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Link;

public partial class BatchPrintDocumentLink
{
    [Inject]
    public IFilterCacheService FilterCacheService { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    [Parameter]
    public string StyleClass { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public IEnumerable<string> OrderIds { get; set; } = new List<string>();

    private async Task NavigateAsync()
    {
        var filterFields = new PrintDocumentBatchFilterFields { OrderIds = OrderIds.ToList() };
        var filterId = await FilterCacheService.SaveFilterFieldsAsync(filterFields);
        NavigationManager.NavigateToPrintDocumentListWithBatchFilter(filterId);
    }
}
