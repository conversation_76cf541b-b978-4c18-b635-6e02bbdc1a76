﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Models;

public class OrderDefaultFilterContext : QueryStringFilterContext<OrderListItem, OrderDefaultFilterFields>, IOrderFilterContext
{
    private readonly IOrderService orderService;
    public override string FilterTypeName => FilterType.Default.ToString();

    public OrderDefaultFilterContext(NavigationManager navigationManager, IOrderService orderService)
        : base(navigationManager)
    {
        this.orderService = orderService;
    }

    protected override void NavigateToFilter() =>
        navigationManager.NavigateToOrderListWithFilter(FilterType.Default, FilterHelper.ConvertToQueryParameter(Fields));

    protected override async Task<PagedResultModel<OrderListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await orderService.GetOrdersByFilterAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));
}
