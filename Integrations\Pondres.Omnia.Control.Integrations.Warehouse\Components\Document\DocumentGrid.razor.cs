﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Warehouse.Extensions;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;
using System.Collections.ObjectModel;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;

public partial class DocumentGrid : SelectableGridBase<WarehouseDocumentListItem>
{
    [Parameter]
    public ObservableCollection<WarehouseDocumentListItem> Documents { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    [Parameter]
    public EventCallback OnRefresh { get; set; }

    [Parameter]
    public RenderFragment NavigationLinks { get; set; }

    public IEnumerable<string> UniqueSelectedCustomerReferences => DataGrid.SelectedItems.Select(x => x.OrderMetadata.CustomerReference).Distinct();

    private void OnRowDoubleClick(GridRowClickEventArgs e)
    {
        NavigateToDocumentDetails(e.Item as WarehouseDocumentListItem);
    }

    private void NavigateToDocumentDetails(WarehouseDocumentListItem item)
    {
        NavigationManager.NavigateTo($"WMS/Documents/{item.OrderMetadata.Customer}/{item.DocumentId}");
    }

    private void NavigateToOrder(WarehouseDocumentListItem item)
    {
        NavigationManager.NavigateTo($"Orders/{item.OrderMetadata.Customer}/{item.OrderMetadata.OrderId}");
    }

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs)
    {
        eventArgs.Class = (eventArgs.Item as WarehouseDocumentListItem).LastStatus.GetStatusColorStyle();
    }
}