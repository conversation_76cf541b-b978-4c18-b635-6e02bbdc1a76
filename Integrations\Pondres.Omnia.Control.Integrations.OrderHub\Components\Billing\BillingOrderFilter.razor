﻿@using Microsoft.AspNetCore.Components.Forms
@using Pondres.Omnia.Control.Integrations.Common.Components.Form

<EditForm EditContext="EditContext">
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 col-sm-12">
                    <label for="From" class="pr-4 col-form-label">Created on from</label>
                    <div class="row">
                        <div class="col-8 pr-1">
                            <InputDate Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CreatedOnFilter.FromDate" />
                        </div>
                        <div class="col-4 pl-1">
                            <InputTime Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CreatedOnFilter.FromTime" />
                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-sm-12">
                    <label for="To" class="pr-4 col-form-label">Created on to</label>
                    <div class="row">
                        <div class="col-8 pr-1">
                            <InputDate Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CreatedOnFilter.ToDate" />
                        </div>
                        <div class="col-4 pl-1">
                            <InputTime Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CreatedOnFilter.ToTime" />
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <label class="pr-4 col-form-label">Set to created today</label>
                    <div class="row">
                        <div class="col-8 pr-1">
                            <button class="btn btn-outline-primary mr-3" Disabled="@Loading" @onclick="SetFilterCreatedTodayAsync"><i class="oi oi-reload mr-2"></i>Set filter</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 col-sm-12">
                    <label for="From" class="pr-4 col-form-label">Completed on from</label>
                    <div class="row">
                        <div class="col-8 pr-1">
                            <InputDate Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CompletedOnFilter.FromDate" />
                        </div>
                        <div class="col-4 pl-1">
                            <InputTime Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CompletedOnFilter.FromTime" />
                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-sm-12">
                    <label for="To" class="pr-4 col-form-label">Completed on to</label>
                    <div class="row">
                        <div class="col-8 pr-1">
                            <InputDate Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CompletedOnFilter.ToDate" />
                        </div>
                        <div class="col-4 pl-1">
                            <InputTime Disabled="@Loading" class="form-control border-1 small" @bind-Value="Filter.CompletedOnFilter.ToTime" />
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <label class="pr-4 col-form-label">Set to completed today</label>
                    <div class="row">
                        <div class="col-8 pr-1">
                            <button class="btn btn-outline-primary mr-3" Disabled="@Loading" @onclick="SetFilterCompletedTodayAsync"><i class="oi oi-reload mr-2"></i>Set filter</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="float-right">
                
                
            </div>
        </div>
    </div>
</EditForm>


