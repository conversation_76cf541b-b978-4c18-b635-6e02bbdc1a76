﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.Control.Integrations.SecureMail.Extensions;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.SecureMail.Deliveries;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Grid : CustomerFilterGridPageBase<DeliveryListItem, ISecureMailDeliveryFilterContext>
{
    [Inject]
    protected IOrderService OrderService { get; set; }

    public List<string> FilterFlows { get; private set; }

    private IEnumerable<string> SelectedSecureMailConnectors =>
        DataGrid.SelectedItems.Select(bundle => bundle.Connector);

    public IEnumerable<string> SelectedUniqueCustomerReferences =>
        DataGrid.SelectedItems.Select(x => x.CustomerReference).Distinct();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await TryLoadFilterFlowsAsync();

        await LoadGridItemsAsync(refresh: true);
    }

    private async Task TryLoadFilterFlowsAsync()
    {
        try
        {
            FilterFlows?.Clear();
            FilterFlows = await OrderService.GetFlowsAsync(CurrentCustomerId);
        }
        catch
        {
            ToastService.ShowError($"Flows could not be loaded for customer {CurrentCustomerId}");
        }
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await TryLoadFilterFlowsAsync();

            await LoadGridItemsAsync(refresh: true);
        }
    }

    private void NavigateToDetails(string id)
    {
        NavigationManager.NavigateTo($"SecureMail/{CurrentCustomerId}/{id}");
    }

    private async Task CopySelectedSecureMailConnectorsToClipboardAsync()
    {
        var connectorsJoined = string.Join(Environment.NewLine, SelectedSecureMailConnectors);

        await ClipboardManager.CopyToClipboardAsync(connectorsJoined);

        ToastService.ShowInfo($"Copied {SelectedSecureMailConnectors.Count()} connectors to clipboard");
    }

    private void OnDetailsCommand(GridCommandEventArgs e) =>
         NavigateToDetails((e.Item as DeliveryListItem).Id);

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
         NavigateToDetails((e.Item as DeliveryListItem).Id);

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as DeliveryListItem).StateType.GetStatusColorStyle();
}