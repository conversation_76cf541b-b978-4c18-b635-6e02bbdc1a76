using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace;

public partial class NotificationTraceDefaultFilter
{
    [Inject]
    private INotificationTraceService NotificationTraceService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public EventCallback<NotificationTraceDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public NotificationTraceDefaultFilterContext Filter { get; set; }

    private List<string> eventTypes = new();
    private List<string> statusTypes = new();

    private async Task OnFilterChangedAsync()
    {
        await FilterChanged.InvokeAsync(Filter);
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            eventTypes = await NotificationTraceService.GetEventTypesAsync();
            statusTypes = NotificationTraceService.GetStatusTypes();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }
}
