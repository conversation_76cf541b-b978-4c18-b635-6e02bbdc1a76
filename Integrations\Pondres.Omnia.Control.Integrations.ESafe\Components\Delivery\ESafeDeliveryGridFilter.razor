﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab
@using Pondres.Omnia.Control.Integrations.ESafe.Models

<TabPanel TDataType="IESafeDeliveryFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IESafeDeliveryFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IESafeDeliveryFilterContext">
                <TabPanelTab TDataType="IESafeDeliveryFilterContext" Name="Default" Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <ESafeDeliveryDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" Flows="@Flows" />
                    </Template>
                </TabPanelTab>
                <TabPanelTab TDataType="IESafeDeliveryFilterContext" Name="Batch" Data="BatchFilter" Selected="FilterManager.CurrentFilter == BatchFilter">
                    <Template>
                        <ESafeDeliveryBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>