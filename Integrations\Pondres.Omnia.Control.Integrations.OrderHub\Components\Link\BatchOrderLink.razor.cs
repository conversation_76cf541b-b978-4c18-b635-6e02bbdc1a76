using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Link;

public partial class BatchOrderLink
{
    [Inject]
    public IFilterCacheService FilterCacheService { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    [Parameter]
    public string StyleClass { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public IEnumerable<string> CustomerReferences { get; set; } = new List<string>();

    private async Task NavigateAsync()
    {
        var filterFields = new OrderBatchFilterFields { CustomerReferences = CustomerReferences.ToList() };
        var filterId = await FilterCacheService.SaveFilterFieldsAsync(filterFields);
        NavigationManager.NavigateToOrderListWithBatchFilter(filterId);
    }
}
