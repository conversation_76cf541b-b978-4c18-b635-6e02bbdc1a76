﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab


<TabPanel TDataType="IOrderFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IOrderFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IOrderFilterContext">
                <TabPanelTab TDataType="IOrderFilterContext" Name="Default" 
                             Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <OrderDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" Flows="Flows" />
                    </Template>
                </TabPanelTab>
                <TabPanelTab TDataType="IOrderFilterContext" Name="Batch" Data="BatchFilter" Selected="FilterManager.CurrentFilter == BatchFilter">
                    <Template>
                        <OrderBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>