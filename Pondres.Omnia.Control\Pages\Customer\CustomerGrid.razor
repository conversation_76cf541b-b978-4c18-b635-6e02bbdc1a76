﻿@page "/Configuration/Customer"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using Pondres.Omnia.Control.Integrations.Customer.Models
@using Telerik.SvgIcons

@inherits SelectableGridBase<CustomerModel>

<CustomerDetailsModal @ref="DialogEditor" OnSaveAction="@OnSaveActionAsync" />

<PageHeader Title="Customer Configuration" />
<PageBody>
    <AuthorizeView Roles="ControlOwner">
        <TelerikCard>
            <CardHeader>
                <CardTitle>
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Customers</h6>
                    <div class="float-right">
                        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Success)" OnClick="@OnAddCommand">Add Customer</TelerikButton>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardBody>
                <TelerikGrid Data="@Customers" @ref="DataGrid" Class="grid-no-scroll"
                             SelectionMode="GridSelectionMode.Multiple">
                    <GridExport>
                        <GridCsvExport FileName="Customers" OnBeforeExport="@OnBeforeCsvExport" />
                    </GridExport>
                    <GridToolBarTemplate>
                        <GridCommandButton Command="CsvExport" Icon="@SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export to selected CSV" : "Export to CSV")</GridCommandButton>
                        <GridCommandButton Command="Copy" Icon="@SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
                    </GridToolBarTemplate>
                    <GridColumns>
                        <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" />
                        <SelectableGridColumn Field="Id" Title="Id" SelectionChanged="@OnColumnSelect" />
                        <SelectableGridColumn Field="Name" Title="Name" SelectionChanged="@OnColumnSelect" />
                        <GridCommandColumn Context="customer">
                            <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="Edit" OnClick="@EditCommandAsync">Edit</GridCommandButton>
                            <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)" Title="Disable" OnClick="@OnDisableCommandAsync">Disable</GridCommandButton>
                        </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            </CardBody>
        </TelerikCard>
    </AuthorizeView>
</PageBody>