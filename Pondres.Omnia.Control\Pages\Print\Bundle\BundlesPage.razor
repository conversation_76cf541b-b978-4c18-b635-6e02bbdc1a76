﻿@page "/Print/Bundles"

@using Pondres.Omnia.Control.Integrations.Common.Components.Buttons
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Print.Client;
@using Pondres.Omnia.Control.Integrations.Print.Components.Bundle
@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Print.Models
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal
@using static Telerik.Blazor.ThemeConstants.Button
@using Telerik.SvgIcons

@inherits CustomerFilterGridPageBase<PrintBundleListItem, IPrintBundleFilterContext>

<PageHeader Title="Print bundle overview" />
<PageBody>

    <PrintBundleRawDetailsModal @ref="PrintBundleRawDetailsDialog" />
    <PrintBundleContinueModal @ref="PrintBundleContinueDialog" OnSaveAction="@ContinueBundleAsync" />
    <PrintBundleCancelModal @ref="PrintBundleCancelDialog" OnSaveAction="@CancelBundleAsync" />
    <PrintBundleRemoveModal @ref="PrintBundleRemoveDialog" OnSaveAction="@RemoveDocumentAsync" />
    <PrintBundleReleaseModal @ref="PrintBundleReleaseDialog" 
                                        OnSaveAction="@ReleaseBundleAsync"
                                        CloseOnSubmit="false" />
    <PrintBundlePrintConfirmationModal @ref="PrintBundlePrintConfirmationDialog" 
                                       OnSaveAction="@ConfirmPrintAsync"
                                       CloseOnSubmit="false" />
    <PrintBundleCompleteModal @ref="PrintBundleCompleteDialog"
                                       OnSaveAction="@CompleteAsync"
                                       CloseOnSubmit="false" />

    <PrintBundleGridFilter FilterChanged="OnFilterChangedAsync" />
    <TableHeader AutoRefresh="true" Refresh="OnRefresh">
        <AuthorizeView Roles="PrintContributor, ControlOwner">             
            <TelerikButton ThemeColor="@ThemeColor.Success" OnClick="OnPrintConfirmationCommand" Class="large-scale"
                           Enabled="SelectedBundlesCanBeConfirmed">Printed</TelerikButton>
            <TelerikButton ThemeColor="@ThemeColor.Success" OnClick="OnReleaseBundleCommand" Class="large-scale"
                           Enabled="SelectedBundlesCanBeReleased">Release</TelerikButton>
            <TelerikButton ThemeColor="@ThemeColor.Success" OnClick="OnCompleteBundleCommand" Class="large-scale"
                           Enabled="SelectedBundlesCanBeCompleted">Complete</TelerikButton>
        </AuthorizeView>
    </TableHeader>

    <TelerikGrid Data="@GridItems" @ref="DataGrid" Class="grid-no-scroll"
                 SelectedItemsChanged="@(async (IEnumerable<PrintBundleListItem> items) => await OnSelectedItemsChangedAsync(items))"
                 SelectionMode="GridSelectionMode.Multiple">
        <GridExport>
            <GridCsvExport FileName="Bundles" OnBeforeExport="@OnBeforeCsvExport" />
        </GridExport>
        <GridToolBarTemplate>
            <GridCommandButton Command="CsvExport" Icon="SvgIcon.FileCsv" Enabled="@HasData">@(HasSelectedItems ? "Export selected to CSV" : "Export to CSV")</GridCommandButton>
            <GridCommandButton Command="Copy" Icon="SvgIcon.Copy" Enabled="@HasData" OnClick="@CopySelectedAsync">@(HasSelectedItems ? "Copy selected" : "Copy")</GridCommandButton>
            <GridCommandButton Command="CopyPath" Icon="SvgIcon.Copy" Enabled="@HasSelectedItems"
                               OnClick="@CopySelectedBundleFilePathsToClipboardAsync">Copy paths</GridCommandButton>
        </GridToolBarTemplate>
        <GridColumns>
            <GridCheckboxColumn Width="40px" CheckBoxOnlySelection="true" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="Id" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <TitleFragment>
                    Bundle @BundleCount
                </TitleFragment>
            </SelectableGridColumn>
            <SelectableGridColumn Field="BatchName" Title="Batch" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="Status.Status" Title="Status" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="Sheets" Title="Sheets" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <TitleFragment>
                    Sheets @SheetCount
                </TitleFragment>
            </SelectableGridColumn>
            <SelectableGridColumn Field="Documents" SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground">
                <TitleFragment>
                    Documents @(DocumentCount)
                </TitleFragment>
            </SelectableGridColumn>
            <SelectableGridColumn Field="Metadata.PrintMode" Title="Mode" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="Metadata.SheetArticleCode" Title="Paper" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="Metadata.PrinterType" Title="Printer" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="@ApplyBackground" />
            <SelectableGridColumn Field="Metadata.MailDate" Title="Mail date" IsEnabled="false"
                                  SelectionChanged="@OnColumnSelect" OnCellRender="ApplyBackground" DisplayFormat="{0:dd-MM-yyyy}" />
            <GridCommandColumn Context="item" Width="200px" OnCellRender="@ApplyBackground">
                @{
                    var bundle = item as PrintBundleListItem;
                }
                
                <AuthorizeView Roles="ControlOwner, PrintContributor">
                    @if (bundle.Actions.Contains(PrintBundleInputOption.Continue))
                    {
                        <GridCommandButton OnClick="OnContinueBundleCommand" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="Continue">Continue</GridCommandButton>
                    }
                    @if (bundle.Actions.Contains(PrintBundleInputOption.RemoveDocument))
                    {
                        <GridCommandButton OnClick="OnRemoveDocumentCommand" ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)" Title="Remove Document">Remove Document</GridCommandButton>
                    }
                </AuthorizeView>
                <AuthorizeView Roles="ControlOwner">                    
                    @if (bundle.Status.Status != MapPrintBundleState.Printed && 
                         bundle.Status.Status != MapPrintBundleState.Cancelled)
                    {
                        <GridCommandButton OnClick="OnCancelBundleCommand" ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)" Title="Cancel">Cancel</GridCommandButton>
                    }

                    <GridCommandButton OnClick="OnGetRawBundleDetailsCommand" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="RawDetails">RawDetails</GridCommandButton>
                </AuthorizeView>

                <GridCommandButton Icon="@SvgIcon.Copy" OnClick="OnCopyPathCommandAsync" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="Copy path">Copy path</GridCommandButton>
                    
                <HyperLinkButton Icon="folder" RequestUrl="@($"file:///{GetPrintFolderPath(bundle)}")" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" Title="Open Folder">Open Folder</HyperLinkButton>
            </GridCommandColumn>
        </GridColumns>
        <DetailTemplate Context="bundle">
            <table class="w-100">
                <tbody>
                    <tr>
                        <td><b>Id</b></td>
                        <td> @bundle.Id</td>
                        <td><b>Customer</b></td>
                        <td> @bundle.Customer</td>
                        <td><b>Initiated By</b></td>
                        <td> @bundle.InitiatedBy</td>
                    </tr>
                    <tr>
                        <td><b>Created On</b></td>
                        <td> @bundle.CreatedOn.LocalDateTime.ToString()</td>
                        <td><b>Material Format</b></td>
                        <td> @bundle.Metadata.SheetFormat</td>
                        <td><b>Destination</b></td>
                        <td> @bundle.Metadata.PostalDestination</td>
                    </tr>
                    <tr>
                        <td><b>Carrier</b></td>
                        <td> @bundle.Metadata.Carrier</td>
                        <td><b>Laminate</b></td>
                        <td> @bundle.Metadata.Laminate.ToString()</td>
                    </tr>
                    <tr>
                        <td><b>Files</b></td>
                        <td>
                            @foreach (var file in bundle.Files)
                            {
                                <a href="@GetFullFilePath(bundle, file)" target="_blank" class="print-file-link">@file</a>

                                <br />
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </DetailTemplate>
    </TelerikGrid>
</PageBody>
