﻿using Microsoft.AspNetCore.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Buttons;

public partial class AutoRefreshButtons : IDisposable
{
    private int selectedOption;

    private PeriodicTimer periodicTimer;
    private bool isDisposing;

    [Parameter]
    public EventCallback Refresh { get; set; }

    [Parameter]
    public EventCallback<int> RefreshProgress { get; set; }

    public int SelectedOption
    {
        get => selectedOption;
        set
        {
            selectedOption = value;

            InitializeTimer();
        }
    }

    private int GetDuration()
    {
        return SelectedOption > 0 ? (SelectedOption / 1000) : 0;
    }

    private async void InitializeTimer()
    {
        int duration = GetDuration();

        if (RefreshProgress.HasDelegate)
            await RefreshProgress.InvokeAsync(duration);

        await InvokeAsync(StateHasChanged);

        if (duration > 0 && periodicTimer == null)
            periodicTimer = new(TimeSpan.FromSeconds(duration));

        while (duration > 0 && periodicTimer != null && !isDisposing && await periodicTimer.WaitForNextTickAsync())
        {
            if (Refresh.HasDelegate)
                await Refresh.InvokeAsync();

            if (RefreshProgress.HasDelegate)
                await RefreshProgress.InvokeAsync(GetDuration());
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        isDisposing = disposing;

        if (isDisposing)
            periodicTimer?.Dispose();

        periodicTimer = null;
    }
}