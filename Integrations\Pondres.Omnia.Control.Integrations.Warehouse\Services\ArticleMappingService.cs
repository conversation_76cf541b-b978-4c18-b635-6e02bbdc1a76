﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Warehouse.Contracts.Api;
using Pondres.Omnia.Warehouse.Contracts.Api.ArticleMapping;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Services;

public class ArticleMappingService : BaseApiService, IArticleMappingService
{
    public ArticleMappingService(HttpClient httpClient)
        : base(httpClient)
    { }

    public async Task<PagedResultModel<ArticleMapping>> LoadArticleMappingsAsync(ArticleMappingListFilter filter)
    {
        var result = await PostWithResultAsync<PagedList<ArticleMapping>>(filter, "wms/api/articlemapping/pagedlist");

        return new PagedResultModel<ArticleMapping>
        {
            Items = result.Items,
            ContinuationToken = result.ContinuationToken
        };
    }

    public async Task<PagedResultModel<ArticleMapping>> LoadArticleMappingsAsync(ArticleMappingBatchSelectionFilter filter)
    {
        var result = await PostWithResultAsync<PagedList<ArticleMapping>>(filter, "wms/api/articlemapping/batchlist");

        return new PagedResultModel<ArticleMapping>
        {
            Items = result.Items,
            ContinuationToken = result.ContinuationToken
        };
    }

    public async Task CreateArticleMappingAsync(ArticleMapping mapping)
    {
        await PostAsync(mapping, "wms/api/articlemapping/create");
    }

    public async Task UpdateArticleMappingAsync(ArticleMapping mapping)
    {
        await PostAsync(mapping, "wms/api/articlemapping/update");
    }

    public async Task DisableArticleMappingAsync(ArticleMapping mapping)
    {
        mapping.Enabled = false;

        await UpdateArticleMappingAsync(mapping);
    }
}
