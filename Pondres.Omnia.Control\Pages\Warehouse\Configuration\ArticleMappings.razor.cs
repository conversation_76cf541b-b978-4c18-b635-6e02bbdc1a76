﻿using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Components.Configuration;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.ArticleMapping;
using System.Collections.ObjectModel;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Warehouse.Configuration;

public partial class ArticleMappings : SelectableCustomerGridPageBase<ArticleMapping>
{
    [Inject]
    private IArticleMappingService ArticleMappingService { get; set; }

    public bool ExpandTab { get; set; } = true;

    public MappingEditor DialogEditor { get; set; }

    public ArticleMappingListFilter DefaultFilter { get; set; } = new();

    public ArticleMappingBatchSelectionFilter BatchFilter { get; set; } = new();

    public string BatchFilterKeys
    {
        get => BatchFilter.Keys.Join(Environment.NewLine);
        set
        {
            if (value.IsNullOrEmpty())
                BatchFilter.Keys.Clear();
            else
                BatchFilter.Keys = value.Split(Environment.NewLine, StringSplitOptions.TrimEntries).ToList();
        }
    }

    public ObservableCollection<ArticleMapping> ArticleMappingsList { get; } = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await LoadDefaultItemsAsync();
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await LoadDefaultItemsAsync();
        }
    }

    private async Task LoadDefaultItemsAsync()
    {
        await ResetDefaultFilterAsync();

        await LoadArticleMappingsAsync();
    }

    private async Task LoadArticleMappingsAsync()
    {
        ArticleMappingsList.Clear();

        var result = await ArticleMappingService.LoadArticleMappingsAsync(DefaultFilter);

        ArticleMappingsList.AddRange(result.Items);
    }

    private async Task LoadArticleMappingsBatchAsync()
    {
        ArticleMappingsList.Clear();

        var result = await ArticleMappingService.LoadArticleMappingsAsync(BatchFilter);

        ArticleMappingsList.AddRange(result.Items);
    }

    private void OnAddCommand()
    {
        DialogEditor.ShowDialog(new() { Customer = CurrentCustomerId }, true);
    }

    private void OnDetailsCommand(GridCommandEventArgs e)
    {
        DialogEditor.ShowDialog(e.Item as ArticleMapping, false);
    }

    private async Task ResetDefaultFilterAsync()
    {
        DefaultFilter = new() { Customer = CurrentCustomerId, MaxPageSize = ControlConstants.DefaultPageSize };

        await InvokeAsync(StateHasChanged);
    }

    private async Task ResetBatchFilterAsync()
    {
        BatchFilter = new() { Customer = CurrentCustomerId, MaxPageSize = ControlConstants.DefaultPageSize };

        BatchFilterKeys = string.Empty;

        await InvokeAsync(StateHasChanged);
    }

    public async void OnSaveAction()
    {
        var articleMapping = DialogEditor.ArticleMapping;

        try
        {
            if (DialogEditor.IsNew)
                await ArticleMappingService.CreateArticleMappingAsync(articleMapping);
            else
                await ArticleMappingService.UpdateArticleMappingAsync(articleMapping);
        }
        catch (Exception exception)
        {
            ToastService.ShowError($"The article mapping could not be saved {exception.Message}");
        }
        finally
        {
            await LoadArticleMappingsAsync();
        }
    }
}