﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
{
    public partial class IntersolveOrderSummary
    {
        [Parameter]
        public string OrderId { get; set; }

        [Parameter]
        public IntersolveOrderDetails Order { get; set; }

        [Parameter]
        public string Customer { get; set; }

        [Parameter]
        public RenderFragment OrderDetailsLink { get; set; }

        private string GetFlowOrderTypesString() =>
            Order.OrderTypes.Count == 0 ?
                Order.OrderMetadataDefinition.Flow :
                $"({string.Join(" + ", Order.OrderTypes)}) {Order.OrderMetadataDefinition.Flow}";

        private string GetConcatinatedCategories()
        {
            var categories = new string[] { Order.OrderMetadataDefinition.Categories.One, Order.OrderMetadataDefinition.Categories.Two, Order.OrderMetadataDefinition.Categories.Three };
            var jointCategories = string.Join("-", categories.Where(x => !string.IsNullOrEmpty(x)));
            return !string.IsNullOrEmpty(jointCategories) ? jointCategories : "None";
        }
    }
}
