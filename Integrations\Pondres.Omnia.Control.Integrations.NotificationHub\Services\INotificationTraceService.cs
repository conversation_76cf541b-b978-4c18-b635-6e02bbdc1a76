﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Services;

public interface INotificationTraceService
{
    Task<PagedResultModel<NotificationTraceListItem>> GetAllTracesForFilterAsync(NotificationTraceListFilter filter);
    Task<NotificationTraceDetails> GetTraceDetailsAsync(string traceId, string customer);

    Task<NotificationTraceRetryResponse> RetryTraceFailedOutputsForCustomerAsync(string traceId, string customer);

    Task<List<string>> GetEventTypesAsync();

    List<string> GetStatusTypes();

    Task<NotificationTraceRetryResponse> RetryOutputAsync(string outputId, string traceId, string customer);

    Task CancelOutputAsync(Guid outputId);
}
