﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintBundleDefaultFilterFields : FilterFieldsBase<PrintBundleDefaultFilterFields>
{

    public PrintBundleDefaultFilterFields()
    {
        PageSize = 500;
    }

    public string BundleId { get; set; }
    public bool ShowEmptyBundles { get; set; }
    public string Customer { get; set; }
    public string InitiatedBy { get; set; }
    public string BatchName { get; set; }
    public string DGCode { get; set; }
    public string GordNumber { get; set; }
    public string Carrier { get; set; }
    public string PrintMode { get; set; }
    public string PrinterType { get; set; }
    public bool ShowTestCustomers { get; set; }
    public string StateTypeName { get; set; } = PrintBundleStateType.Active.ToString();
    public DateTimePickerFilterFields MailDate { get; set; } = new DateTimePickerFilterFields();
    public DateTimePickerFilterFields CreatedOn { get; set; } = new DateTimePickerFilterFields();
}
