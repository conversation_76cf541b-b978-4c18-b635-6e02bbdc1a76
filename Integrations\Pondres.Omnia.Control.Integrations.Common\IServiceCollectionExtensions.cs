﻿using Blazored.Toast;
using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Common.Provicers;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Common;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddCommonServices(this IServiceCollection services, AppSettings appSettings) => services
        .AddSingleton<IDistributedCacheProvider, DistributedCacheProvider>()
        .AddStackExchangeRedisCache(options =>
        {
            options.Configuration = appSettings.RedisConnectionString;
        })
        .AddTransient<IClipboardManager, ClipboardManager>()
        .AddTransient<IApiFilterManager, ApiFilterManager>()
        .AddSingleton<IFilterCacheService, FilterCacheService>()
        .AddScoped<InfiniteScrollHelper>()
        .AddBlazoredToast();
}