﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.OrderHub.Contracts.Api.Order

<GridCard Title="Order Confirmation Triggers"
          GetRawColorFunc="(trigger) => trigger.GetStatusColorStyle()"
          GetItemsAsync="(_) => Task.FromResult(Order.ConfirmationTriggers.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Type name</th>
        <th>Received on</th>
    </TableHeaderContent>
    <TableRowContent Context="trigger">
        <td>@trigger.TypeName</td>
        <td>@trigger.ReceivedOn?.LocalDateTime</td>
    </TableRowContent>
</GridCard>


