﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Services;

public interface IWarehouseResponseService
{
    Task<WarehouseResponseDetails> GetResponseDetailsAsync(string fileName);

    Task<FileModel> GetFileContentsAsync(string filename);

    Task<PagedResultModel<WarehouseResponseListItem>> GetAllResponsesByFilterAsync(WarehouseResponseListFilter filter);

    Task<PagedResultModel<WarehouseResponseListItem>> GetDeliveriesForOrderAsync(Guid orderId, string customer);

    Task ReprocessFileAsync(string fileName);

    Task<List<string>> GetMessageTypesAsync();

    Task<CustomerConfiguration> GetCustomerConfigurationAsync(string customer);

    Task CreateCustomerConfigurationAsync(CustomerConfiguration configuration);

    Task UpdateCustomerConfigurationAsync(CustomerConfiguration configuration);
}
