﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Pondres.Omnia.Control.Integrations.Print.Services;
using System.Collections.ObjectModel;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Document;

public partial class DocumentsGrid : SelectableGridBase<PrintDocumentListItem>
{
    [Inject]
    NavigationManager Navigation { get; set; }
    [Inject]
    IPrintService PrintService { get; set; }

    [Parameter]
    public ObservableCollection<PrintDocumentListItem> Documents { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    [Parameter]
    public EventCallback OnRefresh { get; set; }

    private PrintDocumentReprintSelectedModal PrintDocumentReprintSelectedDialog { get; set; }

    [Inject]
    public AuthenticationStateProvider AuthenticationStateProvider { get; set; }

    [Parameter]
    public RenderFragment<PrintDocumentListItem> OrderDetailsLink { get; set; }

    private IEnumerable<string> SelectedUniqueDocumentBundleIds => DataGrid.SelectedItems
            .Where(x => x.LastBundle != null)
            .Select(x => x.LastBundle.BundleId.ToString())
            .Distinct();

    private void OnRowDoubleClick(GridRowClickEventArgs e)
    {
        var filter = new PrintBundleDefaultFilterFields
        {
            BundleId = (e.Item as PrintDocumentListItem).LastBundle.BundleId.ToString(),
            ShowEmptyBundles = true,
            ShowTestCustomers = true
        };

        Navigation.NavigateToAllPrintBundlesWithDefaultFilter(filter);
    }

    public static void OnCellRender(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as PrintDocumentListItem).GetStatusColorStyle();

    public async Task ReprintDocumentsAsync()
    {
        var documentIds = DataGrid.SelectedItems.Select(d => d.OrderMetadata.OrderId).ToList();
        var customer = DataGrid.SelectedItems.FirstOrDefault()?.OrderMetadata.Customer;

        var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();

        await PrintService.ReprintSelectedPrintDocumentsAsync(
            orderIds: documentIds,
            customer: customer,
            username: state.GetUsername());

        ToastService.ShowInfo("Reprints requested");
    }

    private void OnReprintConfirmationCommand()
    {
        PrintDocumentReprintSelectedDialog.ShowDialog(DataGrid.SelectedItems.ToList());
    }

    private bool SelectedDocumentsCanBeConfirmed
    {
        get
        {
            return HasSelectedItems;
        }
    }
}
