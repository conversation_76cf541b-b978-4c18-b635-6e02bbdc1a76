﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Email.Client;
using Pondres.Omnia.Control.Integrations.Email.Models;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Email
{
    public static class IServiceCollectionExtensions
    {
        public static IServiceCollection AddEmailServices(this IServiceCollection services, AppSettings appSettings)
        {
            services.AddHttpClient<IEmailDeliveryService, EmailDeliveryService>()
                .ConfigureBaseAddress(appSettings.EmailServiceUri)
                .ConfigureAuthenticationToken(appSettings.EmailServiceAuthToken);

            services.AddHttpClient(name: "emailClient")
                .ConfigureBaseAddress(appSettings.EmailServiceUri)
                .ConfigureAuthenticationToken(appSettings.EmailServiceAuthToken);

            services.AddHttpClient<EmailServiceClient>("emailClient");

            services.AddTransient<EmailDeliveryBatchFilterContext>();
            services.AddTransient<EmailDeliveryDefaultFilterContext>();

            return services;
        }
    }
}
