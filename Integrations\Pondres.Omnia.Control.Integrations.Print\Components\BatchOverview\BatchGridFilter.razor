﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

<TabPanel TDataType="IPrintBatchFilterContext">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IPrintBatchFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IPrintBatchFilterContext">
                <TabPanelTab TDataType="IPrintBatchFilterContext" Name="Default" Data="Filter"
                             Selected="FilterManager.CurrentFilter == Filter">
                    <Template>
                        <BatchOverviewFilter Filter="Filter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>