﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Exceptions;
using System.Runtime.CompilerServices;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Modal;

public partial class ActionModal<T> : ModalBase
{
    [Parameter]
    public string Width { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public string ButtonText { get; set; }

    [Parameter]
    public string ButtonType { get; set; }

    [Parameter]
    public RenderFragment<T> ChildContent { get; set; }

    [Parameter]
    public EventCallback<T> OnSubmit { get; set; }

    [Parameter]
    public EventCallback OnClose { get; set; }

    [Parameter]
    public bool ShowEmptyContext { get; set; }

    private T Context { get; set; }

    private TelerikDialog Dialog { get; set; }

    private List<string> errorMessages = new();

    public List<string> ErrorMessages
    {
        get => errorMessages;
        set
        {
            errorMessages = value;
            Dialog.Refresh();
        }
    }

    public void OpenModal(T context)
    {
        Context = context;
        ErrorMessages = new List<string>();

        IsVisible = true;
    }

    public async Task CloseModalAsync()
    {
        Context = default;

        IsVisible = false;
        await OnClose.InvokeAsync();

        await InvokeAsync(StateHasChanged);
    }

    private async Task SubmitModalAsync()
    {
        try
        {
            await OnSubmit.InvokeAsync(Context);
            await CloseModalAsync();
        }
        catch (InvalidFormException ex)
        {
            ErrorMessages = ex.Errors;
        }
        catch (ApiException exception)
        {
            ErrorMessages = new List<string> { $"{exception.StatusCode}: {exception.Message}" };
        }
        catch (Exception ex)
        {
            ErrorMessages = new List<string> { ex.Message };
        }

        await InvokeAsync(StateHasChanged);
    }
}