﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

@inherits ModalBase
    
<TelerikDialog Visible="@IsVisible" Width="350px">
    <DialogTitle>
        <strong>Cancel bundle</strong>
    </DialogTitle>
    <DialogContent>
        <span><strong>WARNING:</strong> You are about to cancel this bundle. Are you sure?</span>
        <TelerikForm Model="@Model" 
                     OnValidSubmit="@OnSubmitModalAsync"
                     ButtonsLayout="FormButtonsLayout.End">
            <FormValidation>
                <DataAnnotationsValidator />
            </FormValidation>
            <FormItems>
                <AuthorizeView Roles="ControlOwner">
                    <FormItem Field="@nameof(Model.Force)" LabelText="Force cancel" />
                    <FormItem Field="@nameof(Model.Reprint)" LabelText="Reprint" />
                </AuthorizeView>
            </FormItems>
            <FormButtons>
                <TelerikButton Enabled="@ConfirmButtonEnabled" ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)">Ok</TelerikButton>
                <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
</TelerikDialog>