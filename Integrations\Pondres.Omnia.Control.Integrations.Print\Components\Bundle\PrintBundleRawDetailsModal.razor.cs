﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Bundle
{
    public partial class PrintBundleRawDetailsModal : ModalBase
    {
        [Parameter]
        public EventCallback OnCloseAction { get; set; }
        public PrintBundleRawDetails Model { get; private set; }

        public async Task ShowDialogAsync(PrintBundleRawDetails model)
        {
            Model = model;

            IsVisible = true;

            await InvokeAsync(StateHasChanged);
        }

        public async Task CloseDialogAsync()
        {
            if (OnCloseAction.HasDelegate)
                await OnCloseAction.InvokeAsync();

            IsVisible = false;
        }
    }
}
