using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration;

namespace Pondres.Omnia.Control.Pages.Warehouse.Configuration;

public partial class Configuration : CustomerPageBase
{
    [Inject]
    protected IToastService ToastService { get; set; }

    [Inject]
    protected IWarehouseResponseService WarehouseResponseService { get; set; }

    public CustomerConfiguration CustomerConfiguration { get; set; }

    public bool HasConfiguration => CustomerConfiguration != null;

    public async Task OnConfigurationChangedAsync() =>
        await LoadConfigurationAsync();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        await LoadConfigurationAsync();
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
        {
            CurrentCustomerId = customerId;

            await LoadConfigurationAsync();
        }
    }

    private async Task LoadConfigurationAsync()
    {
        CustomerConfiguration = null;

        try
        {
            CustomerConfiguration = await WarehouseResponseService.GetCustomerConfigurationAsync(CurrentCustomerId);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }

        await InvokeAsync(StateHasChanged);
    }
}