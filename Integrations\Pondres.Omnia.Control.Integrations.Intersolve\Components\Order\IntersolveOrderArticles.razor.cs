﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Order
{
    public partial class IntersolveOrderArticles
    {
        [Parameter]
        public IntersolveOrderDetails Order { get; set; }

        public Task<PagedResultModel<IntersolveOrderArticle>> GetNextItemsAsync(string continuationToken) => 
            Task.FromResult(new PagedResultModel<IntersolveOrderArticle>
            {
                ContinuationToken = null,
                Items = [.. Order.IntersolveArticles]
            });
    }
}
