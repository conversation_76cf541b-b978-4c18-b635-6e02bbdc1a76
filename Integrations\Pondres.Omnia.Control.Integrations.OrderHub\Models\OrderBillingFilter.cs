﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Models;

public class OrderBillingFilter
{
    public string Customer { get; set; }
    public DateTimePickerFilterFields CreatedOnFilter { get; set; } = new();
    public DateTimePickerFilterFields CompletedOnFilter { get; set; } = DateTimePickerFilterFields.Today();

    public OrderBillingFilter()
    {
    }

    public void Merge(OrderBillingFilter filter)
    {
        if (filter == null)
            return;

        Customer = filter.Customer;
        CreatedOnFilter = filter.CreatedOnFilter;
        CompletedOnFilter = filter.CompletedOnFilter;
    }

    public OrderLightFilter ToApiFilter()
    {
        var filter = new OrderLightFilter
        {
            Customer = Customer,
            CreatedFromDate = CreatedOnFilter.GetFromDate(),
            CreatedToDate = CreatedOnFilter.GetToDate(),
            CompletedFromDate = CompletedOnFilter.GetFromDate(),
            CompletedToDate = CompletedOnFilter.GetToDate(),
        };

        return filter;
    }
}
