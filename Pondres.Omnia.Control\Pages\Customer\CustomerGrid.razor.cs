﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Customer.Models;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Telerik.Blazor;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Customer;

public partial class CustomerGrid : SelectableGridBase<CustomerModel>
{
    public CustomerDetailsModal DialogEditor { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    [Inject]
    protected ICustomerService CustomerDataService { get; set; }

    public List<CustomerModel> Customers { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadCustomersAsync();

        await base.OnInitializedAsync();
    }

    private async Task LoadCustomersAsync()
    {
        await InvokeAsync(StateHasChanged);

        try
        {
            Customers = await CustomerDataService.GetAllCustomersAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    public async Task OnSaveActionAsync()
    {
        try
        {
            if (DialogEditor.IsNew)
                await CustomerDataService.CreateCustomerAsync(DialogEditor.Customer);
            else
                await CustomerDataService.UpdateCustomerAsync(DialogEditor.Customer);

            await LoadCustomersAsync();

            await DialogEditor.CloseDialogAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private async Task OnDisableCommandAsync(GridCommandEventArgs e)
    {
        bool isConfirmed = await Dialogs.ConfirmAsync("Disabling a customer will stop it from being used remove it from the list but not delete it. Proceed?", title: "Disable customer (soft delete)");

        if (isConfirmed)
        {
            await CustomerDataService.DisableCustomerAsync((e.Item as CustomerModel).Id);
            await LoadCustomersAsync();
        }
    }

    public void OnAddCommand()
    {
        DialogEditor.ShowDialog(new(), true);
    }

    private async Task EditCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var customer = await CustomerDataService.GetCustomerDetailsAsync((e.Item as CustomerModel).Id);

            DialogEditor.ShowDialog(customer, false);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }
}