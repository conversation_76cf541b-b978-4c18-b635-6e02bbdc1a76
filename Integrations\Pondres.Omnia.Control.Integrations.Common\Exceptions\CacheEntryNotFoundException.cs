﻿using System.Runtime.Serialization;

namespace Pondres.Omnia.Control.Integrations.Common.Exceptions
{
    [Serializable]
    public class CacheEntryNotFoundException : Exception
    {
        public CacheEntryNotFoundException(string key) : base($"{key} not found in cache")
        {
        }

        protected CacheEntryNotFoundException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}