﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32002.185
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{DA5C7B8B-B884-419E-AA38-33ED8612B116}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control", "Pondres.Omnia.Control\Pondres.Omnia.Control.csproj", "{F26433C9-FD9B-4EDA-8D53-16F85B963866}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Integrations", "Integrations", "{8A73B18F-4895-48C7-8055-60CC55B82293}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.OrderHub", "Integrations\Pondres.Omnia.Control.Integrations.OrderHub\Pondres.Omnia.Control.Integrations.OrderHub.csproj", "{938616BF-AF3F-40A1-9612-5F1478897EA2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Print", "Integrations\Pondres.Omnia.Control.Integrations.Print\Pondres.Omnia.Control.Integrations.Print.csproj", "{1A92832C-E631-4BAD-95D5-89AFBE32FCAD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.ESafe", "Integrations\Pondres.Omnia.Control.Integrations.ESafe\Pondres.Omnia.Control.Integrations.ESafe.csproj", "{B415B61C-85B5-499D-9F8E-8C83333F5521}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Warehouse", "Integrations\Pondres.Omnia.Control.Integrations.Warehouse\Pondres.Omnia.Control.Integrations.Warehouse.csproj", "{04642981-7D9E-40D3-ADD0-3CA9243432D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Common", "Integrations\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj", "{EC287027-60A7-4712-AF34-BBB1EA3A4611}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.NotificationHub", "Integrations\Pondres.Omnia.Control.Integrations.NotificationHub\Pondres.Omnia.Control.Integrations.NotificationHub.csproj", "{2BCCE939-3BBC-413C-9E9D-68BF46AD8F48}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Customer", "Integrations\Pondres.Omnia.Control.Integrations.Customer\Pondres.Omnia.Control.Integrations.Customer.csproj", "{41671FD2-113F-49FC-A112-C574F9F1A917}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.OpsGenie", "Integrations\Pondres.Omnia.Control.Integrations.OpsGenie\Pondres.Omnia.Control.Integrations.OpsGenie.csproj", "{E4A2B71F-E345-4791-8474-B38F76511D9A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.SecureMail", "Integrations\Pondres.Omnia.Control.Integrations.SecureMail\Pondres.Omnia.Control.Integrations.SecureMail.csproj", "{31081DA5-9ACA-4AB3-B243-9B8455FE4A70}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{9CB786DA-0217-4BCC-9F90-ABC06EF792EE}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Email", "Integrations\Pondres.Omnia.Control.Integrations.Email\Pondres.Omnia.Control.Integrations.Email.csproj", "{0D094B6E-C0A4-42C4-8FBA-BA1FD9785CE3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Demo", "Integrations\Pondres.Omnia.Control.Integrations.Demo\Pondres.Omnia.Control.Integrations.Demo.csproj", "{E0DE6D42-8D57-4FEA-824C-E1BA033E5C26}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Web2Omnia", "Integrations\Pondres.Omnia.Control.Integrations.Web2Omnia\Pondres.Omnia.Control.Integrations.Web2Omnia.csproj", "{9E73DC8F-5703-43F3-A139-D710434A0C89}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Common", "Pondres.Omnia.Control.Common\Pondres.Omnia.Control.Common.csproj", "{A95C6165-E793-400F-9C12-A0EBC03BD7A4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Control.Integrations.Intersolve", "Integrations\Pondres.Omnia.Control.Integrations.Intersolve\Pondres.Omnia.Control.Integrations.Intersolve.csproj", "{1D150E4D-C2B3-4841-9A9C-0A744E5A22AC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DA5C7B8B-B884-419E-AA38-33ED8612B116}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA5C7B8B-B884-419E-AA38-33ED8612B116}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA5C7B8B-B884-419E-AA38-33ED8612B116}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA5C7B8B-B884-419E-AA38-33ED8612B116}.Release|Any CPU.Build.0 = Release|Any CPU
		{F26433C9-FD9B-4EDA-8D53-16F85B963866}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F26433C9-FD9B-4EDA-8D53-16F85B963866}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F26433C9-FD9B-4EDA-8D53-16F85B963866}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F26433C9-FD9B-4EDA-8D53-16F85B963866}.Release|Any CPU.Build.0 = Release|Any CPU
		{938616BF-AF3F-40A1-9612-5F1478897EA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{938616BF-AF3F-40A1-9612-5F1478897EA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{938616BF-AF3F-40A1-9612-5F1478897EA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{938616BF-AF3F-40A1-9612-5F1478897EA2}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A92832C-E631-4BAD-95D5-89AFBE32FCAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A92832C-E631-4BAD-95D5-89AFBE32FCAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A92832C-E631-4BAD-95D5-89AFBE32FCAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A92832C-E631-4BAD-95D5-89AFBE32FCAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{B415B61C-85B5-499D-9F8E-8C83333F5521}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B415B61C-85B5-499D-9F8E-8C83333F5521}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B415B61C-85B5-499D-9F8E-8C83333F5521}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B415B61C-85B5-499D-9F8E-8C83333F5521}.Release|Any CPU.Build.0 = Release|Any CPU
		{04642981-7D9E-40D3-ADD0-3CA9243432D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04642981-7D9E-40D3-ADD0-3CA9243432D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04642981-7D9E-40D3-ADD0-3CA9243432D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04642981-7D9E-40D3-ADD0-3CA9243432D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{EC287027-60A7-4712-AF34-BBB1EA3A4611}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC287027-60A7-4712-AF34-BBB1EA3A4611}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC287027-60A7-4712-AF34-BBB1EA3A4611}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC287027-60A7-4712-AF34-BBB1EA3A4611}.Release|Any CPU.Build.0 = Release|Any CPU
		{2BCCE939-3BBC-413C-9E9D-68BF46AD8F48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2BCCE939-3BBC-413C-9E9D-68BF46AD8F48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2BCCE939-3BBC-413C-9E9D-68BF46AD8F48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2BCCE939-3BBC-413C-9E9D-68BF46AD8F48}.Release|Any CPU.Build.0 = Release|Any CPU
		{41671FD2-113F-49FC-A112-C574F9F1A917}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41671FD2-113F-49FC-A112-C574F9F1A917}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41671FD2-113F-49FC-A112-C574F9F1A917}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41671FD2-113F-49FC-A112-C574F9F1A917}.Release|Any CPU.Build.0 = Release|Any CPU
		{E4A2B71F-E345-4791-8474-B38F76511D9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4A2B71F-E345-4791-8474-B38F76511D9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4A2B71F-E345-4791-8474-B38F76511D9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4A2B71F-E345-4791-8474-B38F76511D9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{31081DA5-9ACA-4AB3-B243-9B8455FE4A70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31081DA5-9ACA-4AB3-B243-9B8455FE4A70}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31081DA5-9ACA-4AB3-B243-9B8455FE4A70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31081DA5-9ACA-4AB3-B243-9B8455FE4A70}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D094B6E-C0A4-42C4-8FBA-BA1FD9785CE3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D094B6E-C0A4-42C4-8FBA-BA1FD9785CE3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D094B6E-C0A4-42C4-8FBA-BA1FD9785CE3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D094B6E-C0A4-42C4-8FBA-BA1FD9785CE3}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0DE6D42-8D57-4FEA-824C-E1BA033E5C26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0DE6D42-8D57-4FEA-824C-E1BA033E5C26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0DE6D42-8D57-4FEA-824C-E1BA033E5C26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0DE6D42-8D57-4FEA-824C-E1BA033E5C26}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E73DC8F-5703-43F3-A139-D710434A0C89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E73DC8F-5703-43F3-A139-D710434A0C89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E73DC8F-5703-43F3-A139-D710434A0C89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E73DC8F-5703-43F3-A139-D710434A0C89}.Release|Any CPU.Build.0 = Release|Any CPU
		{A95C6165-E793-400F-9C12-A0EBC03BD7A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A95C6165-E793-400F-9C12-A0EBC03BD7A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A95C6165-E793-400F-9C12-A0EBC03BD7A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A95C6165-E793-400F-9C12-A0EBC03BD7A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D150E4D-C2B3-4841-9A9C-0A744E5A22AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D150E4D-C2B3-4841-9A9C-0A744E5A22AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D150E4D-C2B3-4841-9A9C-0A744E5A22AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D150E4D-C2B3-4841-9A9C-0A744E5A22AC}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{938616BF-AF3F-40A1-9612-5F1478897EA2} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{1A92832C-E631-4BAD-95D5-89AFBE32FCAD} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{B415B61C-85B5-499D-9F8E-8C83333F5521} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{04642981-7D9E-40D3-ADD0-3CA9243432D3} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{EC287027-60A7-4712-AF34-BBB1EA3A4611} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{2BCCE939-3BBC-413C-9E9D-68BF46AD8F48} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{41671FD2-113F-49FC-A112-C574F9F1A917} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{E4A2B71F-E345-4791-8474-B38F76511D9A} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{31081DA5-9ACA-4AB3-B243-9B8455FE4A70} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{0D094B6E-C0A4-42C4-8FBA-BA1FD9785CE3} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{E0DE6D42-8D57-4FEA-824C-E1BA033E5C26} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{9E73DC8F-5703-43F3-A139-D710434A0C89} = {8A73B18F-4895-48C7-8055-60CC55B82293}
		{1D150E4D-C2B3-4841-9A9C-0A744E5A22AC} = {8A73B18F-4895-48C7-8055-60CC55B82293}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7C9D9AC2-AD4E-4EED-9030-70012EEF2419}
	EndGlobalSection
EndGlobal
