﻿@page "/Configuration/Warehouse"

@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Configuration
@using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration

@inherits CustomerPageBase

<PageHeader Title="Warehouse Configuration">
    <CustomerSelection Customers="@Customers" CustomerChanged="OnCustomerChangedAsync" SelectedCustomerId="@CurrentCustomerId" />
</PageHeader>

<PageBody>
    @if(HasConfiguration)
    { 
        <WarehouseConfiguration ConfigurationModel="CustomerConfiguration" Customer="@CurrentCustomerId" OnModalSubmit="OnConfigurationChangedAsync" />
    }
</PageBody>


