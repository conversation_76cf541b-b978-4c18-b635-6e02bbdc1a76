using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace;

public partial class NotificationTraceOutputGrid
{
    [Inject]
    private INotificationTraceService NotificationTraceService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Parameter]
    public string TraceId { get; set; }

    [Parameter]
    public EventCallback<string> OnFileOpened { get; set; }

    [Parameter]
    public EventCallback<string> OnFileDownloaded { get; set; }

    [Parameter]
    public List<NotificationTraceOutput> Outputs { get; set; }

    [Parameter]
    public string Customer { get; set; }

    private ActionModal<NotificationTraceOutput> RetryOutputModal { get; set; }

    private ActionModal<NotificationTraceOutput> CancelOutputModal { get; set; }

    private async Task RetryOutputAsync(NotificationTraceOutput output)
    {
        var result = await NotificationTraceService.RetryOutputAsync(output.Id, TraceId, Customer);
        if (!result.Retried)
            ToastService.ShowError($"Failed to retry output: {result.Message}");
    }

    private async Task CancelOutputAsync(NotificationTraceOutput output) =>
        await NotificationTraceService.CancelOutputAsync(Guid.Parse(output.Id));
}
