﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;

namespace Pondres.Omnia.Control.Integrations.Common.Models.Filter;

public abstract class QueryStringFilterContext<TFilterResultType, TFilterFields> : FilterContextBase<TFilterResultType, TFilterFields>
    where TFilterFields : FilterFieldsBase<TFilterFields>, new()
{
    protected abstract void NavigateToFilter();

    protected QueryStringFilterContext(NavigationManager navigationManager)
        : base(navigationManager)
    { }

    public override Task MergeFromCurrentUriAsync()
    {
        var filterFromQuery = FilterHelper.GetFilterFromUri<TFilterFields>(navigationManager.ToAbsoluteUri(navigationManager.Uri));

        Fields.Merge(filterFromQuery);

        return Task.CompletedTask;
    }

    public override Task NavigateToFilterAsync()
    {
        NavigateToFilter();

        return Task.CompletedTask;
    }
}
