﻿using Pondres.Omnia.Control.Integrations.OpsGenie.Models;

namespace Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;

public static class AlertsViewModelExtensions
{
    public static string GetNotAcknowledgedBgColor(this AlertsViewModel model) =>
        model.HasHighPriorityUnacknowledged() ? "bg-danger text-white" : "bg-success text-white";

    private static bool HasHighPriorityUnacknowledged(this AlertsViewModel model) =>
        model.P1.HasUnacknowledged ||
        model.P2.HasUnacknowledged ||
        model.P3.HasUnacknowledged;
}
