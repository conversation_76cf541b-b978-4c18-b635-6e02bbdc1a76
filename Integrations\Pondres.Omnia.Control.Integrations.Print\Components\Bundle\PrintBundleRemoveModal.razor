﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

@inherits ModalBase
    
<TelerikDialog Visible="@IsVisible" Width="350px">
    <DialogTitle>
        <strong>Remove document and retry impositioning</strong>
    </DialogTitle>
    <DialogContent>
        <TelerikForm Model="@Model" 
                     OnValidSubmit="@OnSubmitModalAsync"
                     ButtonsLayout="FormButtonsLayout.End">
            <FormValidation>
                <DataAnnotationsValidator />
            </FormValidation>
            <FormItems>
                <span>Enter the id of the document that should be removed.</span>
                <AuthorizeView Roles="ControlOwner">           
                    <FormItem Field="@nameof(Model.DocumentId)" LabelText="Document ID" />
                </AuthorizeView>
            </FormItems>
            <FormButtons>
                <TelerikButton Enabled="@ConfirmButtonEnabled" ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Ok</TelerikButton>
                <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
</TelerikDialog>