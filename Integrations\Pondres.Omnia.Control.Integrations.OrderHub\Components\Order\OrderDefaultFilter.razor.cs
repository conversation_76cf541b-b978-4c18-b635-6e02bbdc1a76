using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Order;

public partial class OrderDefaultFilter
{
    [Inject]
    protected IToastService ToastService { get; set; }

    [Parameter]
    public OrderDefaultFilterContext Filter { get; set; }

    [Parameter]
    public EventCallback<OrderDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    private async Task OnFilterChangedAsync()
    {
        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }
}