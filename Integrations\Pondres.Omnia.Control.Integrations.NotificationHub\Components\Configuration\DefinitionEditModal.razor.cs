﻿using Blazored.Toast.Services;
using BlazorMonaco.Editor;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Pondres.Omnia.Control.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.Common.Exceptions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Definition;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration;

public partial class DefinitionEditModal
{
    [Inject]
    private INotificationDefinitionService DefinitionService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    [Inject]
    private IJSRuntime JS { get; set; }

    [Parameter]
    public EventCallback OnSubmit { get; set; }

    private ActionModal<NotificationDefinitionModel> ActionModal { get; set; }

    private StandaloneCodeEditor Editor { get; set; }

    public DateTimeOffset? FailSilentlyUntil { get; set; }

    private NotificationDefinitionModel Definition { get; set; } = new();

    private DefinitionModalType Type { get; set; }

    private string definitionSchema;

    public async Task OpenModalAsync(DefinitionModalType type, NotificationDefinitionModel definition)
    {
        try
        {
            Type = type;
            Definition = definition;
            FailSilentlyUntil = definition.FailSilentlyUntil;

            ActionModal.OpenModal(definition);

            await SetEditorSchemaAsync();

            if (Type == DefinitionModalType.Edit)
                await SetEditorValueAsync();
            else
                await ResetDialogAsync();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private async Task ResetDialogAsync() =>
        await Editor.SetValue("{\n\n}");

    private string GetDialogTitle() =>
        Type == DefinitionModalType.Edit ? "Edit definition" : "Create definition";

    private async Task SetEditorValueAsync()
    {
        var definitionJson = await DefinitionService.GetDefinitionAsync(Definition.Id, Definition.Type);

        await Editor.SetValue(definitionJson);
    }

    private async Task SetEditorSchemaAsync()
    {
        definitionSchema = await DefinitionService.GetDefinitionSchemaAsync(Definition.Type, Definition.Customer);

        await JS.InvokeVoidAsync("monacoJsonSchema.setJsonSchema", definitionSchema);
    }

    private async Task SubmitModalAsync()
    {
        var editorValue = await Editor.GetValue();

        ValidateDefinition(editorValue);

        if (Type == DefinitionModalType.Create)
            await DefinitionService.CreateDefinitionAsync(Definition.ToUpsertModel(editorValue, FailSilentlyUntil));
        else
            await DefinitionService.UpdateDefinitionAsync(Definition.ToUpsertModel(editorValue, FailSilentlyUntil));

        await ResetDialogAsync();
        await OnSubmit.InvokeAsync();

        await InvokeAsync(StateHasChanged);
    }

    private void ValidateDefinition(string definitionValue)
    {
        var errorList = new List<string>();

        if (string.IsNullOrWhiteSpace(Definition.Id))
        {
            errorList.Add("Name is required");
        }

        var validationResult = definitionValue.ValidateJsonWithSchema(definitionSchema);

        if (!validationResult.IsValid)
        { 
           errorList.AddRange(validationResult.ErrorMessages());  
        }

        if (errorList.Any())
        {
            throw new InvalidFormException(errorList);
        }
    }

    private static StandaloneEditorConstructionOptions EditorConstructionOptions(Editor editor) =>
        new()
        {
            AutomaticLayout = true,
            FixedOverflowWidgets = true,
            Language = "json",
            Value = "{\n\n}",
            AutoIndent = "true",
        };
}