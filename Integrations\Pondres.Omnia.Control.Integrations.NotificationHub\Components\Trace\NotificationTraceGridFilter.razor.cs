using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace;

public partial class NotificationTraceGridFilter
{
    [Inject]
    private IApiFilterManager FilterManager { get; set; }

    [Inject]
    private NotificationTraceDefaultFilterContext defaultFilter { get; set; }

    [Parameter]
    public EventCallback<INotificationTraceFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(defaultFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<INotificationTraceFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);

        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(INotificationTraceFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);

        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(filter);
    }
}