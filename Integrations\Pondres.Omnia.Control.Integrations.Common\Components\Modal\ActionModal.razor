﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@inherits ModalBase

@typeparam T

<TelerikDialog @bind-Visible="IsVisible" @ref="Dialog" Width="@Width">
    <DialogTitle>
        @Title
    </DialogTitle>
    <DialogContent>
        <Alert Messages="@ErrorMessages" />
        @if (ShowEmptyContext || Context != null)
        {
            @ChildContent(Context)
        }
    </DialogContent>
    <DialogButtons>
        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Secondary)" OnClick="CloseModalAsync">Close</TelerikButton>
        @if (OnSubmit.HasDelegate)
        {
            <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="SubmitModalAsync">@ButtonText</TelerikButton>
        }
    </DialogButtons>
</TelerikDialog>