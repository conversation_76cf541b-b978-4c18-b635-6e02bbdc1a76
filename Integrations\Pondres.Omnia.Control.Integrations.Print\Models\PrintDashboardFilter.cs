﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Common.Extensions;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintDashboardFilter
{
    public string Customer { get; set; }
    public string PrinterType { get; set; } = ControlConstants.DefaultDropdownValue;
    public bool ShowTestCustomers { get; set; }
    public DateTimePickerFilterFields CreatedOn { get; set; }

    public PrintBundleTotalDisplayOptionType DisplayOptionType { get; set; }

    public PrintDashboardFilter()
    {
        var today = DateTime.Today;
        var firstDayOfCurrentMonth = new DateTime(today.Year, today.Month, 1).Date;
        var lastDayOfCurrentMonth = firstDayOfCurrentMonth.AddMonths(1).AddDays(-1);

        CreatedOn = new DateTimePickerFilterFields
        {
            FromDate = firstDayOfCurrentMonth,
            ToDate = lastDayOfCurrentMonth
        };
    }

    public void Merge(PrintDashboardFilter filter)
    {
        if (filter == null)
            return;

        Customer = filter.Customer;
        PrinterType = filter.PrinterType;
        CreatedOn = filter.CreatedOn;
        ShowTestCustomers = filter.ShowTestCustomers;
        DisplayOptionType = filter.DisplayOptionType;
    }

    public PrintBundleDateRangeFilter ToApiFilter() =>
        new()
        {
            Customer = Customer,
            PrinterType = PrinterType == ControlConstants.DefaultDropdownValue ? null : PrinterType.ToNullableEnum<PrinterType>(),
            IncludeHiddenCustomers = ShowTestCustomers,

            CreatedOnFrom = CreatedOn.FromDate.Value.Date,
            CreatedOnTo = CreatedOn.ToDate.Value.Date.AddDays(1).AddTicks(-1)
        };
}
