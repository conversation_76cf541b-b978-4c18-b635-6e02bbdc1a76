﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Components.Order;

public partial class OrdersGridFilter : ComponentBase
{
    [Inject]
    private OrderBatchFilterContext BatchFilter { get; set; }

    [Inject]
    private OrderDefaultFilterContext DefaultFilter { get; set; }

    [Inject]
    private IApiFilterManager FilterManager { get; set; }

    [Parameter]
    public EventCallback<IOrderFilterContext> FilterChanged { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilter, BatchFilter);

        await FilterManager.SetInitialFilterFromUriAsync();

        await base.OnInitializedAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<IOrderFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);

        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(IOrderFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);

        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(filter);
    }
}