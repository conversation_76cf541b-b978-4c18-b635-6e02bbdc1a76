﻿using Pondres.Omnia.OrderHub.Contracts.Api.Order;
using Pondres.Omnia.OrderHub.Contracts.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderStatusInformationExtensions
{
    public static string ToStatusColorClass(this OrderStatusInformation status) =>
        status.StateType switch
        {
            OrderStateType.Active => "table-primary",
            OrderStateType.Waiting => "table-info",
            OrderStateType.Completed => "table-success-light",
            OrderStateType.OnHold => "table-danger",
            OrderStateType.Blocked => "table-danger",
            OrderStateType.Failed => "table-warning",
            _ => "table-light",
        };
}
