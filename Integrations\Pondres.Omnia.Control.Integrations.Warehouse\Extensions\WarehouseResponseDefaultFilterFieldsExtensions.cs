﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Extensions;

public static class Extensions
{
    public static WarehouseResponseListFilter ToApiFilter(this WarehouseResponseDefaultFilterFields fields, string nextContinuationToken) =>
        new()
        {
            FileName = fields.FileName,
            MessageType = fields.MessageType.ToDropdownInvariantValue(),
            StateNameType = fields.StateTypeName.ToDropdownInvariantValue(),
            ResultNameType = fields.ResultTypeName.ToDropdownInvariantValue(),
            CreatedFromDate = fields.CreatedDateTime.GetFromDate(),
            CreatedToDate = fields.CreatedDateTime.GetToDate(),
            MaxPageSize = fields.PageSize,
            ContinuationToken = nextContinuationToken
        };
}
