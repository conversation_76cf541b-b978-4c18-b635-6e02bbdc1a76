using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using System.Diagnostics.CodeAnalysis;

namespace Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning;

public partial class InputMultiLineBarcodeModal : InputBase<List<string>>
{
    [Parameter]
    public EventCallback<List<string>> FailedBundlesAction { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    private ModalWindow EditWindow { get; set; }

    private async Task CloseModalAsync() => await EditWindow.CloseAsync();

    public async Task OpenModalAsync()
    {
        await EditWindow.OpenAsync();
    }

    protected override string FormatValueAsString(List<string> value) => string.Join(Environment.NewLine, value);

    protected override bool TryParseValueFromString(string value, [MaybeNullWhen(false)] out List<string> result, [NotNullWhen(false)] out string validationErrorMessage)
    {
        if (string.IsNullOrEmpty(value))
        {
            validationErrorMessage = "Input is leeg.";
            result = new List<string>();
            return true;
        }

        validationErrorMessage = null;
        result = value.Split().Select(x => x.Trim()).Where(c => !string.IsNullOrWhiteSpace(c)).ToList();

        return true;
    }


    private async Task FailedBarcodeListAsync()
    {
        if (CurrentValue!.Exists(c => !Validation.Validation.IsValidBarcode(c)))
        {
            ToastService.ShowError("Ongeldige barcode(s) gevonden");
            return;
        }

        if (CurrentValue.Count == 0)
        {
            ToastService.ShowError("Geen barcodes gevonden");
            return;
        }

        if (FailedBundlesAction.HasDelegate)
        {
            await FailedBundlesAction.InvokeAsync(CurrentValue);
        }

        await CloseModalAsync();
    }
}
