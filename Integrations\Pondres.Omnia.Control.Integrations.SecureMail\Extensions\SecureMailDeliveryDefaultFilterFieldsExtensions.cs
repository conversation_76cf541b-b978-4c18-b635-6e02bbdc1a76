﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Extensions;

public static class SecureMailDeliveryDefaultFilterFieldsExtensions
{
    public static DeliveryListFilter ToApiFilter(this SecureMailDeliveryDefaultFilterFields fields, string continuationToken, string currentCustomer) =>
        new()
        {
            Customer = currentCustomer,
            MaxPageSize = fields.PageSize,
            Flow = fields.Flow.ToDropdownInvariantValue(),
            StateTypeName = fields.StateTypeName.ToDropdownInvariantValue(),
            ResultTypeName = fields.ResultTypeName.ToDropdownInvariantValue(),
            CreatedFromDate = fields.CreatedDateTime.GetFromDate(),
            CreatedToDate = fields.CreatedDateTime.GetToDate(),
            CustomerReference = fields.CustomerReference,
            ContinuationToken = continuationToken,
            OrderId = fields.OrderId.ToNullableGuid(),
            Connector = fields.Connector
        };
}
