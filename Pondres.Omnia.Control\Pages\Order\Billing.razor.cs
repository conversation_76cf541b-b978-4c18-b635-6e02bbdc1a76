using Blazored.Toast.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.Control.Integrations.OrderHub.Services;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Pages.Order;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Billing : CustomerPageBase
{
    [Inject]
    private IBillingService BillingService { get; set; }

    [Inject]
    private IToastService ToastService { get; set; }

    private OrderBillingFilter Filter { get; set; } = new OrderBillingFilter();

    private List<OrderStatisticsResult> Statistics { get; set; } = new List<OrderStatisticsResult>();

    private bool IsLoading = false;

    private EditContext EditContext;

    private async Task OnFilterChangedAsync(OrderBillingFilter filter)
    {
        if (!IsLoading)
        {
            Filter.Merge(filter);


            if (Filter.CreatedOnFilter.HasValues() || Filter.CompletedOnFilter.HasValues())
            {
                filter.Customer = CurrentCustomerId;
                NavigationManager.NavigateTo($"Billing/Orders?{FilterHelper.ConvertToQueryParameter(filter)}");
                await TryGetOrderStatisticsAsync();
            }
            else
            {
                ToastService.ShowError("Please supply for at least one filter From and To");
            }
        }
    }

    private async Task TryGetOrderStatisticsAsync()
    {
        IsLoading = true;

        try
        {
            Statistics = await BillingService.GetOrderStatisticsAsync(Filter);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }
        finally
        {
            IsLoading = false;
        }

        await InvokeAsync(StateHasChanged);
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        var newfilter = FilterHelper.GetFilterFromUri<OrderBillingFilter>(NavigationManager.ToAbsoluteUri(NavigationManager.Uri));
        Filter.Merge(newfilter);

        Filter.Customer = CurrentCustomerId;
    }

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Filter);
    }

    protected override async Task OnCustomerChangedAsync(string customerId)
    {
        if (CurrentCustomerId != customerId)
            CurrentCustomerId = customerId;

        Filter.Customer = customerId;

        await TryGetOrderStatisticsAsync();
    }
}