﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.OpsGenie.Configuration;
using Pondres.Omnia.Control.Integrations.OpsGenie.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.OpsGenie;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddOpsGenieServices(this IServiceCollection services, AppSettings appSettings)
    {
        services.AddHttpClient<IOpsGenieService, OpsGenieService>();

        services.AddSingleton(x => new OpsGenieSettings
        {
            BaseUrl = "https://api.atlassian.com/jsm/ops/integration/",
            OpsGenieAlertsKey = appSettings.OpsGenieAlertsApiKeyReadOnly,
            OpsGenieScheduleKey = appSettings.OpsGenieScheduleApiKeyReadOnly,
            PlatformFilterId = appSettings.OpsGeniePlatformFilterId,
            CustomerFilterId = appSettings.OpsGenieCustomerFilterId,
            ScheduleFilterId = appSettings.OpsGenieScheduleFilterId
        });

        return services;
    }
}
