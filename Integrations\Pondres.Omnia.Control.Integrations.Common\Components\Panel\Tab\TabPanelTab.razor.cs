using Microsoft.AspNetCore.Components;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;

public partial class TabPanelTab<TDataType> : IDisposable
{
    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public bool Selected { get; set; }

    [Parameter]
    public TDataType Data { get; set; }

    [Parameter]
    public RenderFragment Template { get; set; }

    [CascadingParameter]
    private TabPanel<TDataType> Panel { get; set; }

    protected override void OnInitialized()
    {
        Panel.AddColumn(this);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        Panel.RemoveColumn(this);
    }
}
