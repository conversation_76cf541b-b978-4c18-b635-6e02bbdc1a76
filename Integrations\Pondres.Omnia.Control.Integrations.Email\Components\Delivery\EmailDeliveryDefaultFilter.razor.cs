﻿using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Email.Models;

namespace Pondres.Omnia.Control.Integrations.Email.Components.Delivery;

public partial class EmailDeliveryDefaultFilter
{
    [Inject]
    protected IToastService ToastService { get; set; }

    [Parameter]
    public EmailDeliveryDefaultFilterContext Filter { get; set; }

    [Parameter]
    public EventCallback<EmailDeliveryDefaultFilterContext> FilterChanged { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    private async Task OnFilterChangedAsync()
    {
        if (FilterChanged.HasDelegate)
            await FilterChanged.InvokeAsync(Filter);
    }
}