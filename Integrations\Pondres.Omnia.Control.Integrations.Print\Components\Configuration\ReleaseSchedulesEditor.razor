﻿@using Pondres.Common.Extensions
@using BlazorMonaco
@using BlazorMonaco.Editor

@inherits ModalBase

<TelerikDialog Width="800px" @ref="Dialog"
               @bind-Visible="IsVisible">
    <DialogTitle>
        <strong>Release schedule details</strong>
    </DialogTitle>
    <DialogContent>
        @if (!ValidationMessage.IsNullOrWhiteSpace())
        {
            <div class="alert alert-danger">
                @ValidationMessage
            </div>
        }
        <div class="full-width-modal-body pt-4">
            <StandaloneCodeEditor @ref="Editor" Id="monaco-definition-details" OnDidInit="@MonacoEditorInitializedAsync"
                          ConstructionOptions="EditorConstructionOptions" />
        </div>
    </DialogContent>
    <DialogButtons>
        @if (!IsView) 
        {
            <TelerikButton ButtonType="ButtonType.Button" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@SaveDialogAsync">Save</TelerikButton>
            <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
        }
    </DialogButtons>
</TelerikDialog>
