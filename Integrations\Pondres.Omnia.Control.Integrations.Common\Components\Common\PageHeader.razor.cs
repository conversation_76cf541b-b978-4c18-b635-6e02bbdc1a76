﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Pondres.Common.Extensions;
using System.Security.Claims;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Common;

public partial class PageHeader : ComponentBase
{
    [Parameter]
    public string Title { get; set; }

    [Inject]
    private AuthenticationStateProvider authenticationStateProvider { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    public string CurrentUserHeader { get; private set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        var claims = (await authenticationStateProvider.GetAuthenticationStateAsync()).User.Claims;

        var username = claims.SingleOrDefault(claim => claim.Type == "name")?.Value;
        var role = claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;

        if (!username.IsNullOrWhiteSpace())
            CurrentUserHeader = username;

        if (!role.IsNullOrWhiteSpace())
            CurrentUserHeader += $" - {role}";
    }
}