using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace;

public partial class NotificationTraceGridCard
{
    [Inject]
    public NavigationManager Navigation { get; set; }

    [Inject]
    public INotificationTraceService NotificationTraceService { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public Guid OrderId { get; set; }

    private void NavigateToTrace(string traceId) => Navigation.NavigateTo($"Notifications/TraceDetails/{Customer}/{traceId}");

    private NotificationTraceListFilter CreateTraceListFilter(string continuationToken) => new()
    { 
        ContinuationToken = continuationToken,
        Customer = Customer, 
        OrderId = OrderId,
        PageSize = ControlConstants.DefaultPageSize 
    };

    public async Task<PagedResultModel<NotificationTraceListItem>> GetNextItemsAsync(string continuationToken) =>
        await NotificationTraceService.GetAllTracesForFilterAsync(CreateTraceListFilter(continuationToken));
}
