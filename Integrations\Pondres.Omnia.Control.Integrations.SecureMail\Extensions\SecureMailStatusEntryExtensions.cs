﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Extensions;

public static class SecureMailStatusEntryExtensions
{
    public static PagedResultModel<DeliveryMailStatusEntry> ToPagedResultModel(this List<DeliveryMailStatusEntry> triggers) =>
        new() { Items = triggers.OrderBy(x => x.Timestamp).ToList() };
}
