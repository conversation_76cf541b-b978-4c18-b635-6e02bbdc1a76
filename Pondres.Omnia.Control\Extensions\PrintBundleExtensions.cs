﻿using Pondres.Omnia.Control.Integrations.Print;
using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Extensions
{
    public static class PrintBundleExtensions
    {
        public static string ToPrintFolderPath(this PrintBundleListItem bundle, string basePath) =>
            $"{basePath}\\{bundle.Customer}\\{bundle.BatchName}";

        public static string ToPrintFolderPath(this PrintBundleForInstructions bundle, string basePath) =>
            $"{basePath}\\{bundle.Customer}\\{bundle.BatchName}";

        public static string ToPrintFilePath(this PrintBundleListItem bundle, string basePath, string fileName) =>
            $"{basePath}\\{bundle.Customer}\\{bundle.BatchName}\\{bundle.Metadata.SheetArticleCode}\\{fileName}";
    }
}
