﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Services;

namespace Pondres.Omnia.Control.Integrations.Print.Models
{
    public class PrintBatchDefaultFilterContext : QueryStringFilterContext<DGCodeBatch, PrintBundleDefaultFilterFields>, IPrintBatchFilterContext
    {
        private readonly IPrintService printService;

        public override string FilterTypeName => FilterType.Batch.ToString();

        public PrintBatchDefaultFilterContext(NavigationManager navigationManager, IPrintService printService) : base(navigationManager)
        {
            this.printService = printService;
            this.Fields.PageSize = 500;
        }

        protected override async Task<PagedResultModel<DGCodeBatch>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) => await printService.GetBatchesAsync(Fields.ToApiFilter(nextContinuationToken));

        protected override void NavigateToFilter() =>
            navigationManager.NavigateToCurrentPrintBatchWithDefaultFilter(Fields);
    }
}