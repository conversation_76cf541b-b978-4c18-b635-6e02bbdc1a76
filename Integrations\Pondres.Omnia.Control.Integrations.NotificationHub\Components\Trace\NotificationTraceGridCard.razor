﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Extensions
@using Pondres.Omnia.NotificationHub.Contracts.Api.Trace
 
<GridCard Title="Notification traces"
          GetRawColorFunc="(trace) => trace.GetStatusColorStyle()"
          GetItemsAsync="GetNextItemsAsync">
    <TableHeaderContent>
        <th>Id</th>
        <th>Event Type</th>
        <th>Status</th>
        <th>Updated On</th>
    </TableHeaderContent>
    <TableRowContent>
        <td><a class="link" @onclick="() => NavigateToTrace(context.TraceId)" @onclick:stopPropagation="true">@context.TraceId.Shorten()</a></td>
        <td>@context.EventType</td>
        <td>@context.Status</td>
        <td>@context.UpdatedOn.LocalDateTime</td>
    </TableRowContent>
</GridCard>


