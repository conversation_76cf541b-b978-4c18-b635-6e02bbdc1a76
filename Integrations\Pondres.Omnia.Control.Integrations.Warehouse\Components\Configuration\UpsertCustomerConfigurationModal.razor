﻿@using Blazored.Toast.Services

<ModalWindow @ref="ModalWindow">
    <ModalHeader>
        @ModalHeader
    </ModalHeader>
    <ModalBody>
        @if (editContext != null)
        {
            <EditForm EditContext="editContext" OnValidSubmit="AddUpdateCustomerConfigurationAsync" Id="ConfigurationForm">
                <div class="form-group">
                    <label class="pr-4 col-form-label">Customer</label>
                    <input disabled="@true" type="text" class="form-control" name="Customer" @bind-value="@configuration.Customer" />
                </div>

                <div class="form-group">
                    <label class="pr-4 col-form-label">Sender Address Mapping</label>
                    <input type="text" class="form-control" name="SenderAddressMapping" @bind-value="@configuration.SenderAddressMapping" />
                </div>

                <div class="form-group">
                    <label class="pr-4 col-form-label">Address Mappings</label>

                    @foreach (var mapping in configuration.AddressMappings)
                    {
                        <div class="row mb-2">
                            <div class="col-5">
                                <input type="text" class="form-control" @bind-value="@mapping.AgentCode" placeholder="Agent Code" />
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" @bind-value="@mapping.SenderAddress" placeholder="Sender Address" />
                            </div>
                            <div class="col-2">
                                <a class="btn btn-outline-danger" @onclick="() => RemoveAddressMappingLine(mapping)"><span class="oi oi-trash" aria-hidden="true"></span></a>
                            </div>
                        </div>
                    }
                </div>
            </EditForm>

            <div class="container-fluid">
                <div class="form-group">
                    <button class="btn btn-outline-success" @onclick="AddAddressMappingLine"><span class="oi oi-plus" aria-hidden="true"></span> Add Address Mapping</button>
                </div>
            </div>
        }
    </ModalBody>
    <ModalFooter>
        <button class="btn btn-secondary" @onclick="CloseModalAsync">Close</button>
        <button class="btn btn-success text-white" type="submit" form="ConfigurationForm">Save</button>
    </ModalFooter>
</ModalWindow>

