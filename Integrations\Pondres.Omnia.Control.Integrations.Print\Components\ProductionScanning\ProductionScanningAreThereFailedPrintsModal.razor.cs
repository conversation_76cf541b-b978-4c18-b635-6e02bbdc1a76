using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;

namespace Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning;

public partial class ProductionScanningAreThereFailedPrintsModal : ModalBase
{
    [Parameter]
    public EventCallback PrintsFailedAction { get; set; }

    [Parameter]
    public EventCallback NoPrintFailedAction { get; set; }

    public void ShowDialog()
    {
        IsVisible = true;
    }

    private async Task PrintsFailedAsync()
    {
        if (PrintsFailedAction.HasDelegate)
        {
            await PrintsFailedAction.InvokeAsync();
        }

        IsVisible = false;
    }
    private async Task NoPrintsFailedAsync()
    {
        if (NoPrintFailedAction.HasDelegate)
        {
            await NoPrintFailedAction.InvokeAsync();
        }

        IsVisible = false;
    }
}