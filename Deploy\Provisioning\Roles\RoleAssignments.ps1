[CmdletBinding()]
param (
    $SUBSCRIPTION_ID,
    $ENV 
)

$SERVICEBUSNAME = "sb-omnia-$ENV"
$RESOURCE_NAME  = "-control-$ENV"
$RESOURCEGROUP  = "rg-omnia-$ENV"
$STORAGE_ACCOUNT_NAME = "stomniacontrol$ENV"
$MANAGED_ID_NAME = "umi$RESOURCE_NAME"
$KEYVAULT_NAME   = "kv$RESOURCE_NAME"

az account set --subscription $SUBSCRIPTION_ID

$CLIENT_ID = az ad sp list --display-name $MANAGED_ID_NAME --query [].objectId --output tsv
az role assignment create --role "Azure Service Bus Data Owner" --assignee $CLIENT_ID --scope /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCEGROUP/providers/Microsoft.ServiceBus/namespaces/$SERVICEBUSNAME

$AGENTPOOL_CLIENT_ID = az ad sp list --display-name "aks-omnia-$ENV-agentpool" --query [].objectId --output tsv
az role assignment create --assignee $AGENTPOOL_CLIENT_ID --role "Managed Identity Operator" --scope "/subscriptions/$SUBSCRIPTION_ID/resourcegroups/rg-omnia-$ENV-identities/providers/Microsoft.ManagedIdentity/userAssignedIdentities/$MANAGED_ID_NAME"

az keyvault set-policy --name $KEYVAULT_NAME --resource-group $RESOURCEGROUP --object-id $CLIENT_ID --secret-permissions get, list, set
az role assignment create --role "Key Vault Secrets User" --assignee $CLIENT_ID --scope /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCEGROUP/providers/Microsoft.KeyVault/vaults/$KEYVAULT_NAME

az role assignment create --role "Storage Blob Data Reader" --assignee $CLIENT_ID  --scope /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCEGROUP/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME
az role assignment create --role "Storage Blob Data Contributor" --assignee $CLIENT_ID  --scope /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCEGROUP/providers/Microsoft.Storage/storageAccounts/$STORAGE_ACCOUNT_NAME