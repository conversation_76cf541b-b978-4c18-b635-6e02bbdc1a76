﻿@page "/Configuration/OrderFlow"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Constants
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.OrderHub.Contracts.Api.Flow
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Configuration

@inherits SelectableCustomerGridPageBase<OrderFlowDefinitionDetails>

<OrderFlowEditor @ref="DialogEditor" OnSaveAction="@OnSaveActionAsync" />

<PageHeader Title="Order flow configuration">
    <CustomerSelection Customers="@Customers" SelectedCustomerId="@CurrentCustomerId" CustomerChanged="OnCustomerChangedAsync" />
</PageHeader>
<PageBody>
    <AuthorizeView Roles="ControlOwner">
        <TelerikCard>
            <CardHeader>
                <CardTitle>                    
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Order flow configuration</h6>        
                    <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Success)" OnClick="@OnAddCommand">Add</TelerikButton>
                </CardTitle>
            </CardHeader>
            <CardBody>
                <TelerikGrid Data=@OrderFlowDefinitions @ref="DataGrid" Class="grid-no-scroll"                              
                             OnStateInit="@((GridStateEventArgs<OrderFlowDefinitionDetails> args) => OnGridStateInit(args))"
                             SelectionMode="GridSelectionMode.None">
                    <GridColumns>
                        <GridColumn Field="@(nameof(OrderFlowDefinitionDetails.Name))" Title="Name" />
                        <GridColumn Field="@(nameof(OrderFlowDefinitionDetails.CreatedOn))" Title="Created on" />
                        <GridColumn Field="@(nameof(OrderFlowDefinitionDetails.UpdatedOn))" Title="Last updated on" />
                        <GridColumn Field="@(nameof(OrderFlowDefinitionDetails.Disabled))" Title="Disabled" />
                    <GridCommandColumn Context="flow" Width="140px">
                        <GridCommandButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" OnClick="@OnDetailsCommand" Command="Edit">Edit</GridCommandButton>
                    </GridCommandColumn>
                    </GridColumns>
                </TelerikGrid>
            </CardBody>
        </TelerikCard>
    </AuthorizeView>    
</PageBody>