﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Customer.Services;
using Pondres.Omnia.Control.Integrations.NotificationHub.Components.Configuration;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Configuration;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Pages.Notification.Configuration;

public partial class NotificationMappingGrid
{
    [Inject]
    private ICustomerService CustomerService { get; set; }

    [Parameter]
    public string CustomerId { get; set; }

    [Inject]
    private INotificationConfigurationService NotificationConfigurationService { get; set; }

    [CascadingParameter]
    public NotificationMappingGridContext MappingContext { get; set; }

    [Parameter]
    public EventCallback OnConfigurationChanged { get; set; }

    private MappingFormModal MappingFormModal { get; set; }

    private async Task CreateConfigurationAsync()
    {
        try
        {
            var customerDetails = await CustomerService.GetCustomerDetailsAsync(CustomerId);

            await NotificationConfigurationService.CreateConfigurationAsync(new CreateNotificationConfigurationModel
            {
                Customer = customerDetails.Id,
                SecretKeyVaultName = customerDetails.VaultName,
                StorageAccountName = customerDetails.StorageAccountName
            });
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }

        await OnConfigurationChanged.InvokeAsync();
    }

    private async void OnConfigurationModalSubmitted()
    {
        await OnConfigurationChanged.InvokeAsync();
    }

    private async Task OpenNewModalAsync()
    {
        await MappingFormModal.OpenNewModalAsync();
    }

    private async Task OpenModalAsync(NotificationConfigurationMappingModel mapping)
    {
        try
        {
            await MappingFormModal.OpenEditModalAsync(mapping);
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Invalid configuration mapping: {ex.Message}");
        }
    }

    private async Task EditMappingAsync(GridCommandEventArgs args)
    {
        await OpenModalAsync(args.Item as NotificationConfigurationMappingModel);
    }
}