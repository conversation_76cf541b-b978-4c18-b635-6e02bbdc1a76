﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class NotificationTraceDefaultFilterFieldsExtensions
{
    public static NotificationTraceListFilter ToApiFilter(this NotificationTraceDefaultFilterFields fields, string nextContinuationToken, string currentCustomer) =>
        new()
        {
            Customer = currentCustomer,
            ContinuationToken = nextContinuationToken,
            EventType = fields.EventType.ToDropdownInvariantValue(),
            Status = fields.Status.ToDropdownInvariantValue(),
            FromDate = fields.CreatedDateTime.GetFromDate(),
            ToDate = fields.CreatedDateTime.GetToDate(),
            OrderId = fields.OrderId.ToNullableGuid(),
            PageSize = fields.PageSize
        };
}
