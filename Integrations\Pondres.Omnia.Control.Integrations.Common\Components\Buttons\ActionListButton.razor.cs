﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using static Telerik.Blazor.ThemeConstants;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Buttons;

public partial class ActionListButton
{
    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public string ButtonText { get; set; }

    [Parameter]
    public string StyleClass { get; set; }

    [Parameter]
    public string ModalStyleClass { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public string ThemeColor { get; set; } = Button.ThemeColor.Primary;

    [Parameter]
    public Func<IAsyncEnumerable<ActionListButtonResult>> Actions { get; set; }

    [Parameter]
    public EventCallback OnCompletion { get; set; }

    [Parameter]
    public EventCallback OnOpen { get; set; }

    public List<ActionListButtonResult> ActionResults { get; set; } = new List<ActionListButtonResult>();
    public bool Executing { get; set; } = false;
    public bool IsCompleted { get; set; } = false;
    public bool ActionFailed { get; set; } = false;
    public string ActionFailedMessage { get; set; }

    public async Task SubmitModalAsync()
    {
        Executing = true;
        await InvokeAsync(StateHasChanged);

        try
        {
            await foreach (var actionResult in Actions.Invoke())
            {
                ActionResults.Add(actionResult);
            }
        }
        catch (Exception ex)
        {
            ActionFailed = true;
            ActionFailedMessage = ex.Message;
        }

        Executing = false;
        IsCompleted = true;
        await InvokeAsync(StateHasChanged);
    }

    public bool ShowModal { get; set; }

    public async Task OpenModalAsync()
    {
        await OnOpen.InvokeAsync();

        ShowModal = true;
    }

    public async Task CloseModalAsync() => await ResetModalAsync();

    public async Task ResetModalAsync()
    {
        if (IsCompleted && OnCompletion.HasDelegate)
            await OnCompletion.InvokeAsync();

        ShowModal = false;
        ActionFailed = false;
        IsCompleted = false;
        ActionFailedMessage = string.Empty;
        ActionResults = new List<ActionListButtonResult>();
    }

    public static string GetRowColor(bool success) => success ? "table-success-light" : "table-danger";
}