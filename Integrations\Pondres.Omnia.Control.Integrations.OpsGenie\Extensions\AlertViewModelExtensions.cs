﻿using Pondres.Omnia.Control.Integrations.OpsGenie.Models;

namespace Pondres.Omnia.Control.Integrations.OpsGenie.Extensions;

public static class AlertViewModelExtensions
{
    public static string GetAlertBgColor(this AlertViewModel model, bool isHighPriority) =>
        model.HasUnacknowledged ? GetHighAlertBgColor(isHighPriority) : model.GetLowAlertBgColor();

    public static string GetLowAlertBgColor(this AlertViewModel model) =>
        model.Amount > 0 ? "bg-primary text-white" : "bg-success text-white";

    private static string GetHighAlertBgColor(bool isHighPriority) =>
        isHighPriority ? "bg-danger text-white" : "bg-warning text-white";
}
