﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="BlazorMonaco" Version="3.3.0" />
		<PackageReference Include="JsonSchema.Net" Version="7.3.1" />
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
		<PackageReference Include="Pondres.Common" Version="3.20221025.2" />
		<PackageReference Include="Pondres.Omnia.OrderHub.Contracts" Version="3.20250120.2" />
		<PackageReference Include="Solude.Packages.Common" Version="1.20250120.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
	</ItemGroup>
</Project>
