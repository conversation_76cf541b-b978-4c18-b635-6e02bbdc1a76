﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

  <ItemGroup>
	  <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
	  <PackageReference Include="Blazored.Toast" Version="4.2.1" />
	  <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.1" />
	  <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.1" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
	  <PackageReference Include="Pondres.Common" Version="3.20221025.2" />
	  <PackageReference Include="Pondres.Omnia.ESafe.Contracts" Version="1.20240916.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Pondres.Omnia.Control.Integrations.Common\Pondres.Omnia.Control.Integrations.Common.csproj" />
  </ItemGroup>

</Project>
