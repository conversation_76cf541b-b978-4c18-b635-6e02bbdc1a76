﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

<TabPanel TDataType="IWarehouseDocumentFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="IWarehouseDocumentFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="IWarehouseDocumentFilterContext">
                <TabPanelTab TDataType="IWarehouseDocumentFilterContext" Name="Default" Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <DocumentDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
                <TabPanelTab TDataType="IWarehouseDocumentFilterContext" Name="Batch" Data="BatchFilter" Selected="FilterManager.CurrentFilter == BatchFilter">
                    <Template>
                        <DocumentBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>


