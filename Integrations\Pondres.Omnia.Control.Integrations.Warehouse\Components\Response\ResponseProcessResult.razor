﻿@using Pondres.Omnia.Warehouse.Contracts.Response

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Process results
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table mb-0">
            <thead>
                <tr>
                    <th>Customer</th>
                    <th>Order reference</th>
                    <th>Type</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var result in Results)
                {
                    <tr>
                        <td rowspan="@(result.PublishedMessages.Count + 1)">@result.Customer</td>
                        <td rowspan="@(result.PublishedMessages.Count + 1)">@result.OrderReference</td>
                    </tr>
                    @foreach (var message in result.PublishedMessages)
                    {
                        <tr>
                            <td>@message.Type</td>
                            <td>@message.Count</td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </CardBody>
</TelerikCard>

