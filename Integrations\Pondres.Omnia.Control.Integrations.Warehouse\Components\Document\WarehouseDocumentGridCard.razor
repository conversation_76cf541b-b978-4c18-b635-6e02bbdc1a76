﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument

<GridCard Title="Warehouse documents"
          GetRawColorFunc="@((document) => document.LastStatus.GetStatusColorStyle())"
          GetItemsAsync="async(continuationToken) => await WarehouseService.GetDocumentsForOrderIdAsync(OrderId, continuationToken)">
    <TableHeaderContent>
        <th>Id</th>
        <th>Status</th>
        <th>Updated on</th>
    </TableHeaderContent>
    <TableRowContent>
        <td>
            <a class="link" @onclick="() => NavigateToDocument(context.OrderMetadata.Customer, context.DocumentId)" @onclick:stopPropagation="true">@context.DocumentId</a>
        </td>
        <td>
            @($"{context.LastStatus?.StatusCode}-{context.LastStatus?.StatusName}")
        </td>
        <td>@GetLastUpdatedOnTimestamp(context).LocalDateTime</td>
    </TableRowContent>
</GridCard>


