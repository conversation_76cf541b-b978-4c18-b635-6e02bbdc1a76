﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Warehouse.Extensions;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Response;
using System.Collections.ObjectModel;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Response;

public partial class ResponsesGrid : SelectableGridBase<WarehouseResponseListItem>
{
    [Inject]
    IWarehouseResponseService WarehouseService { get; set; }

    [Parameter]
    public ObservableCollection<WarehouseResponseListItem> Responses { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    [Parameter]
    public EventCallback OnRefresh { get; set; }

    private void NavigateToResponseDetails(string filename) =>
        NavigationManager.NavigateTo($"WMS/Responses/{filename}/Details");

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
        NavigateToResponseDetails((e.Item as WarehouseResponseListItem).FileName);

    private async IAsyncEnumerable<ActionListButtonResult> ReprocessFilesAsync()
    {
        foreach (var fileName in DataGrid.SelectedItems.Select(item => item.FileName))
        {
            ActionListButtonResult result;
            try
            {
                await WarehouseService.ReprocessFileAsync(fileName);
                result = new ActionListButtonResult { Key = fileName, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = fileName, FailedResultMessage = ex.Message };
            }
            yield return result;
        }
    }

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as WarehouseResponseListItem).GetStatusColorStyle();
}
