﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.NotificationHub;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddNotificationHubServices(this IServiceCollection services, AppSettings appSettings)
    {
        var httpClientName = "NotificationHub";

        services.AddHttpClient(name: httpClientName)
            .ConfigureBaseAddress(appSettings.NotificationServiceUri)
            .ConfigureAuthenticationToken(appSettings.NotificationServiceAuthToken);

        services.AddHttpClient<INotificationTraceService, NotificationTraceService>(name: httpClientName);
        services.AddHttpClient<INotificationConfigurationService, NotificationConfigurationService>(name: httpClientName);
        services.AddHttpClient<INotificationDefinitionService, NotificationDefinitionService>(name: httpClientName);

        services.AddTransient<NotificationTraceDefaultFilterContext>();

        return services;
    }
}
