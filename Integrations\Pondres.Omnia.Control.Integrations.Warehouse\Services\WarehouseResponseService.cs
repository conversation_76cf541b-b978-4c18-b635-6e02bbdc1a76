﻿using Microsoft.Extensions.Caching.Memory;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Customer.Contracts.Shared;
using Pondres.Omnia.Warehouse.Contracts.Api;
using Pondres.Omnia.Warehouse.Contracts.Api.CustomerConfiguration;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Services;

public class WarehouseResponseService : BaseApiService, IWarehouseResponseService
{
    private readonly IMemoryCache memoryCache;

    public WarehouseResponseService(IMemoryCache memoryCache, HttpClient httpClient)
        : base(httpClient)
    {
        this.memoryCache = memoryCache;
    }

    public async Task<WarehouseResponseDetails> GetResponseDetailsAsync(string fileName) =>
        await GetAsync<WarehouseResponseDetails>($"wms/api/response/details?fileName={fileName}");

    public async Task<FileModel> GetFileContentsAsync(string filename)
    {
        var fileResult = await GetAsync<TemporaryFileData>($"wms/api/response/file?fileName={filename}");

        var contents = await GetRawAsync(fileResult.TemporaryUri);

        return new FileModel
        {
            FileName = fileResult.FileName,
            FilePath = fileResult.FilePath,
            Contents = contents
        };
    }

    public async Task<PagedResultModel<WarehouseResponseListItem>> GetAllResponsesByFilterAsync(WarehouseResponseListFilter filter)
    {
        var responseTraceList = await PostWithResultAsync<PagedList<WarehouseResponseListItem>>(filter, $"wms/api/response/pagedList");

        return new PagedResultModel<WarehouseResponseListItem>
        {
            Items = responseTraceList.Items,
            ContinuationToken = responseTraceList.ContinuationToken
        };
    }

    public async Task ReprocessFileAsync(string fileName) =>
        await PostAsync(fileName, $"wms/api/response/reprocess");

    public async Task<PagedResultModel<WarehouseResponseListItem>> GetDeliveriesForOrderAsync(Guid orderId, string customer)
    {
        var deliveries = await GetAsync<List<WarehouseResponseListItem>>($"wms/api/response/allForOrder?orderId={orderId}&customer={customer}");

        return new PagedResultModel<WarehouseResponseListItem>
        {
            Items = deliveries
        };
    }

    public async Task<List<string>> GetMessageTypesAsync()
    {
        if (!memoryCache.TryGetValue("_ResponseMessageTypes", out List<string> messageTypes))
        {
            messageTypes = await GetAsync<List<string>>("wms/api/response/messageTypes");
            memoryCache.Set("_ResponseMessageTypes", messageTypes, new MemoryCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(60) });
        }

        return messageTypes;
    }

    public async Task<CustomerConfiguration> GetCustomerConfigurationAsync(string customer) =>
        await GetAsync<CustomerConfiguration>($"wms/api/customerConfiguration/details?customer={customer}");

    public async Task CreateCustomerConfigurationAsync(CustomerConfiguration configuration) =>
        await PostAsync(configuration, "wms/api/customerConfiguration/create");

    public async Task UpdateCustomerConfigurationAsync(CustomerConfiguration configuration) =>
        await PostAsync(configuration, "wms/api/customerConfiguration/update");
}
