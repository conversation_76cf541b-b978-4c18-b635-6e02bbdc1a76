﻿using Microsoft.AspNetCore.Components;
using Pondres.Common.Extensions;
using Telerik.Blazor;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Buttons;

public partial class HyperLinkButton
{
    [Parameter]
    public string ThemeColor
    {
        get;
        set;
    } = ThemeConstants.Button.ThemeColor.Primary;

    [Parameter]
    public string Target
    {
        get;
        set;
    } = "_blank";

    [Parameter]
    public string Icon
    {
        get;
        set;
    }

    [Parameter]
    public string RequestUrl { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    public bool HasIcon => !Icon.IsNullOrWhiteSpace();
}