﻿using Pondres.Omnia.Control.Integrations.Common.ViewModels;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Services;

namespace Pondres.Omnia.Control.ViewModels.Schedules
{
    public class SchedulesOverviewViewModel : ViewModelBase, ISchedulesOverviewViewModel
    {
        private IPrintService PrintService { get; set; }

        public SchedulesOverviewViewModel(IPrintService printService)
        {
            this.PrintService = printService;
        }

        public async Task<List<ReleaseSchedules>> LoadAllReleaseSchedulesAsync()
        {
            return await PrintService.GetReleaseSchedulesAsync();
        }

        public async Task<ReleaseScheduleResult> CreateReleaseSchedulesAsync(ReleaseSchedules schedule)
        {
            return await PrintService.CreateReleaseSchedulesAsync(schedule);
        }

        public async Task<ReleaseScheduleResult> UpdateReleaseScheduleAsync(ReleaseSchedules schedule)
        {
            return await PrintService.UpdateReleaseSchedulesAsync(schedule);
        }

        public async Task DeleteReleaseScheduleAsync(string scheduleId)
        {
            await PrintService.DeleteReleaseSchedulesAsync(scheduleId);
        }
    }
}
