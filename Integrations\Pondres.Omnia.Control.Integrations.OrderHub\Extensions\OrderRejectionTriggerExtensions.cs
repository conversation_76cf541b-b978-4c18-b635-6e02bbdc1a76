﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderRejectionTriggerExtensions
{
    public static PagedResultModel<OrderRejectionTrigger> ToPagedResultModel(this List<OrderRejectionTrigger> triggers) =>
        new()
        {
            Items = triggers
                .OrderBy(x => x.ReceivedOn == null)
                .ThenBy(o => o.ReceivedOn)
                .ToList()
        };

    public static string GetStatusColorStyle(this OrderRejectionTrigger trigger) =>
        trigger.ReceivedOn.HasValue ? "table-warning" : "table-primary";
}
