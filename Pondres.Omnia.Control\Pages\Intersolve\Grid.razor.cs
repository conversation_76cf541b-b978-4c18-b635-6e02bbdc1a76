﻿using Microsoft.AspNetCore.Authorization;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;
using Pondres.Omnia.Control.Integrations.Intersolve.Extensions;

namespace Pondres.Omnia.Control.Pages.Intersolve;

[Authorize(Roles = "ControlReader, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Grid : CustomerFilterGridPageBase<IntersolveOrderListItem, IIntersolveOrderFilterContext>
{ 
}
