﻿<EditForm EditContext="@EditContext">
    <div class="form-row">
        <InputMultiLineDialog @bind-Value="Filter.Fields.PrintBundleIds" FieldName="Bundle identifiers" ItemName="Bundle identifier" />
        <InputMultiLineDialog @bind-Value="Filter.Fields.BatchNames" FieldName="Batch names" ItemName="Batch name" />
    </div>

    <div class="pt-2">
        <div class="float-right">
            <label class="col-form-label ml-3">Page size</label>
            <InputSelect class="form-control" style="width: unset; display: unset;" @bind-Value="Filter.Fields.PageSize">
                @foreach (var pageSize in ControlConstants.PageSizes)
                {
                    <option value="@pageSize">@pageSize</option>
                }
            </InputSelect>

            <button class="btn btn-outline-primary ml-1" @onclick="ClearFilterAsync"><i class="oi oi-reload mr-2"></i> Reset</button>
        </div>
    </div>
</EditForm>

