﻿@using Microsoft.AspNetCore.Components.Forms
@using Pondres.Omnia.Control.Integrations.Common.Components.Form
@using Pondres.Omnia.Control.Integrations.Common.Components.Filters
@using Telerik.Blazor.Components
@using Pondres.Omnia.Control.Integrations.Common.Components.Common

<EditForm EditContext="editContext">
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-10 col-sm-12">
                    <div class="row">
                        <div class="col-4 pr-1">
                            <label class="pr-4 col-form-label">Scan</label>
                            <InputText @ref="scanField" class="form-control border-1 small" @bind-Value="Filter.BarCode" />
                        </div>
                        <div class="col-4 pr-1">
                            <label class="pr-4 col-form-label">Batches</label><br />
                            <TelerikDropDownList Data="@Batches" @bind-Value="Filter.BatchName" Width="170px" />
                        </div>

                    </div>
                </div>
            </div>
            <div class="float-right">
                <button class="btn btn-outline-primary mr-3" @onclick="ClearFilterAsync"><i class="oi oi-reload mr-2"></i>Reset</button>
            </div>
            <div class="float-right">
                <button class="btn btn-outline-primary mr-3" @onclick="CompleteScannedBundlesClickAsync"><i class="oi oi-caret-right mr-2"></i>Gescand Afmelden</button>
            </div>
            <ProductionScanningAreThereFailedPrintsModal @ref="ProductionScanningAreThereFailedPrintsDialog" PrintsFailedAction="@PrintFailureAsync" NoPrintFailedAction="@NoPrintFailureAsync" />
            <InputMultiLineBarcodeModal @bind-Value="Filter.BarCodesToBeReprinted" @ref="MultiLineBarcodeModalDialog" FailedBundlesAction="@FailedBundlesAsync" />
        </div>
    </div>
    <LoadingSpinner Show="Loading" />
</EditForm>


