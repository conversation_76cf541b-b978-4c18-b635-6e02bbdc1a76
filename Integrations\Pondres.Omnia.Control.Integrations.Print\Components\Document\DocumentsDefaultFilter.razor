﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters

<ListFilter Filter="Filter" FilterChanged="OnFilterChanged">

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.OrderId" LabelText="Order Id" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.Customer" LabelText = "Customer" />
        <ListFilterInputText @bind-Value="Filter.Fields.DocumentId" LabelText="Document id" />
        <ListFilterInputText @bind-Value="Filter.Fields.CustomerDocumentReference" LabelText="Document Reference" />
        <ListFilterInputText @bind-Value="Filter.Fields.BatchName" LabelText="Batch Name" />
        <ListFilterInputText @bind-Value="Filter.Fields.BundleId" LabelText="Bundle Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.GordNumber" LabelText="Gord Number" />
        <ListFilterInputText @bind-Value="Filter.Fields.TaskNumber" LabelText="Task Number" />
        <ListFilterInputText @bind-Value="Filter.Fields.SequenceId" LabelText="Sequence Id" />
        <ListFilterInputText @bind-Value="Filter.Fields.BarCode" LabelText="Bar Code" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="created on" />
    </ListFilterRow>

    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />

</ListFilter>

