﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Configuration;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Services;

public class NotificationConfigurationService : BaseApiService, INotificationConfigurationService
{
    public NotificationConfigurationService(HttpClient httpClient)
        : base(httpClient)
    {
    }

    public async Task<NotificationConfigurationModel> GetConfigurationAsync(string customer) =>
        await GetAsync<NotificationConfigurationModel>($"notification/configuration/get?customer={customer}");

    public async Task<NotificationConfigurationModel> CreateConfigurationAsync(CreateNotificationConfigurationModel model) =>
        await PostWithResultAsync<NotificationConfigurationModel>(model, "notification/configuration/create");

    public async Task<NotificationConfigurationModel> UpdateConfigurationAsync(UpdateNotificationConfigurationModel model) =>
        await PostWithResultAsync<NotificationConfigurationModel>(model, "notification/configuration/update");
}
