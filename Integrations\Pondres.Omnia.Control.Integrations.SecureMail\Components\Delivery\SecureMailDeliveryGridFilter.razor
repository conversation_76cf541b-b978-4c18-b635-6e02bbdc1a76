﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab
@using Pondres.Omnia.Control.Integrations.Common.Manager

<TabPanel TDataType="ISecureMailDeliveryFilterContext" TabChanged="OnFilterTabChanged">
    <div class="card shadow mb-4">
        <div class="card-header">
            <TabPanelHeader TDataType="ISecureMailDeliveryFilterContext" NavigationType="tabs card-header-tabs" Title="Filter" />
        </div>
        <div class="card-body">
            <TabPanelTabs TDataType="ISecureMailDeliveryFilterContext">
                <TabPanelTab TDataType="ISecureMailDeliveryFilterContext" Name="Default" Data="DefaultFilter" Selected="FilterManager.CurrentFilter == DefaultFilter">
                    <Template>
                        <SecureMailDeliveryDefaultFilter Filter="DefaultFilter" FilterChanged="OnFilterChanged" Flows="@Flows" />
                    </Template>
                </TabPanelTab>
                <TabPanelTab TDataType="ISecureMailDeliveryFilterContext" Name="Batch" Data="BatchFilter" Selected="FilterManager.CurrentFilter == BatchFilter">
                    <Template>
                        <SecureMailDeliveryBatchFilter Filter="BatchFilter" FilterChanged="OnFilterChanged" />
                    </Template>
                </TabPanelTab>
            </TabPanelTabs>
        </div>
    </div>
</TabPanel>

