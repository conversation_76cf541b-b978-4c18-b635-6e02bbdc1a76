﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderDefaultFilterFieldsExtensions
{
    public static OrderListFilter ToApiFilter(this OrderDefaultFilterFields fields, string continuationToken, string currentCustomer) =>
        new()
        {
            Customer = currentCustomer,
            ContinuationToken = continuationToken,
            OrderId = fields.OrderId,
            MaxPageSize = fields.PageSize,
            StateTypeName = fields.StateTypeName.ToDropdownInvariantValue(),
            ResultTypeName = fields.ResultTypeName.ToDropdownInvariantValue(),
            Flow = fields.Flow.ToDropdownInvariantValue(),
            CreatedFromDate = fields.CreatedDateTime.GetFromDate(),
            CreatedToDate = fields.CreatedDateTime.GetToDate(),
            CustomerReference = fields.Reference,
            Category1 = fields.Category1,
            Category2 = fields.Category2,
            Category3 = fields.Category3
        };
}
