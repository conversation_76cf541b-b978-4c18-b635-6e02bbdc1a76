﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Extensions;

public static class WarehouseDocumentResponseStatusExtensions
{
    public static PagedResultModel<WarehouseDocumentResponseStatus> ToPagedResultModel(this List<WarehouseDocumentResponseStatus> responses) =>
        new()
        {
            Items = responses
                .OrderBy(o => o.Timestamp)
                .ToList()
        };

    public static string GetStatusColorStyle(this WarehouseDocumentResponseStatus response) =>
        response?.StatusCode == 42 || response?.StatusCode == 40 ? "table-success-light" : "table-primary";
}
