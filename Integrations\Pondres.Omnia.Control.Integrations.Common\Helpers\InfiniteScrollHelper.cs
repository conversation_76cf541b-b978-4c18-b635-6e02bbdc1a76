﻿using Microsoft.JSInterop;

namespace Pondres.Omnia.Control.Integrations.Common.Helpers;

public class InfiniteScrollHelper
{
    private readonly IJSRuntime jsRuntime;
    private readonly List<Func<Task>> scrollActions = new();

    public InfiniteScrollHelper(IJSRuntime jsRuntime)
    {
        this.jsRuntime = jsRuntime;
    }

    public async Task RegisterInteropAsync() => 
        await jsRuntime.InvokeVoidAsync("infiniteScroll.register", DotNetObjectReference.Create(this));

    public void AddHandler(Func<Task> scrollAction) =>
        scrollActions.Add(scrollAction);

    public void RemoveHandler(Func<Task> scrollAction) => 
        scrollActions.Remove(scrollAction);

    [JSInvokable]
    public async Task ScrollHandlerAsync()
    {
        foreach (var action in scrollActions)
        {
            await action.Invoke();
        }
    }
}
