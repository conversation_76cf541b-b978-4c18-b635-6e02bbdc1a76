﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;
using Telerik.DataSource.Extensions;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions;

public static class PrintBundleBatchFilterFieldsExtensions
{
    public static PrintBundleBatchFilter ToApiFilter(this PrintBundleBatchFilterFields fields, string continuationToken) =>
        new()
        {
            State = fields.StateTypeName.ToDropdownInvariantValue().ToEnum<PrintBundleStateType>(PrintBundleStateType.Active),
            MaxPageSize = fields.PageSize,
            BatchNames = fields.BatchNames,
            PrintBundleIds = fields.PrintBundleIds,
            ContinuationToken = continuationToken
        };
}
