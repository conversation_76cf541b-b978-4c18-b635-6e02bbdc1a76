﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Email.Services;
using Pondres.Omnia.Email.Contracts.Api.Delivery;
using Telerik.Blazor;

namespace Pondres.Omnia.Control.Integrations.Email.Components.Delivery;

public partial class DeliveryCurrentStatus
{
    [Inject]
    public IEmailDeliveryService EmailDeliveryService { get; set; }

    [CascadingParameter]
    public DeliveryDetailedData Delivery { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    public async Task HandleBouncedAsync()
    {
        bool isConfirmed = await Dialogs.ConfirmAsync("Are you sure you want to complete the bounced email?", title: "Complete bounced email");

        if (isConfirmed)
        {
            await EmailDeliveryService.UpdateBouncedResultAsync(Delivery.Customer, Delivery.DeliveryId);
            Delivery = await EmailDeliveryService.GetDetailsAsync(Delivery.Customer, Delivery.DeliveryId);
        }
    }
}