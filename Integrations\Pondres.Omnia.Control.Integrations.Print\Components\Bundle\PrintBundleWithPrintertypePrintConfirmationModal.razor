﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab

@inherits ModalBase
    
<TelerikDialog @ref="Dialog" Visible="@IsVisible" Width="750px" ShowCloseButton="false">
    <DialogTitle>
        <strong>Confirm bundles as printed</strong>
    </DialogTitle>
    <DialogContent>
        @if (!HasResult)
        { 
            <span>You are about to confirm <strong>@Model.PrinterDetails.Count()</strong> bundle(s) as printed. Continue?</span>
        }
        <table class="table table-hover mb-0 mt-2">
            <thead><tr><th colspan="2">Files:</th></tr></thead>
            <tbody class="d-block overflow-auto bundle-print-files-list">
                @foreach (var printerDetails in Model.PrinterDetails)
                {
                    <tr>
                        <td><strong>@printerDetails.PrintFileName</strong></td>
                    </tr>
                }
            </tbody>
        </table>

        @if (HasResult)
        { 
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Id</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Result)
                    {
                        <tr class="@GetRowColor(item.IsSuccessful)">
                            <td>@item.Key</td>
                            <td>@(item.IsSuccessful ? item.ResultMessage : item.FailedResultMessage)</td>
                        </tr>
                    }
                </tbody>
            </table>
        }
    </DialogContent>
    <DialogButtons>
        <TelerikButton ButtonType="ButtonType.Submit" Enabled="!HasResult" OnClick="@OnSubmitModalAsync"
                       ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">Ok</TelerikButton>
        <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Close</TelerikButton>
    </DialogButtons>
</TelerikDialog>
