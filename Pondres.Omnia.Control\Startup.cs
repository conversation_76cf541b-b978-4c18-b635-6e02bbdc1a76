using Blazored.LocalStorage;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Identity.Web;
using Pondres.Omnia.Control.Extensions;
using Pondres.Omnia.Control.Settings;
using Serilog;

namespace Pondres.Omnia.Control;

public class Startup
{
    private readonly IConfiguration configuration;
    private readonly AppSettings appSettings;

    public Startup(IWebHostEnvironment environment, IConfiguration configuration)
    {
        this.configuration = configuration;
        appSettings = configuration.Get<AppSettings>();

        Log.Information("Using environment: {environmentName}", environment.EnvironmentName);
    }

    public void Configure(IApplicationBuilder applicationBuilder, IWebHostEnvironment environment)
    {
        applicationBuilder
            .UsePathBase(appSettings.WebBasePath)
            .UseForwardedHeaders(GetForwardedHeadersConfiguration())
            .UseEnvironmentSpecificRequestHandling(environment)
            .UseStaticFiles(
                new StaticFileOptions
                {
                    OnPrepareResponse = ctx =>
                    {
                        ctx.Context.Response.Headers.Append("Content-Security-Policy", "base-uri 'self';default-src 'self';nobject-src 'none';script-src 'self' 'wasm-unsafe-eval';style-src 'self' https://fonts.googleapis.com;connect-src 'self' http: ws: wss:;upgrade-insecure-requests;");
                        ctx.Context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
                    }
                }
                )
            .UseRouting()
            .UseSession()
            .UseAuthorization()
            .UseAuthentication()
            .UseEndpoints(ConfigureEndpoints());

        applicationBuilder.Use(async (context, next) =>
        {
            context.Response.Headers.Append("Content-Security-Policy", "base-uri 'self';default-src 'self';nobject-src 'none';script-src 'self' 'wasm-unsafe-eval';style-src 'self' https://fonts.googleapis.com;connect-src 'self' http: ws: wss:;upgrade-insecure-requests;");
            context.Response.Headers.Append("X-Content-Type-Options", "nosniff");

            // Bug fix for some external libs:
            if ((context.Request.Path.Value?.EndsWith("css") ?? false) && context.Response.Headers.ContentType.Count == 0)
            {
                context.Response.Headers.Append("Content-Type", "text/css");
            }
            await next();
        });
    }

    public void ConfigureServices(IServiceCollection serviceCollection)
    {
        serviceCollection.AddLogging(loggingBuilder => loggingBuilder.AddSerilog(dispose: true));

        serviceCollection
            .Configure<HealthCheckPublisherOptions>(options =>
            {
                options.Delay = TimeSpan.FromSeconds(2);
                options.Predicate = check => check.Tags.Contains("ready");
            })
            .AddHealthChecks();

        serviceCollection.AddDataProtection(appSettings);

        serviceCollection
            .AddApplicationInsights(appSettings)
            .AddMemoryCache()
            .ConfigureOmniaEnvironmentSettings(appSettings)
            .Configure(GetHealthCheckPublisherOptions())
            .AddSession(GetSessionOptions())
            .AddHttpContextAccessor()
            .AddAzureSignalRService(appSettings)
            .AddBlazoredLocalStorage()
            .AddIntegrations(appSettings)
            .AddCache(appSettings)
            .AddViewModels();

        serviceCollection.AddMicrosoftIdentityWebAppAuthentication(configuration);

        serviceCollection.AddControllersWithViews(CreateMvcOptions());
        serviceCollection.AddRazorPages();
        serviceCollection.AddServerSideBlazor();
    }

    private static Action<IEndpointRouteBuilder> ConfigureEndpoints() => endpoints =>
    {
        endpoints.MapBlazorHub();
        endpoints.MapFallbackToPage("/_Host");

        endpoints.MapHealthChecks("/health/ready", new HealthCheckOptions()
        {
            Predicate = (check) => check.Tags.Contains("ready"),
        });

        endpoints.MapHealthChecks("/health/live", new HealthCheckOptions()
        {
            Predicate = (_) => false
        });
    };

    private static Action<MvcOptions> CreateMvcOptions() => options =>
    {
        var policy = new AuthorizationPolicyBuilder()
            .RequireAuthenticatedUser()
            .RequireRole("ControlOwner", "ControlReader", "ControlContributor", "PrintReader", "PrintContributor", "ControlDataReader")
            .Build();

        options.Filters.Add(new AuthorizeFilter(policy));
    };

    private static ForwardedHeadersOptions GetForwardedHeadersConfiguration()
        => new() { ForwardedHeaders = ForwardedHeaders.All };

    private static Action<HealthCheckPublisherOptions> GetHealthCheckPublisherOptions() => options =>
    {
        options.Delay = TimeSpan.FromSeconds(2);
        options.Predicate = (check) => check.Tags.Contains("ready");
    };

    private static Action<SessionOptions> GetSessionOptions() => options =>
    {
        options.IdleTimeout = TimeSpan.FromMinutes(10);
        options.Cookie.HttpOnly = true;
        options.Cookie.IsEssential = true;
    };
}