﻿using System.Collections.ObjectModel;

namespace Pondres.Omnia.Control.Integrations.Common.Extensions;

public static class ObservableCollectionExtensions
{
    public static void AddRange<T>(this ObservableCollection<T> collection, IEnumerable<T> items)
    {
        foreach (T item in items)
            collection.Add(item);
    }

    public static void ForEach<T>(this ObservableCollection<T> collection, Action<T> action)
    {
        foreach (var item in collection)
            action(item);
    }
}
