﻿@using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;
@using static Telerik.Blazor.ThemeConstants.Button;
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal

<ActionModal @ref="RetryTraceModal"
             T="NotificationTraceDetails"
             Context="trace"
             Title="Retry trace?"
             ButtonText="Retry"
             ButtonType="@ThemeColor.Primary"
             OnSubmit="RetryTraceFailedOutputsAsync"
             OnClose="RefreshPageAsync">
    You are about to retry notification <b>@trace.EventSourceType</b>, Continue?
</ActionModal>

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <h6 class="m-0 font-weight-bold text-primary pt-2">@Trace?.EventType</h6>
            <div class="float-right">
                <AuthorizeView Roles="ControlContributor, ControlOwner">

                    <button type="button" class="btn btn-primary view-file float-right mx-1" @onclick="async () => await OnFileOpened.InvokeAsync(Trace.SourceFilePath)">View</button>
                    <button type="button" class="btn btn-primary text-white float-right mx-1" @onclick="async () => await OnFileDownloaded.InvokeAsync(Trace.SourceFilePath)">Download</button>

                    @if (HasActionOfType(NotificationTraceAction.RetryFailedOutputs))
                    {
                        <button class="btn btn-primary" @onclick="() => RetryTraceModal.OpenModal(Trace)">Retry Failed</button>
                    }
                </AuthorizeView>
            </div>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table p-0 m-0">
            <tbody>
                <tr class="@Trace.GetStatusColorStyle()">
                    <th>@nameof(Trace.StateTypeName)</th>
                    <td>@Trace.StateTypeName</td>
                </tr>
                <tr>
                    <th>@nameof(Trace.EventType)</th>
                    <td>@Trace.EventType</td>
                </tr>
                <tr>
                    <th>@nameof(Trace.EventSourceType)</th>
                    <td>@Trace.EventSourceType</td>
                </tr>
                <tr>
                    <th>@nameof(Trace.SourceReference)</th>
                    <td>@Trace.SourceReference</td>
                </tr>
                <tr>
                    <th>@nameof(Trace.OrderMetadata.CustomerReference)</th>
                    <td>@Trace.OrderMetadata.CustomerReference</td>
                </tr>
                @if (Trace.OrderMetadata.OrderId.HasValue)
                {
                    <tr>
                        <th>@nameof(Trace.OrderMetadata.OrderId)</th>
                        <td>
                            @OrderDetailsLink
                        </td>
                    </tr>
                }
                <tr>
                    <th>@nameof(Trace.OrderMetadata.Categories)</th>
                    <td>@GetConcatinatedCategories()</td>
                </tr>
                <tr>
                    <th>@nameof(Trace.CreatedOn)</th>
                    <td>@Trace.CreatedOn.LocalDateTime</td>
                </tr>
                <tr>
                    <th>@nameof(Trace.UpdatedOn)</th>
                    <td>@Trace.UpdatedOn.LocalDateTime</td>
                </tr>
            </tbody>
        </table>
    </CardBody>
</TelerikCard>