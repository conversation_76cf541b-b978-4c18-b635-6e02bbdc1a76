using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace;

public partial class NotificationTraceSummary
{
    [Inject]
    private INotificationTraceService NotificationTraceService { get; set; }

    [Parameter]
    public string TraceId { get; set; }

    [Parameter]
    public NotificationTraceDetails Trace { get; set; }

    [Parameter]
    public EventCallback OnActionHandled { get; set; }

    [Parameter]
    public EventCallback<string> OnFileOpened { get; set; }

    [Parameter]
    public EventCallback<string> OnFileDownloaded { get; set; }

    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public RenderFragment OrderDetailsLink { get; set; }

    private ActionModal<NotificationTraceDetails> RetryTraceModal { get; set; }

    private async Task RetryTraceFailedOutputsAsync(NotificationTraceDetails trace) => await NotificationTraceService.RetryTraceFailedOutputsForCustomerAsync(TraceId, Customer);
    private async Task RefreshPageAsync() => await OnActionHandled.InvokeAsync();
    private bool HasActionOfType(NotificationTraceAction action) => Trace.Actions.Any(a => a == action.ToString());
    private string GetConcatinatedCategories()
    {
        var categories = new string[] { Trace.OrderMetadata.Categories.One, Trace.OrderMetadata.Categories.Two, Trace.OrderMetadata.Categories.Three };
        var jointCategories = string.Join("-", categories.Where(x => !string.IsNullOrEmpty(x)));
        return !string.IsNullOrEmpty(jointCategories) ? jointCategories : "None";
    }
}
