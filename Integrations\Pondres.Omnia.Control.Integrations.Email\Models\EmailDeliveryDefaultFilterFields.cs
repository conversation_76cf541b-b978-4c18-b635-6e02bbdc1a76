﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Email.Models
{
    public class EmailDeliveryDefaultFilterFields : FilterFieldsBase<EmailDeliveryDefaultFilterFields>
    {
        public string Flow { get; set; } = ControlConstants.DefaultDropdownValue;
        public string CustomerReference { get; set; }
        public string OrderId { get; set; }
        public string DeliveryId { get; set; }
        public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
        public string ResultTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
        public string StateTypeName { get; set; } = ControlConstants.DefaultDropdownValue;
    }
}
