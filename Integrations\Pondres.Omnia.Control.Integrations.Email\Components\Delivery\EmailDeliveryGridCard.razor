﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Extensions

<GridCard Title="E-Mail deliveries"
          GetRawColorFunc="(delivery) => delivery.State.GetStatusColorStyle()"
          GetItemsAsync="GetNextItemsAsync">
    <TableHeaderContent>
        <th>Id</th>
        <th>Status</th>
        <th>Updated On</th>
    </TableHeaderContent>
    <TableRowContent>
        <td><a class="link" @onclick="() => NavigateToDelivery(context.DeliveryId)" @onclick:stopPropagation="true">@context.DeliveryId.Shorten()</a></td>
        <td>@context.State.GetStatusString(context.Result, context.TrackStatus)</td>
        <td>@context.LastUpdatedOn.LocalDateTime</td>
    </TableRowContent>
</GridCard>