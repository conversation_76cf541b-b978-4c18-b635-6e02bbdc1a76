﻿using Pondres.Omnia.Control.Integrations.Web2Omnia.Services;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Web2Omnia
{
    public static class IServiceCollectionExtensions
    {
        public static IServiceCollection AddWeb2OmniaServices(this IServiceCollection services, AppSettings appSettings)
        {
            services.AddHttpClient<IWeb2OmniaService, Web2OmniaService>()
                .ConfigureBaseAddress(appSettings.WebToOmniaServiceUri)
                .ConfigureAuthenticationToken(appSettings.WebToOmniaServiceAuthToken);

            return services;
        }
    }
}
