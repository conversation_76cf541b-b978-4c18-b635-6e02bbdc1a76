﻿using Pondres.Omnia.Control.Integrations.Common.Exceptions;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Customer.Extensions;
using Pondres.Omnia.Customer.Contracts.Shared;
using Serilog;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;

namespace Pondres.Omnia.Control.Integrations.Customer.Services
{
    public class FileService : BaseApiService, IFileService
    {
        private readonly JsonSerializerOptions serializerOptions = new JsonSerializerOptions { WriteIndented = true, Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin, UnicodeRanges.Latin1Supplement) };

        public FileService(HttpClient httpClient) : base(httpClient)
        { 

        }

        public async Task<FileModel> GetFileAsync(string customer, string username, string container, string filePath)
        {
            try
            {
                var fileResult = await GetAsync<TemporaryFileData>($"customer/get?customer={customer}&username={username}&container={container}&filepath={filePath}");

                return await GetOrderFileResultForFileExtensionAsync(fileResult);
            }
            catch (Exception ex)
            {
                throw new ApiException("Could not serialize file content", ex);
            }
        }

        private async Task<FileModel> GetOrderFileResultForFileExtensionAsync(TemporaryFileData fileResult) => Path.GetExtension(fileResult.FileName) switch
        {
            ".tno" => fileResult.ToFileModel(fileResult.TemporaryUri),
            ".pdf" => fileResult.ToFileModel(fileResult.TemporaryUri),
            ".xml" => fileResult.ToFileModel(await GetXmlFileContentsAsync(fileResult)),
            ".json" => fileResult.ToFileModel(await GetJsonFileContentsAsync(fileResult)),
            "" => fileResult.ToFileModel(await GetNoExtensionFileContentsAsync(fileResult)),
            _ => fileResult.ToFileModel(await GetUnknownFileContentsAsync(fileResult)),
        };

        private async Task<string> GetXmlFileContentsAsync(TemporaryFileData fileResult) =>
            await GetRawAsync(fileResult.TemporaryUri);

        private async Task<string> GetJsonFileContentsAsync(TemporaryFileData fileResult)
        { 
            try
            {
                var content =  await GetAsync<object>(fileResult.TemporaryUri);
                return JsonSerializer.Serialize(content, serializerOptions);
            }
            catch (Exception exception)
            {
                Log.Warning(exception, "Failed to serialize file content as json. Filename: {fileName}", fileResult.FileName);
                var rawContent = await GetRawAsync(fileResult.TemporaryUri);
                return rawContent;
            }
        }

        private async Task<string> GetUnknownFileContentsAsync(TemporaryFileData fileResult)
        {
            try
            {
                return await GetRawAsync(fileResult.TemporaryUri);
            }
            catch
            {
                Log.Warning("Failed to download unknown file type as text. Filename: {fileName}", fileResult.FileName);
                return string.Empty;
            }
        }

        private async Task<string> GetNoExtensionFileContentsAsync(TemporaryFileData fileResult)
        {
            try
            {
                return await GetJsonFileContentsAsync(fileResult);
            }
            catch
            {
                Log.Warning("Failed to download empty file type as json, trying to download as XML. Filename: {fileName}", fileResult.FileName);

                try
                {
                    return await GetXmlFileContentsAsync(fileResult);
                }
                catch
                {
                    Log.Warning("Failed to download empty file type as XML, trying to download as for unknown file type. Filename: {fileName}", fileResult.FileName);
                    return await GetUnknownFileContentsAsync(fileResult);
                }
            }
        }
    }
}