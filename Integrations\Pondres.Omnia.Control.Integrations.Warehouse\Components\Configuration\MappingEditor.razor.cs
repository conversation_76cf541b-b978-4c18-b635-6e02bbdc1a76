﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Modal;
using Pondres.Omnia.Warehouse.Contracts.Api.ArticleMapping;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Components.Configuration;

public partial class MappingEditor : ModalBase
{
    public ArticleMapping ArticleMapping { get; private set; }

    public bool IsNew { get; private set; }

    [Parameter]
    public EventCallback OnSaveAction { get; set; }

    [Parameter]
    public EventCallback OnCloseAction { get; set; }

    public void ShowDialog(ArticleMapping articleMapping, bool isNew)
    {
        ArticleMapping = articleMapping;
        IsNew = isNew;

        IsVisible = true;
    }

    public async Task SaveArticleMappingAsync()
    {
        if (OnSaveAction.HasDelegate)
            await OnSaveAction.InvokeAsync();

        await CloseDialogAsync();
    }

    public async Task CloseDialogAsync()
    {
        if (OnCloseAction.HasDelegate)
            await OnCloseAction.InvokeAsync();

        ArticleMapping = null;

        IsVisible = false;
    }
}