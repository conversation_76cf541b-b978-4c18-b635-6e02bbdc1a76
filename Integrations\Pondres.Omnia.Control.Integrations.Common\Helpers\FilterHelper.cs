﻿using Microsoft.AspNetCore.WebUtilities;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Pondres.Omnia.Control.Integrations.Common.Helpers;

public static class FilterHelper
{
    private const string FilterQueryParameterName = "Filter";
    private const string FilterTypeQueryParameterName = "FilterType";
    private const string FilterIdQueryParameterName = "FilterId";

    public static TFilterType GetFilterFromUri<TFilterType>(Uri uri)
    {
        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue(FilterQueryParameterName, out var filterString))
        {
            var jsonFilter = Convert.FromBase64String(filterString);
            return JsonSerializer.Deserialize<TFilterType>(Encoding.UTF8.GetString(jsonFilter));
        }

        return default;
    }

    public static string GetFilterIdFromUri(Uri uri)
    {
        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue(FilterIdQueryParameterName, out var filterIdString))
        {
            return filterIdString;
        }

        return null;
    }

    public static string GetFilterTypeFromUri(Uri uri)
    {
        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue(FilterTypeQueryParameterName, out var filterTypeString))
            return filterTypeString;

        return string.Empty;
    }

    private static readonly JsonSerializerOptions serializerOptions = new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull };

    public static string ConvertToQueryParameter<TFilterType>(TFilterType filter)
    {
        var jsonObject = JsonSerializer.Serialize(filter, serializerOptions);
        return $"{FilterQueryParameterName}={Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonObject))}";
    }

    public static string GetFilterIdQueryParameter(string filterId) => $"{FilterIdQueryParameterName}={filterId}";
}
