﻿using Microsoft.AspNetCore.Authorization;
using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;

namespace Pondres.Omnia.Control.Pages.Notification.Trace;

[Authorize(Roles = "ControlR<PERSON>er, ControlContributor, ControlOwner, ControlDataReader")]
public partial class Grid : CustomerFilterGridPageBase<NotificationTraceListItem, INotificationTraceFilterContext>
{ }