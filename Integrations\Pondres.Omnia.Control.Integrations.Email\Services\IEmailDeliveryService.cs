﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Email.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.Email.Services
{
    public interface IEmailDeliveryService
    {
        Task<DeliveryDetailedData> GetDetailsAsync(string customer, Guid deliveryId);
        Task<PagedResultModel<DeliveryData>> GetDeliveriesAsync(DeliveryBatchFilter filter);
        Task<PagedResultModel<DeliveryData>> GetDeliveriesAsync(DeliveryDefaultFilter filter);
        Task UpdateBouncedResultAsync(string customer, Guid deliveryId);
    }
}