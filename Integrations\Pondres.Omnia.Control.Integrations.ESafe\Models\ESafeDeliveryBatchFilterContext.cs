﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.ESafe.Extensions;
using Pondres.Omnia.Control.Integrations.ESafe.Services;
using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.ESafe.Models;

public class ESafeDeliveryBatchFilterContext : CachedFilterContext<DeliveryListItem, ESafeDeliveryBatchFilterFields>, IESafeDeliveryFilterContext
{
    private readonly IESafeDeliveryService esafelDeliveryService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public ESafeDeliveryBatchFilterContext(NavigationManager navigationManager, IFilterCacheService filterCacheService, IESafeDeliveryService secureMailDeliveryService)
        : base(navigationManager, filterCacheService)
    {
        esafelDeliveryService = secureMailDeliveryService;
    }

    protected override async Task<PagedResultModel<DeliveryListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await esafelDeliveryService.GetPagedListAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter(string filterId) =>
        navigationManager.NavigateToESafeDeliveryListWithBatchFilter(filterId);
}
