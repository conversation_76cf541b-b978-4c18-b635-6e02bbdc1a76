﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Grid

<GridCard Title="Status history"
          GetRawColorFunc="(_) => Delivery.Status.StateType.GetStatusColorStyle()"
          GetItemsAsync="(_) => Task.FromResult(Delivery.StatusHistory.ToPagedResultModel())">
    <TableHeaderContent>
        <th>Type</th>
        <th>Timestamp</th>
    </TableHeaderContent>
    <TableRowContent Context="statusEntry">
        <td>@statusEntry.StateType.GetStatusString(statusEntry.ResultType)</td>
        <td>@statusEntry.Timestamp.LocalDateTime</td>
    </TableRowContent>
</GridCard>