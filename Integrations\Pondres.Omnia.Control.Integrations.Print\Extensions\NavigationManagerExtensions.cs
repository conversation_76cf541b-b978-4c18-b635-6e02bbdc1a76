﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Extensions;

public static class NavigationManagerExtensions
{
    public static void NavigateToCurrentPrintBundlesWithDefaultFilter(this NavigationManager navigationManager, PrintBundleDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"{navigationManager.Uri.Split("?")[0]}?FilterType={FilterType.Default}&{FilterHelper.ConvertToQueryParameter(filter)}");

    public static void NavigateToCurrentPrintBatchWithDefaultFilter(this NavigationManager navigationManager, PrintBundleDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"{navigationManager.Uri.Split("?")[0]}?FilterType={FilterType.Batch}&{FilterHelper.ConvertToQueryParameter(filter)}");

    public static void NavigateToCurrentPrintBundlesWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"{navigationManager.Uri.Split("?")[0]}?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");

    public static void NavigateToAllPrintBundlesWithDefaultFilter(this NavigationManager navigationManager, PrintBundleDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"Print/Bundles?FilterType={FilterType.Default}&{FilterHelper.ConvertToQueryParameter(filter)}");

    public static void NavigateToAllPrintBundlesWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"Print/Bundles?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");

    public static void NavigateToPrintDocumentListWithBatchFilter(this NavigationManager navigationManager, string filterId) =>
        navigationManager.NavigateTo($"Print/Documents?FilterType={FilterType.Batch}&{FilterHelper.GetFilterIdQueryParameter(filterId)}");

    public static void NavigateToPrintDocumentListWithDefaultFilter(this NavigationManager navigationManager, PrintDocumentDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"Print/Documents?FilterType={FilterType.Default}&{FilterHelper.ConvertToQueryParameter(filter)}");
}