using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Components.Link;

public partial class SinglePrintBundleListLink
{
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public string BundleId { get; set; }

    private void Navigate()
    {
        var filter = new PrintBundleDefaultFilterFields
        {
            BundleId = BundleId,
            ShowEmptyBundles = true,
            ShowTestCustomers = true
        };

        NavigationManager.NavigateToAllPrintBundlesWithDefaultFilter(filter);
    }
}
