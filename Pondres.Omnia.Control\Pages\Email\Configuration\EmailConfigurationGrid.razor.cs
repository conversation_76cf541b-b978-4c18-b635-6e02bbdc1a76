﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Email.Components.Configuration;
using Telerik.Blazor;
using Telerik.Blazor.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using System.Text.Json;
using Pondres.Omnia.Control.Integrations.Email.Client;
using Pondres.Omnia.Control.Integrations.Email.Extensions;
using Serilog;

namespace Pondres.Omnia.Control.Pages.Email.Configuration;

public partial class EmailConfigurationGrid : SelectableGridBase<CustomerConfigurationModel>
{
    public EmailConfigurationEditor DialogEditor { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    [Inject]
    protected EmailServiceClient EmailClient { get; set; }

    public List<CustomerConfigurationModel> Configurations { get; private set; }

    private readonly JsonSerializerOptions jsonOptions = new() { WriteIndented = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadConfigurationsAsync();

        await base.OnInitializedAsync();
    }

    private async Task LoadConfigurationsAsync()
    {
        await InvokeAsync(StateHasChanged);

        try
        {
            Configurations = (await EmailClient.ConfigurationsGetAsync()).ToList();
        }
        catch (Exception exception)
        {
            Log.Error(exception, "Exception during load of email configurations");
            ToastService.ShowError(exception.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    public async Task OnSaveActionAsync()
    {
        try
        {
            if (DialogEditor.IsNew)
                await EmailClient.ConfigurationsPostAsync(DialogEditor.Configuration.ToCreateDto());
            else
                await EmailClient.ConfigurationsPutAsync(DialogEditor.CurrentConfigCustomer, DialogEditor.Configuration.ToUpdateDto());

            await LoadConfigurationsAsync();

            await DialogEditor.CloseDialogAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private async Task OnViewCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var config = await EmailClient.ConfigurationsGetAsync((e.Item as CustomerConfigurationModel).Customer);

            DialogEditor.ShowDialog(config.ToJson(jsonOptions), true, config.Customer);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    public void OnAddCommand()
    {
        DialogEditor.ShowDialog(new CreateCustomerConfigurationModel().ToJson(jsonOptions), false, string.Empty,true);
    }

    private async Task EditCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var config = await EmailClient.ConfigurationsGetAsync((e.Item as CustomerConfigurationModel).Customer);

            var updateConfig = config.ToJson(jsonOptions).ToUpdateDto();

            DialogEditor.ShowDialog(updateConfig.ToJson(jsonOptions), false, config.Customer);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }
}