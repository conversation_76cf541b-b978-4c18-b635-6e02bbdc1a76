﻿using Pondres.Omnia.Control.Integrations.Print.Client;

namespace Pondres.Omnia.Control.Integrations.Print
{
    public class PrintBundleForInstructions
    {
        public string PrintBundleId { get; set; }
        public string Customer { get; set; }
        public string BatchName { get; set; }
        public string GordNumber { get; set; }
        public int TaskNumber { get; set; }
        public PrintDocumentCreationMetadata Metadata { get; set; }
        public List<string> PrintFiles { get; set; } = new List<string>();
        public string PrintFileLocation { get; set; }

        public int Sheets { get; set; }
        public int Documents { get; set; }
        public int TotalPageCount { get; set; }
    }
}
