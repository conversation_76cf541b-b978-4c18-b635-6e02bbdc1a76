﻿@page "/ESafe/{Customer}/{DeliveryId}"

@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.ESafe.Components.Delivery
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.ESafe.Contracts.Api.Delivery;

<PageHeader Title="@($"E-Safe Delivery {DeliveryId}")" />
<PageBody>
    <div class="container-fluid row">
        @if (PageLoading)
        {
            <LoadingSpinner />
        }
        else
        {
            <CascadingValue Value="Delivery">
                <div class="col-xl-6 col-12">
                    <DeliveryInformation>
                        <OrderDetailsLink>
                            <OrderDetailsLink OrderId="@Delivery.OrderMetadata.OrderId.ToString()" Customer="@Delivery.OrderMetadata.Customer">
                                @Delivery.OrderMetadata.CustomerReference
                            </OrderDetailsLink>
                        </OrderDetailsLink>
                    </DeliveryInformation>

                    <DeliveryVault />
                </div>
                <div class="col-xl-6 col-12">
                    <DeliveryCurrentStatus />

                    <DeliveryStatusHistory />

                    <DeliveryTimeouts ExpectedTimeouts="CreateDeliveryTimeoutModels()"/>
                </div>
            </CascadingValue>
        }
    </div>
</PageBody>