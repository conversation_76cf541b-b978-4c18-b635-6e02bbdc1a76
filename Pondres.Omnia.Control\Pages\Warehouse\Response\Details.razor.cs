using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Response;

namespace Pondres.Omnia.Control.Pages.Warehouse.Response;

public partial class Details
{
    [Inject]
    private IWarehouseResponseService warehouseService { get; set; }

    [Inject]
    private IToastService toastService { get; set; }

    [Parameter]
    public string Filename { get; set; }

    private WarehouseResponseDetails Response { get; set; }

    private bool InvalidResponse { get; set; } = false;
    private bool LoadingResponses { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await LoadResponseAsync();
    }

    private async Task LoadResponseAsync()
    {
        try
        {
            await StartLoadingAsync();
            Response = await warehouseService.GetResponseDetailsAsync(Filename);
        }
        catch (Exception exception)
        {
            InvalidResponse = true;
            toastService.ShowError(exception.Message);
        }

        await StopLoadingAsync();
    }

    private async Task StartLoadingAsync()
    {
        LoadingResponses = true;
        await InvokeAsync(StateHasChanged);
    }

    private async Task StopLoadingAsync()
    {
        LoadingResponses = false;
        await InvokeAsync(StateHasChanged);
    }
}