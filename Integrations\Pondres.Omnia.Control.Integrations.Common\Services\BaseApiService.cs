﻿using Microsoft.AspNetCore.Http.Extensions;
using Pondres.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Common.Exceptions;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Pondres.Omnia.Control.Integrations.Common.Services;

public class BaseApiService
{
    private readonly HttpClient httpClient;
    private readonly JsonSerializerOptions jsonSerializerSettings;

    public BaseApiService(HttpClient httpClient)
    {
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        var enumConverter = new JsonStringEnumConverter();
        options.Converters.Add(enumConverter);

        jsonSerializerSettings = options;
        this.httpClient = httpClient;
    }

    protected async Task<T> GetAsync<T>(string route) =>
        await ExecuteRequestAsync(async () =>
        {
            var response = await httpClient.GetAsync(route);

            await ValidateResponseAsync(response);

            return await GetJsonResponseAsync<T>(response);
        });

    protected async Task<string> PostWithStringResultAsync(object requestBody, string route, string authToken, JsonSerializerOptions? serializerOptions = null) =>
        await ExecuteRequestAsync(async () =>
        {
            var stringContent = JsonSerializer.Serialize(requestBody, serializerOptions ?? jsonSerializerSettings);
            var content = new StringContent(stringContent, Encoding.UTF8, "application/json");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);

            var response = await httpClient.PostAsync(route, content);
            await ValidateResponseAsync(response);

            var result = await response.Content.ReadAsStringAsync();
            return result;
        });

    protected async Task<T> PostWithResultAsync<T>(object requestBody, string route) =>
        await ExecuteRequestAsync(async () =>
        {
            var stringContent = JsonSerializer.Serialize(requestBody, jsonSerializerSettings);
            var content = new StringContent(stringContent, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(route, content);

            await ValidateResponseAsync(response);

            return await GetJsonResponseAsync<T>(response);
        });

    protected async Task PostAsync(object requestBody, string route) =>
        await ExecuteRequestAsync<object>(async () =>
        {
            var stringContent = JsonSerializer.Serialize(requestBody, jsonSerializerSettings);
            var content = new StringContent(stringContent, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(route, content);

            await ValidateResponseAsync(response);

            return null;
        });

    protected async Task PatchAsync(object requestBody, string route) =>
        await ExecuteRequestAsync<object>(async () =>
        {
            var stringContent = JsonSerializer.Serialize(requestBody, jsonSerializerSettings);
            var content = new StringContent(stringContent, Encoding.UTF8, "application/json");

            var response = await httpClient.PatchAsync(route, content);

            await ValidateResponseAsync(response);

            return null;
        });


    protected async Task PostWithParametersAsync(string route, Dictionary<string, string> queryParameters) =>
        await ExecuteRequestAsync<object>(async () =>
        {
            var query = new QueryBuilder(queryParameters).ToQueryString();

            var response = await httpClient.PostAsync($"{route}{query}", null);

            await ValidateResponseAsync(response);

            return null;
        });

    protected async Task<string> GetRawAsync(string route) =>
        await ExecuteRequestAsync(async () =>
        {
            var response = await httpClient.GetAsync(route);

            await ValidateResponseAsync(response);

            return await response.Content.ReadAsStringAsync();
        });

    private static async Task<T> ExecuteRequestAsync<T>(Func<Task<T>> requestFunc)
    {
        try
        {
            return await requestFunc();
        }
        catch (TaskCanceledException ex)
        {
            throw new ApiException("Timout during GET Request", ex);
        }
        catch (Exception ex) when (ex is not ApiException)
        {
            throw new ApiException("Unexpected Error in GET Request", ex);
        }
    }

    private async Task<T> GetJsonResponseAsync<T>(HttpResponseMessage response)
    {
        try
        {
            var stringContent = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<T>(stringContent, jsonSerializerSettings);
        }
        catch (Exception ex)
        {
            throw new ApiException("Error while deserialize object", ex);
        }
    }

    

    private static async Task ValidateResponseAsync(HttpResponseMessage response)
    {
        if (!response.IsSuccessStatusCode)
        {
            try
            {
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.StatusCode == HttpStatusCode.NotFound)
                    throw new ApiException(response.StatusCode, $"Not Found. {responseContent}");
                else if (responseContent.IsNullOrWhiteSpace())
                    throw new ApiException(response.StatusCode, $"No response content, status code {response.StatusCode} was returned");
                else
                    throw new ApiException(response.StatusCode, responseContent);
            }
            catch (Exception exception)
            {
                throw new ApiException(response.StatusCode, $"Api returned status code {response.StatusCode} with no content", exception);
            }
        }
    }
}