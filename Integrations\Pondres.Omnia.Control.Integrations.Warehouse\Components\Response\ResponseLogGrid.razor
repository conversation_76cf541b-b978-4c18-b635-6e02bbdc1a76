﻿@using Pondres.Omnia.Warehouse.Contracts.Response

<TelerikCard>
    <CardHeader>
        <CardTitle>
            Logs
        </CardTitle>
    </CardHeader>
    <CardBody>
        <table class="table mb-0">
            <thead>
                <tr>
                    <th>Timestamp</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var log in Logs)
                {
                    <ResponseLogGridItem Log="@log" />
                }
            </tbody>
        </table>
    </CardBody>
</TelerikCard>



