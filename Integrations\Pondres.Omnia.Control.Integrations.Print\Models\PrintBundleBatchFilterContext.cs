﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Extensions;
using Pondres.Omnia.Control.Integrations.Print.Services;

namespace Pondres.Omnia.Control.Integrations.Print.Models;

public class PrintBundleBatchFilterContext : CachedFilterContext<PrintBundleListItem, PrintBundleBatchFilterFields>, IPrintBundleFilterContext
{
    private readonly IPrintService printService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public PrintBundleBatchFilterContext(NavigationManager navigationManager, IPrintService printService,
        IFilterCacheService filterCacheService)
        : base(navigationManager, filterCacheService)
    {
        this.printService = printService;
    }

    protected override void NavigateToFilter(string filterId) =>
        navigationManager.NavigateToCurrentPrintBundlesWithBatchFilter(filterId);

    protected override async Task<PagedResultModel<PrintBundleListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await printService.GetAllBundlesByBatchFilterAsync(Fields.ToApiFilter(nextContinuationToken));
}