﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;

namespace Pondres.Omnia.Control.Integrations.Common.Models.Filter;

public abstract class FilterContextBase<TFilterResultType, TFilterFields> :
    IFilterContext<PagedResultModel<TFilterResultType>>
    where TFilterFields : FilterFieldsBase<TFilterFields>, new()
{
    protected string nextContinuationToken;
    protected readonly NavigationManager navigationManager;

    protected abstract Task<PagedResultModel<TFilterResultType>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer);

    public abstract Task NavigateToFilterAsync();

    public abstract string FilterTypeName { get; }

    public abstract Task MergeFromCurrentUriAsync();

    public TFilterFields Fields { get; set; } = new TFilterFields();

    protected FilterContextBase(NavigationManager navigationManager)
    {
        this.navigationManager = navigationManager;
    }

    public async Task<PagedResultModel<TFilterResultType>> GetNextResultsAsync(string currentCustomer, bool refresh)
    {
        if (refresh)
            nextContinuationToken = null;

        var apiResult = await GetNextResultSetAsync(nextContinuationToken, currentCustomer);

        nextContinuationToken = apiResult.ContinuationToken;

        return apiResult;
    }

    public async Task ResetAsync()
    {
        nextContinuationToken = null;

        Fields = new TFilterFields();

        await NavigateToFilterAsync();
    }
}
