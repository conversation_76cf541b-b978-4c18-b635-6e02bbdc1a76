﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab
@using Pondres.Omnia.Control.Integrations.Print.Client;

@inherits ModalBase

<TelerikDialog Visible="@IsVisible" Width="400px" Height="600px">
    <DialogTitle>
        <strong>Reprint bundles</strong>
    </DialogTitle>
    <DialogContent>
        <span><strong>WARNING:</strong> You are about to reprint these documents. Are you sure?</span>
        <TelerikGrid Data="@Models"
                     Sortable="true"
                     FilterMode="@GridFilterMode.None">
             <GridColumns>
                <GridColumn Field="Id">{Models.Id}</GridColumn>
             </GridColumns>
        </TelerikGrid>
    </DialogContent>
    <DialogButtons>
        <TelerikButton ButtonType="ButtonType.Submit" ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)" OnClick="@OnSubmitModelAsync">Ok</TelerikButton>
        <TelerikButton ButtonType="ButtonType.Button" OnClick="@CloseDialogAsync">Cancel</TelerikButton>
    </DialogButtons>
</TelerikDialog>

@code {
    public List<PrintDocumentListItem> Models { get; set; }

    [Parameter]
    public EventCallback OnSaveAction { get; set; }

    [Parameter]
    public EventCallback OnCloseAction { get; set; }

    public void ShowDialog(List<PrintDocumentListItem> models)
    {
        Models = models;
        IsVisible = true;
    }

    public async Task OnSubmitModelAsync()
    {
        if (OnSaveAction.HasDelegate)
            await OnSaveAction.InvokeAsync();

        await CloseDialogAsync();
    }

    public async Task CloseDialogAsync()
    {
        if (OnCloseAction.HasDelegate)
            await OnCloseAction.InvokeAsync();

        Models = null;
        IsVisible = false;
    }
}