﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;
using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;
using System.Collections.ObjectModel;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Components.Order;

public partial class IntersolveOrderGrid : SelectableGridBase<IntersolveOrderListItem>
{
    [Parameter]
    public ObservableCollection<IntersolveOrderListItem> Orders { get; set; }

    [Parameter]
    public string CustomerId { get; set; }

    [Parameter]
    public EventCallback OnRefresh { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    private void NavigateToOrderDetails(IntersolveOrderListItem order) =>
        NavigationManager.NavigateTo($"Intersolve/OrderDetails/{CustomerId}/{order.ProductOwnerNr}/{order.IntersolveOrderId}");

    private void OnDetailsCommand(GridCommandEventArgs e) =>
         NavigateToOrderDetails((e.Item as IntersolveOrderListItem));

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
         NavigateToOrderDetails((e.Item as IntersolveOrderListItem));

    private static string GetFlowOrderTypesString(IntersolveOrderListItem order) =>
        order.OrderTypes.Count == 0 ?
            order.FlowName :
            $"({string.Join(" + ", order.OrderTypes)}) {order.FlowName}";

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as IntersolveOrderListItem).GetStatusColorStyle();
}
