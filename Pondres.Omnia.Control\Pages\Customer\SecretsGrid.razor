﻿@page "/Secrets"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Common.Models.Common
@using Pondres.Omnia.Control.Integrations.Common.Models.Filter
@using Pondres.Omnia.Control.Integrations.Customer.Components;
@using Pondres.Omnia.Customer.Contracts.Api

@inherits SelectableCustomerGridPageBase<VaultSecretListItem>

<CustomerSecretEditor @ref="DialogEditor" OnSaveAction="OnSaveAction" />

<PageHeader Title="Secrets Configuration">
    <CustomerSelection CustomerChanged="@OnCustomerChangedAsync" SelectedCustomerId="@CurrentCustomerId" Customers="Customers" />
</PageHeader>
<PageBody>
    <div class="card shadow mb-4">
        <AuthorizeView Roles="ControlOwner">
            <div class="card-header py-3">
                <div class="d-flex justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary pt-2">Vault Secrets</h6>
                    <div class="float-right">
                        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Success)" OnClick="OnAddCommand">
                            Add Secret
                        </TelerikButton>
                    </div>
                </div>
            </div>
            <TelerikGrid Data="@VaultSecrets" @ref="DataGrid" Class="grid-no-scroll" SelectionMode="GridSelectionMode.None">
                <GridColumns>
                    <GridColumn Field="@(nameof(VaultSecretListItem.Name))" Title="Secret Name" />
                    <GridColumn Field="@(nameof(VaultSecretListItem.CreatedOn))" Title="Created On" DisplayFormat="{0:dd-MM-yyyy HH:mm:ss}" />
                    <GridCommandColumn Width="90px" Context="secret">
                        <GridCommandButton Class="btn btn-primary mx-1 float-right" OnClick="@OnEditCommand">Edit</GridCommandButton>
                    </GridCommandColumn>
                </GridColumns>
            </TelerikGrid>
        </AuthorizeView>
    </div>
</PageBody>