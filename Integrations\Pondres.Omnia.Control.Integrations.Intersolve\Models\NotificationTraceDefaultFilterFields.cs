﻿using Pondres.Omnia.Control.Integrations.Common.Constants;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;

namespace Pondres.Omnia.Control.Integrations.Intersolve.Models;

public class IntersolveOrderDefaultFilterFields : FilterFieldsBase<IntersolveOrderDefaultFilterFields>
{
    public string CustomerReference { get; set; }
    public string IntersolveOrderId { get; set; }
    public string ProductOwnerNr { get; set; }
    public string OrderId { get; set; }
    public string Status { get; set; } = ControlConstants.DefaultDropdownValue;
    public DateTimePickerFilterFields CreatedDateTime { get; set; } = new DateTimePickerFilterFields();
}
