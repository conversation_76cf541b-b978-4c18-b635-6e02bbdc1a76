using Microsoft.AspNetCore.Components;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery;

public partial class DeliveryInformation
{
    [CascadingParameter]
    public DeliveryDetails Delivery { get; set; }

    [Parameter]
    public RenderFragment OrderDetailsLink { get; set; }

    private string GetConcatinatedCategories()
    {
        var categories = new string[] { Delivery.OrderMetadata.Categories.One, Delivery.OrderMetadata.Categories.Two, Delivery.OrderMetadata.Categories.Three };

        var jointCategories = string.Join("-", categories.Where(x => !string.IsNullOrEmpty(x)));

        return !string.IsNullOrEmpty(jointCategories) ? jointCategories : "None";
    }
}
