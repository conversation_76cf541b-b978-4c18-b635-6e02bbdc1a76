﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.OrderHub.Contracts.Api.OrderTask;
using Pondres.Omnia.OrderHub.Contracts.OrderTask;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderTaskInformationExtensions
{
    public static string GetStatusColorStyle(this OrderTaskInformation task)
    {
        if (task.Status.StateType == OrderTaskStateType.Completed &&
            task.Status.ResultType == OrderTaskResultType.Finished)
            return "table-success-light";
        else if (task.Status.StateType == OrderTaskStateType.Completed &&
            task.Status.ResultType != OrderTaskResultType.Finished)
            return "table-danger";
        else if (task.Status.StateType == OrderTaskStateType.Active)
            return "table-primary";
        else if (task.Status.StateType == OrderTaskStateType.Skipped)
            return "table-warning";
        else
            return "";
    }
    public static bool HasAction(this OrderTaskInformation task, string action) =>
        task.Actions.Any(x => x.Equals(action, StringComparison.InvariantCultureIgnoreCase));

    public static PagedResultModel<OrderTaskInformation> ToPagedResultModel(this List<OrderTaskInformation> tasks) =>
        new()
        {
            Items = tasks
                .OrderBy(x => x.Status.StartedOn == null)
                .ThenBy(o => o.Status.StartedOn)
                .ToList()
        };

}
