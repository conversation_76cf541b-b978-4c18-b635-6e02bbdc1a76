﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Services;

public class BillingService : BaseApiService, IBillingService
{
    public BillingService(HttpClient httpClient)
        : base(httpClient)
    { }

    public async Task<List<OrderStatisticsResult>> GetOrderStatisticsAsync(OrderBillingFilter filter)
    {
        if (string.IsNullOrEmpty(filter.Customer))
        {
            return [];
        }

        var apiFilter = filter.ToApiFilter();

        var statisticsResult = await PostWithResultAsync<List<OrderStatisticsResult>>(apiFilter, "order/statistics");

        return statisticsResult;
    }
}