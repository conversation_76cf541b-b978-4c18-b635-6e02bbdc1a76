﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Common.Models.Filter;
using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.SecureMail.Extensions;
using Pondres.Omnia.Control.Integrations.SecureMail.Services;
using Pondres.Omnia.SecureMail.Contracts.Api.Delivery;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Models;

public class SecureMailDeliveryBatchFilterContext : CachedFilterContext<DeliveryListItem, SecureMailDeliveryBatchFilterFields>, ISecureMailDeliveryFilterContext
{
    private readonly ISecureMailDeliveryService secureMailDeliveryService;

    public override string FilterTypeName => FilterType.Batch.ToString();

    public SecureMailDeliveryBatchFilterContext(
        NavigationManager navigationManager,
        IFilterCacheService filterCacheService,
        ISecureMailDeliveryService secureMailDeliveryService)
        : base(navigationManager, filterCacheService)
    {
        this.secureMailDeliveryService = secureMailDeliveryService;
    }

    protected override async Task<PagedResultModel<DeliveryListItem>> GetNextResultSetAsync(string nextContinuationToken, string currentCustomer) =>
        await secureMailDeliveryService.GetAllDeliveriesByFilterAsync(Fields.ToApiFilter(nextContinuationToken, currentCustomer));

    protected override void NavigateToFilter(string filterId) =>
        navigationManager.NavigateToSecureMailDeliveryListWithBatchFilter(filterId);
}
