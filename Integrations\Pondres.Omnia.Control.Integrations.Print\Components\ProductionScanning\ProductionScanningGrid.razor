﻿@using Pondres.Omnia.Control.Integrations.Print.Client

<div class="card shadow mb-4">
    <TelerikGrid Data="@Rows"
                 Class="grid-no-scroll"
                 Sortable="true"
                 Groupable="false"
                 Resizable="true"
                 Reorderable="true"
                 Pageable="false"
                 OnRowRender="OnRowRender">
        <GridColumns>
            <GridColumn Field="@nameof(BundleScanningRow.Batch)" Title="Batch" Width="15%" />
            <GridColumn Field="@nameof(BundleScanningRow.FileName)" Title="Bestands Naam" Width="25%" />
            <GridColumn Field="@nameof(BundleScanningRow.Format)" Title="Formaat" Width="10%" />
            <GridColumn Field="@nameof(BundleScanningRow.PostalDistributor)" Title="Postverspreider" Width="10%" />
            <GridColumn Field="@nameof(BundleScanningRow.Count)" Title="Aantal" Width="10%" />
            <GridColumn Field="@nameof(BundleScanningRow.Status)" Title="Status" Width="10%" />
            <GridCommandColumn Context="dataItem" Title="Gescanned" Width="10%">
                <GridCommandButton Rounded="@(ThemeConstants.Button.Rounded.Full)" ThemeColor="@(((BundleScanningRow)dataItem).IsScanned ? ThemeConstants.Button.ThemeColor.Success : ThemeConstants.Button.ThemeColor.Primary)" Enabled="((BundleScanningRow)dataItem).Status == MapPrintBundleState.WaitingForScan" OnClick="@OnBundleScannedClickedAsync">Scanned</GridCommandButton>
            </GridCommandColumn>

            <GridCommandColumn Context="dataItem" Title="Afmelden" Width="10%">
                <GridCommandButton Rounded="@(ThemeConstants.Button.Rounded.Full)" ThemeColor="@(((BundleScanningRow)dataItem).IsCompleted ? ThemeConstants.Button.ThemeColor.Success : ThemeConstants.Button.ThemeColor.Primary)" Enabled="((BundleScanningRow)dataItem).Status == MapPrintBundleState.Scanned" OnClick="@OnBundleCompleteClickedAsync">Afmelden</GridCommandButton>
            </GridCommandColumn>
        </GridColumns>
    </TelerikGrid>
</div>