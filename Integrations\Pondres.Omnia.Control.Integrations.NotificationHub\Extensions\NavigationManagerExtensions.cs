﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Helpers;
using Pondres.Omnia.Control.Integrations.NotificationHub.Models;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;

public static class NavigationManagerExtensions
{
    public static void NavigateToNotificationTraceListWithDefaultFilter(this NavigationManager navigationManager, NotificationTraceDefaultFilterFields filter) =>
        navigationManager.NavigateTo($"Notifications/Traces?{FilterHelper.ConvertToQueryParameter(filter)}");
}
