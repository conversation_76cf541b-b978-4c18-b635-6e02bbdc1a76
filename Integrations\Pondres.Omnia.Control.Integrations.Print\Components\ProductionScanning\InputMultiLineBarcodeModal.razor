﻿@using System.Diagnostics.CodeAnalysis
@using Pondres.Omnia.Control.Integrations.Common.Components.Modal;

@inherits InputBase<List<string>>

<ModalWindow @ref="EditWindow">
    <ModalHeader>
        Uitval prints
    </ModalHeader>
    <ModalBody>
        <div class="form-row">
            <div class="form-group col-sm-12">
                Iedere barcode moet op een nieuwe regel
            </div>
        </div>
        <div class="form-row">
            <div class="form-group col-sm-12">
                <EditForm EditContext="EditContext">
                    <InputTextArea @bind-Value="CurrentValueAsString" class="form-control" rows="20" maxlength="10000"></InputTextArea>
                </EditForm>
            </div>
        </div>
    </ModalBody>
    <ModalFooter>
        <button class="btn btn-secondary" @onclick="CloseModalAsync">Sluiten</button>
        <button class="btn btn-primary" @onclick="FailedBarcodeListAsync">Uitval verwerken</button>
    </ModalFooter>
</ModalWindow>