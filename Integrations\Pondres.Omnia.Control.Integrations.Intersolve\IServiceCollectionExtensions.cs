﻿using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Integrations.Intersolve.Client;
using Pondres.Omnia.Control.Integrations.Intersolve.Models;
using Pondres.Omnia.Control.Integrations.Intersolve.Services;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Intersolve
{
    public static class IServiceCollectionExtensions
    {
        public static IServiceCollection AddIntersolveServices(this IServiceCollection services, AppSettings appSettings)
        {
            services.AddHttpClient<IIntersolveService, IntersolveService>()
                .ConfigureBaseAddress(appSettings.IntersolveServiceUri)
                .ConfigureAuthenticationToken(appSettings.IntersolveServiceAuthToken);

            services.AddHttpClient(name: "intersolveClient")
                .ConfigureBaseAddress(appSettings.IntersolveServiceUri)
                .ConfigureAuthenticationToken(appSettings.IntersolveServiceAuthToken);

            services.AddHttpClient<IntersolveServiceClient>("intersolveClient");

            services.AddTransient<IntersolveOrderDefaultFilterContext>();
            services.AddTransient<IntersolveOrderDefaultFilterContext>();

            return services;
        }
    }
}
