﻿@page "/WMS/Documents"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.Control.Integrations.Warehouse.Components.Document
@using System.Collections.ObjectModel
@using Pondres.Omnia.Control.Integrations.Warehouse.Models
@using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument

@inherits CustomerFilterGridPageBase<WarehouseDocumentListItem, IWarehouseDocumentFilterContext>

<PageHeader Title="Documents" />
<PageBody>
    <div class="container-fluid">
        <DocumentGridFilter FilterChanged="OnFilterChangedAsync" />

        <DocumentGrid @ref="WarehouseDocumentGrid" Loading="LoadingGridItems" Documents="GridItems">
            <NavigationLinks>
                <BatchOrderLink CustomerReferences="WarehouseDocumentGrid.UniqueSelectedCustomerReferences" StyleClass="dropdown-item">Orders</BatchOrderLink>
            </NavigationLinks>
        </DocumentGrid>
    </div>
</PageBody>