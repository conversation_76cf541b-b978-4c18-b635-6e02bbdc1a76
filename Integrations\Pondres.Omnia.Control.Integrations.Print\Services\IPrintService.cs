﻿using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.Print.Client;
using Pondres.Omnia.Control.Integrations.Print.Models;

namespace Pondres.Omnia.Control.Integrations.Print.Services;

public interface IPrintService
{
    Task<PagedResultModel<DGCodeBatch>> GetBatchesAsync(PrintBundleDefaultFilter filter);
    Task<PagedResultModel<PrintBundleListItem>> GetAllBundlesByBatchFilterAsync(PrintBundleBatchFilter filter);
    Task<PagedResultModel<PrintBundleListItem>> GetAllBundlesByDefaultFilterAsync(PrintBundleDefaultFilter filter);
    Task<PagedResultModel<PrintDocumentListItem>> GetAllDocumentsForOrderIdAsync(Guid orderId, string continuationToken);
    Task<PagedResultModel<PrintDocumentListItem>> GetAllDocumentsByDefaultFilterAsync(PrintDocumentListFilter filter);
    Task<PagedResultModel<PrintDocumentListItem>> GetAllDocumentsByBatchFilterAsync(PrintDocumentBatchListFilter filter);
    Task ConfirmPrintBundleAsync(string bundleId, string username);
    Task ContinuePrintBundleAsync(string bundleId, bool force, string username);
    Task CancelPrintBundleAsync(string bundleId, bool force, bool reprint, string username);
    Task RemoveDocumentPrintBundleAsync(string bundleId, string documentId);
    Task<PrintBundleTotalsResult> GetPrintBundleTotalsAsync(PrintDashboardFilter filter);
    Task<PagedResultModel<PrintBundleListItem>> GetPrintBundlesByIdsAsync(IEnumerable<string> printBundleIds);
    Task<List<ReleaseSchedules>> GetReleaseSchedulesAsync();
    Task<ReleaseScheduleResult> CreateReleaseSchedulesAsync(ReleaseSchedules schedule);
    Task<ReleaseScheduleResult> UpdateReleaseSchedulesAsync(ReleaseSchedules schedules);
    Task DeleteReleaseSchedulesAsync(string scheduleId);
    Task<PrintBundleReleaseResponse> ReleasePrintBundleAsync(Guid bundleId, string username, PrintBundleOptions bundleOptions, DateTimeOffset releaseTime);
    Task CompletePrintBundleAsync(string bundleId, string username);
    Task ReprintSelectedPrintDocumentsAsync(List<Guid> orderIds, string customer, string username);
    Task<PrintBundleRawDetails> GetRawBundleDetailsAsync(string id);
    Task ScanPrintBundleAsync(string bundleId, string userName);
    Task<PrintBundleDetails> GetPrintBundleDetailsAsync(Guid bundleId);
}