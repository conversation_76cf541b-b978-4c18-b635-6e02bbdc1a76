﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Intersolve.Components.Configuration;
using Pondres.Omnia.Control.Integrations.Intersolve.Services;
using Pondres.Omnia.Intersolve.Contracts.Configuration;
using Pondres.Omnia.Control.Integrations.Intersolve.Extensions;
using Telerik.Blazor;
using Telerik.Blazor.Components;
using Pondres.Omnia.Control.Integrations.OrderHub.Extensions;
using System.Text.Json;
using ConfigurationDto = Pondres.Omnia.Intersolve.Contracts.Configuration.Configuration;

namespace Pondres.Omnia.Control.Pages.Intersolve.Configuration;

public partial class IntersolveConfigurationGrid : SelectableGridBase<ConfigurationDto>
{
    public IntersolveConfigurationEditor DialogEditor { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }

    [Inject]
    protected IIntersolveService IntersolveService { get; set; }

    public List<ConfigurationDto> Configurations { get; private set; }

    private readonly JsonSerializerOptions jsonOptions = new() { WriteIndented = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadConfigurationsAsync();

        await base.OnInitializedAsync();
    }

    private async Task LoadConfigurationsAsync()
    {
        await InvokeAsync(StateHasChanged);

        try
        {
            Configurations = await IntersolveService.GetAllConfigurationsAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }

        await InvokeAsync(StateHasChanged);
    }

    public async Task OnSaveActionAsync()
    {
        try
        {
            if (DialogEditor.IsNew)
                await IntersolveService.CreateConfigurationAsync(DialogEditor.Configuration.ToCreateDto());
            else
                await IntersolveService.UpdateConfigurationAsync(DialogEditor.Configuration.ToUpdateDto(), DialogEditor.Configuration.GetCustomerFromJson());

            await LoadConfigurationsAsync();

            await DialogEditor.CloseDialogAsync();
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    private async Task OnViewCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var config = await IntersolveService.GetConfigurationDetailsAsync((e.Item as ConfigurationDto).Customer);

            DialogEditor.ShowDialog(config.ToJson(jsonOptions), true);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }

    public void OnAddCommand()
    {
        DialogEditor.ShowDialog(new CreateConfiguration().ToJson(jsonOptions), false, true);
    }

    private async Task EditCommandAsync(GridCommandEventArgs e)
    {
        try
        {
            var config = await IntersolveService.GetConfigurationDetailsAsync((e.Item as ConfigurationDto).Customer);

            var updateConfig = config.ToJson(jsonOptions).ToUpdateDto();

            DialogEditor.ShowDialog(updateConfig.ToJson(jsonOptions), false);
        }
        catch (Exception exception)
        {
            ToastService.ShowError(exception.Message);
        }
    }
}