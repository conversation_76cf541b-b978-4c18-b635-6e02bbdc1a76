replicaCount: 1

namespace: solude

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-control"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets:
  - name: acr-pondres-secret

nameOverride: "control-service" # name of application
fullnameOverride: "control-service" # name of application

config:
  AzureAd__Instance: "https://login.microsoftonline.com/"
  AzureAd__Domain: "soludecx.onmicrosoft.com"
  AzureAd__TenantId: "a029d3e3-1af1-4648-92a5-fe019d1a7560"
  AzureAd__ClientId: "00000000-0000-0000-0000-000000000000" # TODO
  AzureAd__CallbackPath: "/signin-oidc"
  AzureAd__SignedOutCallbackPath: "/signout-callback-oidc"
  OmniaEnvironment: "#|OmniaEnvironment|#"
  PrintFolderPath: "\\\\TODO" # TODO
  WebBasePath: "/"
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  VaultName: "kv-sol-control-#|OmniaEnvironment|#"
  ReportingServiceUri: "#|ReportingServiceUri|#"
  OrderServiceUri: "#|OrderServiceUri|#"
  NotificationServiceUri: "#|NotificationServiceUri|#"
  PrintServiceUri: "#|PrintServiceUri|#"
  WarehouseServiceUri: "#|WarehouseServiceUri|#"
  CustomerServiceUri: "#|CustomerServiceUri|#"
  SecureMailServiceUri: "#|SecureMailServiceUri|#"
  ESafeServiceUri: "#|ESafeServiceUri|#"
  DemoServiceUri: "#|DemoServiceUri|#"
  EmailServiceUri: "#|EmailServiceUri|#"
  WebToOmniaServiceUri: "#|WebToOmniaServiceUri|#"
  IntersolveServiceUri: "#|IntersolveServiceUri|#"
  ScalerPrintInstructionsUri: "https://scaler-solude-#|OmniaEnvironment|#.solude.cx" # TODO

secrets: {}

podAnnotations: {}

podLabels:
  azure.workload.identity/use: "true"

service:
  type: ClusterIP
  port: 80
  containerPort: 8080

serviceAccount:
  enabled: true
  name: "#|ServiceAccountName|#"

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases: []
#  - ip: IP_ADDRESS_1
#    hostnames:
#      - HOST_NAME_1

ingress:
  enabled: true # enable if API needs to be accesible from outside the cluster
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: control-#|Solude.BaseDomain|#
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: ingress-tls-csi
      hosts:
        - control-#|Solude.BaseDomain|#

resources:
  limits:
    cpu: 500m
    memory: 768Mi
  requests:
    cpu: 200m
    memory: 256Mi

volumes:
  - name: secrets-store-inline
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "azure-tls"
  - name: tmp
    emptyDir: {}

volumeMounts:
  - name: secrets-store-inline
    mountPath: "/mnt/secrets-store"
    readOnly: true 
  - name: tmp
    mountPath: /tmp
    

nodeSelector: {}

tolerations: []

affinity: {}
