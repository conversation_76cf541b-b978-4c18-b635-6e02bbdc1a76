using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;

namespace Pondres.Omnia.Control.Integrations.Common.Components.Filters;

public partial class ListFilterInputDateTime
{
    [CascadingParameter]
    public ListFilter Filter { get; set; }

    [Parameter]
    public DateTimePickerFilterFields Value { get; set; }

    [Parameter]
    public string LabelText { get; set; }

    [Parameter]
    public EventCallback<DateTimePickerFilterFields> ValueChanged { get; set; }

    private EditContext EditContext { get; set; }

    protected override void OnInitialized()
    {
        EditContext = new EditContext(Value);
        EditContext.OnFieldChanged += InvokeValueChanged;
    }

    private async void InvokeValueChanged(object sender, FieldChangedEventArgs e)
    {
        await Filter.SubmitChangedFilterAsync();
    }

    private string GetLabelFrom() => string.IsNullOrEmpty(LabelText) ? "From" : $"From {LabelText}";

    private string GetLabelTo() => string.IsNullOrEmpty(LabelText) ? "To" : $"To {LabelText}";
}
