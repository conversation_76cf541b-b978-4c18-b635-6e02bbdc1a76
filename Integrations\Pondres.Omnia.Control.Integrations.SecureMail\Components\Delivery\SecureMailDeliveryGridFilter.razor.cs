using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.SecureMail.Models;

namespace Pondres.Omnia.Control.Integrations.SecureMail.Components.Delivery;

public partial class SecureMailDeliveryGridFilter
{
    [Inject]
    private IApiFilterManager FilterManager { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    [Inject]
    private SecureMailDeliveryDefaultFilterContext DefaultFilter { get; set; }

    [Inject]
    private SecureMailDeliveryBatchFilterContext BatchFilter { get; set; }

    [Parameter]
    public EventCallback<ISecureMailDeliveryFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilter, BatchFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<ISecureMailDeliveryFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);
        await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(ISecureMailDeliveryFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);
        await FilterChanged.InvokeAsync(filter);
    }
}