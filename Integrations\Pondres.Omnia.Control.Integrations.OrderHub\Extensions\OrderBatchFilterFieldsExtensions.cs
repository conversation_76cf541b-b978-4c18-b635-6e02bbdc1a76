﻿using Pondres.Omnia.Control.Integrations.OrderHub.Models;
using Pondres.Omnia.OrderHub.Contracts.Api.Order;

namespace Pondres.Omnia.Control.Integrations.OrderHub.Extensions;

public static class OrderBatchFilterFieldsExtensions
{
    public static OrderBatchSelectionFilter ToApiFilter(this OrderBatchFilterFields fields, string continuationToken, string currentCustomer) =>
        new()
        {
            Customer = currentCustomer,
            MaxPageSize = fields.PageSize,
            CustomerReferences = fields.CustomerReferences,
            ContinuationToken = continuationToken,
            OrderIds = fields.OrderIds
        };
}
