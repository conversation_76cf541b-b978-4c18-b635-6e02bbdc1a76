﻿using Pondres.Omnia.Control.Integrations.Warehouse.Models;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Integrations.Warehouse.Extensions;

public static class WarehouseDocumentBatchFilterFieldsExtensions
{

    public static WarehouseDocumentBatchFilter ToApiFilter(this WarehouseDocumentBatchFilterFields fields, string continuationToken, string currentCustomer) =>
        new()
        {
            Customer = currentCustomer,
            MaxPageSize = fields.PageSize,
            OrderIds = fields.OrderIds.Select(x => Guid.Parse(x)).ToList(),
            ContinuationToken = continuationToken
        };
}
