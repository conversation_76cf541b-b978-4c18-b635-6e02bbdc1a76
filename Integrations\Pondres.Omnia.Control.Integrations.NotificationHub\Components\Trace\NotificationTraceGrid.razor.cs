﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Grid;
using Pondres.Omnia.Control.Integrations.Common.Models.Common;
using Pondres.Omnia.Control.Integrations.NotificationHub.Extensions;
using Pondres.Omnia.Control.Integrations.NotificationHub.Services;
using Pondres.Omnia.NotificationHub.Contracts.Api.Trace;
using System.Collections.ObjectModel;
using Telerik.Blazor.Components;

namespace Pondres.Omnia.Control.Integrations.NotificationHub.Components.Trace;

public partial class NotificationTraceGrid : SelectableGridBase<NotificationTraceListItem>
{
    [Inject]
    private INotificationTraceService NotificationTraceService { get; set; }

    [Parameter]
    public ObservableCollection<NotificationTraceListItem> Traces { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    [Parameter]
    public string CustomerId { get; set; }

    [Parameter]
    public EventCallback OnRefresh { get; set; }

    private void NavigateToTraceDetails(string traceId) =>
        NavigationManager.NavigateTo($"Notifications/TraceDetails/{CustomerId}/{traceId}");

    private void OnDetailsCommand(GridCommandEventArgs e) =>
         NavigateToTraceDetails((e.Item as NotificationTraceListItem).TraceId);

    private void OnRowDoubleClick(GridRowClickEventArgs e) =>
         NavigateToTraceDetails((e.Item as NotificationTraceListItem).TraceId);

    private static void ApplyBackground(GridCellRenderEventArgs eventArgs) =>
        eventArgs.Class = (eventArgs.Item as NotificationTraceListItem).GetStatusColorStyle();

    private async IAsyncEnumerable<ActionListButtonResult> RetryTraceFailedOutputsAsync()
    {
        foreach (var trace in DataGrid.SelectedItems)
        {
            ActionListButtonResult result;
            try
            {
                await NotificationTraceService.RetryTraceFailedOutputsForCustomerAsync(trace.TraceId, CustomerId);
                result = new ActionListButtonResult { Key = trace.EventType, IsSuccessful = true };
            }
            catch (Exception ex)
            {
                result = new ActionListButtonResult { Key = trace.EventType, FailedResultMessage = ex.Message };
            }
            yield return result;
        }
    }

    private bool HasActionOfType(NotificationTraceAction action) =>
        DataGrid.SelectedItems.Any(t => t.Actions.Any(a => a == action.ToString()));
}