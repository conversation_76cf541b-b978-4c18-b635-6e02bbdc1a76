﻿using Microsoft.Extensions.DependencyInjection;
using Pondres.Omnia.Control.Integrations.Demo.Services;
using Pondres.Omnia.Control.Integrations.Common.Extensions;
using Pondres.Omnia.Control.Settings;

namespace Pondres.Omnia.Control.Integrations.Demo
{
    public static class IServiceCollectionExtensions
    {
        public static IServiceCollection AddDemoServices(this IServiceCollection services, AppSettings appSettings)
        {
            var serviceUri = appSettings.DemoServiceUri;
            var serviceAuthToken = appSettings.DemoServiceAuthToken;

            services.AddHttpClient<IEndToEndTestService, EndToEndTestService>()
                .ConfigureBaseAddress(serviceUri)
                .ConfigureAuthenticationToken(serviceAuthToken);

            return services;
        }
    }
}
