﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.OpsGenie.Models;
using Pondres.Omnia.Control.Integrations.OpsGenie.Services;

namespace Pondres.Omnia.Control.Pages.Dashboard;

public partial class Dashboard : IDisposable
{
    [Inject]
    private IOpsGenieService OpsGenieService { get; set; }

    private PeriodicTimer periodicTimer;
    private bool isDisposing;

    public bool IsLoading { get; private set; }
    public bool OpsGenieFailed { get; set; }
    public AlertsViewModel PlatformAlerts { get; set; }
    public AlertsViewModel CustomerAlerts { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadOpsGenieAlertsAsync();

            InitializeTimer();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async void InitializeTimer()
    {
        if (!isDisposing)
            periodicTimer = new(TimeSpan.FromSeconds(30));

        while (periodicTimer != null && !isDisposing && await periodicTimer.WaitForNextTickAsync())
        {
            await LoadOpsGenieAlertsAsync();
        }
    }

    private async Task LoadOpsGenieAlertsAsync()
    {
        try
        {
            IsLoading = true;
            await InvokeAsync(StateHasChanged);

            PlatformAlerts = await OpsGenieService.GetPlatformAlertCountsAsync();
            CustomerAlerts = await OpsGenieService.GetCustomerAlertCountsAsync();
        }
        catch (Exception)
        {
            OpsGenieFailed = true;
        }

        IsLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        isDisposing = disposing;

        if (isDisposing)
            periodicTimer?.Dispose();

        periodicTimer = null;
    }
}