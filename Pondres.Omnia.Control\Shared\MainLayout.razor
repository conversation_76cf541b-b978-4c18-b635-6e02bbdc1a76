﻿@using Blazored.Toast
@using Pondres.Omnia.Control.Integrations.Common.Helpers
@using Pondres.Omnia.Control.Shared.Components

@layout TelerikLayout
@inherits LayoutComponentBase


<div class="page">
    <div class="sidebar sidebar-omnia">
        <NavMenu />
    </div>

    <div class="main">
        @Body
    </div>
</div>

<BlazoredToasts Position="Blazored.Toast.Configuration.ToastPosition.BottomRight"
                ShowProgressBar="true"
                ErrorClass="toast-error-override"
                SuccessClass="toast-success-override"
                WarningClass="toast-warning-override"
                InfoClass="toast-info-override"
                Timeout="10" />
