﻿using Pondres.Omnia.Control.Integrations.Customer.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Components.Document;
using Pondres.Omnia.Control.Integrations.Warehouse.Models;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Pages.Warehouse.Document;

public partial class Grid : CustomerFilterGridPageBase<WarehouseDocumentListItem, IWarehouseDocumentFilterContext>
{
    public DocumentGrid WarehouseDocumentGrid { get; set; }
}