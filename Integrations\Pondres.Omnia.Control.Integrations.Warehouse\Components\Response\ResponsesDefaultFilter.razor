﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Filters
@using Pondres.Omnia.Warehouse.Contracts.Api.Response

<ListFilter Filter="Filter" FilterChanged="OnFilterChangedAsync">

    <ListFilterRow>
        <ListFilterInputText @bind-Value="Filter.Fields.FileName" LabelText="File Name" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.MessageType" LabelText="Message Type" Options="MessageTypes" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.StateTypeName" LabelText="State" Options="Enum.GetNames(typeof(ResponseStateType)).ToList()" />
        <ListFilterInputSelect @bind-Value="Filter.Fields.ResultTypeName" LabelText="Result" Options="Enum.GetNames(typeof(ResponseResultType)).ToList()" />
    </ListFilterRow>

    <ListFilterRow>
        <ListFilterInputDateTime @bind-Value="Filter.Fields.CreatedDateTime" LabelText="created on" />
    </ListFilterRow>

    <ListFilterPagenator @bind-Value="Filter.Fields.PageSize" />

</ListFilter>


