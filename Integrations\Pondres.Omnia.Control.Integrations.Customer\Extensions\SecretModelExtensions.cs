﻿using Pondres.Omnia.Control.Integrations.Customer.Models;
using Pondres.Omnia.Customer.Contracts.Api;

namespace Pondres.Omnia.Control.Integrations.Customer.Extensions;

public static class SecretModelExtensions
{
    public static CreateOrUpdateSecret ToCreateOrUpdateSecretModel(this SecretModel model) =>
        new()
        {
            CustomerId = model.CustomerId,
            SecretName = model.SecretName,
            SecretValue = model.SecretValue
        };

    public static SecretModel ToModel(this VaultSecretListItem item) =>
        new()
        {
            SecretName = item.Name
        };
}