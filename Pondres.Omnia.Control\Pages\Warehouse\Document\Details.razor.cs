using Blazored.Toast.Services;
using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Warehouse.Services;
using Pondres.Omnia.Warehouse.Contracts.Api.WarehouseDocument;

namespace Pondres.Omnia.Control.Pages.Warehouse.Document;

public partial class Details
{
    [Parameter]
    public string Customer { get; set; }

    [Parameter]
    public string DocumentId { get; set; }

    private WarehouseDocumentInformation Document { get; set; }

    [Inject]
    public IToastService ToastService { get; set; }

    [Inject]
    public IWarehouseDocumentService DocumentService { get; set; }

    private bool IsLoading { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await RefreshPageAsync();
    }

    private async Task RefreshPageAsync()
    {
        IsLoading = true;

        try
        {
            Document = await DocumentService.GetDocumentDetailsAsync(Customer, DocumentId);
        }
        catch (Exception ex)
        {
            ToastService.ShowError(ex.Message);
        }

        IsLoading = false;

        await InvokeAsync(StateHasChanged);
    }
}
