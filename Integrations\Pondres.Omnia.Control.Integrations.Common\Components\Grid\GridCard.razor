﻿@using Pondres.Omnia.Control.Integrations.Common.Components.Common
@using Pondres.Omnia.Control.Integrations.Common.Components.Table
@typeparam TItem

<TelerikCard>
    <CardHeader>
        <CardTitle>
            <a class="cursor-pointer" @onclick="@ToggleAsync">@CardTitle</a>
        </CardTitle>
    </CardHeader>
    <CardBody>
        <TelerikAnimationContainer @ref="@AnimationContainer" ShowDelay="1" HideDelay="1" AnimationDuration="300" AnimationType="@AnimationType.RevealVertical" ParentInlineStyle="display: block; position: relative;">
            @if (!GridItems.Any() && !Loading)
            {
                <div class="text-center py-2">No Data</div>
            }
            else
            {
                <table class="table table-hover mb-0 cursor-pointer" id="@(Title)Table">
                    <thead>
                        <tr>
                            @TableHeaderContent
                        </tr>
                    </thead>
                    @foreach (var item in GridItems)
                    {
                        <TableRowWithDetails RowColor="@GetRawColorFunc(item)">
                            <RowContent>
                                @TableRowContent(item)
                            </RowContent>
                            <DetailsContent>
                                @if (TableRowExpandableContent != null)
                                {
                                    <div class="container-fluid py-2">
                                        @TableRowExpandableContent(item)
                                    </div>
                                }
                                <pre class="container-fluid mb-0">
                                <code>@GetItemJson(item)</code>
                                                                    </pre>
                            </DetailsContent>
                        </TableRowWithDetails>

                        @if (ContinuationToken != null)
                        {
                            <tr>
                                <td>
                                    <button class="btn btn-primary" @onclick="TryLoadNextPageAsync">Load more</button>
                                </td>
                            </tr>
                        }
                    }
                </table>
            }
            @if (Loading)
            {
                <LoadingSpinner Show="Loading"></LoadingSpinner>
            }
        </TelerikAnimationContainer>
    </CardBody>
</TelerikCard>


