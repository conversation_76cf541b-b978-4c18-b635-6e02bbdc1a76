﻿@page "/E2E/Results"
@using Pondres.Omnia.Control.Integrations.Demo.Components
@using Pondres.Omnia.Demo.Contracts.Api;
@using Pondres.Omnia.Demo.Contracts.EndToEnd;

<PageHeader Title="EndToEnd Resultaten" />
<PageBody>
    @if (Loading)
    {
        <LoadingSpinner />
    }
    else
    {
        <div class="card shadow">
            <TelerikGrid Data="AvailableTestRunResults"
                     Pageable="false"
                     @ref="@GridRef">
                     <DetailTemplate>
                         @{
                            <EndToEndTestRunDetails TestRunId="@context.Id" />
                         }
                     </DetailTemplate>
                <GridColumns>
                    <GridColumn OnCellRender="@ApplyBackground" Field="@nameof(ApiTestRunResultItem.Id)" Title="Id" Editable="false" />
                    <GridColumn OnCellRender="@ApplyBackground" Field="@nameof(ApiTestRunResultItem.Success)" Title="Success" Editable="false" />
                    <GridColumn OnCellRender="@ApplyBackground" Field="@nameof(ApiTestRunResultItem.StartedOn)" Title="StartedOn" Editable="false" />
                    <GridColumn OnCellRender="@ApplyBackground" Field="@nameof(ApiTestRunResultItem.CompletedOn)" Title="CompletedOn" Editable="false" />
                </GridColumns>
            </TelerikGrid>
        </div>
    }
</PageBody>

@code {

}
