﻿@page "/Print/ProductionScanning"
@using Pondres.Omnia.Control.Integrations.Print.Components.ProductionScanning
@using Pondres.Omnia.Control.Integrations.Customer.Components

<PageHeader Title="Production Scanning">
    <CustomerSelection CustomerChanged="@OnCustomerChangedAsync" SelectedCustomerId="@Filter.Customer" Customers="Customers" />
</PageHeader>

<PageBody>
    <CascadingValue Value=Bundles>
        <ProductionScanningInfo/>
    </CascadingValue>
    <EditForm EditContext="@EditContext">
        <CascadingValue Value=Batches>
            <CascadingValue Value=Bundles>
                <CascadingValue Value=Filter>
                    <ProductionScanningInput FilterSubmitted="OnFilterChangedAsync" @ref="ProductionScanningInput"/>
                </CascadingValue>
            </CascadingValue>
        </CascadingValue>
        <CascadingValue Value=Bundles>
            <ProductionScanningGrid Loading="IsLoading" OnBundleComplete="OnBundleCompleteAsync" OnBundleScanned="OnBundleScannedAsync"/>
        </CascadingValue>
    </EditForm>
    <LoadingSpinner Show="IsLoading" />
</PageBody>


