﻿using Pondres.Omnia.Control.Integrations.Common.Services;
using Pondres.Omnia.Control.Integrations.Customer.Models;
using Pondres.Omnia.Customer.Contracts.Api;

namespace Pondres.Omnia.Control.Integrations.Customer.Services;

public class CustomerService : BaseApiService, ICustomerService
{
    public CustomerService(HttpClient httpClient)
        : base(httpClient)
    { }

    public async Task<List<CustomerModel>> GetAllCustomersAsync()
    {
        var result = await GetAsync<List<CustomerListItem>>($"customer/customer/list");

        return result.Select(result => new CustomerModel { Id = result.Id, Name = result.Name }).ToList();
    }

    public async Task<CustomerDetailsModel> GetCustomerDetailsAsync(string customerId)
    {
        var result = await GetAsync<Omnia.Customer.Contracts.Api.Customer>($"customer/customer/details?customerId={customerId}");

        return new CustomerDetailsModel { Id = result.Id, Name = result.Name, VaultName = result.VaultName, StorageAccountName = result.StorageAccountName };
    }

    public async Task CreateCustomerAsync(CustomerDetailsModel model) =>
        await PostAsync(model, "customer/customer/create");

    public async Task UpdateCustomerAsync(CustomerDetailsModel model) =>
        await PostAsync(model, "customer/customer/update");

    public async Task DisableCustomerAsync(string customerId)
    {
        var route = "customer/customer/disable";
        var queryParams = new Dictionary<string, string>
        {
            { "id", customerId }
        };
        await PostWithParametersAsync(route, queryParams);
    }
}