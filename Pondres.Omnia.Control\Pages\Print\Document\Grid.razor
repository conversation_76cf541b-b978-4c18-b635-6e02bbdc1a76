﻿@page "/Print/Documents"

@using Pondres.Omnia.Control.Integrations.Common.Components.Grid
@using Pondres.Omnia.Control.Integrations.Customer.Components
@using Pondres.Omnia.Control.Integrations.OrderHub.Components.Link
@using Pondres.Omnia.Control.Integrations.Print.Client;
@using Pondres.Omnia.Control.Integrations.Print.Components.Document
@using Pondres.Omnia.Control.Integrations.Print.Models

@inherits CustomerFilterGridPageBase<PrintDocumentListItem, IPrintDocumentFilterContext>

<PageHeader Title="Print Documents" />
<PageBody>
    <div class="container-fluid">
        <DocumentsGridFilter FilterChanged="OnFilterChangedAsync" />

        <DocumentsGrid Documents="GridItems" Loading="LoadingGridItems" OnRefresh="OnRefresh">
            <OrderDetailsLink>
                <OrderDetailsLink OrderId="@context.OrderMetadata.OrderId.ToString()"
                                  Customer="@context.OrderMetadata.Customer">
                    @context.OrderMetadata.CustomerReference
                </OrderDetailsLink>
            </OrderDetailsLink>
        </DocumentsGrid>
    </div>
</PageBody>