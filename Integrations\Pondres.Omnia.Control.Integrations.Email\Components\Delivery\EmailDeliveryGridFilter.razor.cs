﻿using Microsoft.AspNetCore.Components;
using Pondres.Omnia.Control.Integrations.Common.Components.Panel.Tab;
using Pondres.Omnia.Control.Integrations.Common.Manager;
using Pondres.Omnia.Control.Integrations.Email.Models;

namespace Pondres.Omnia.Control.Integrations.Email.Components.Delivery;

public partial class EmailDeliveryGridFilter
{
    [Inject]
    private IApiFilterManager FilterManager { get; set; }

    [Parameter]
    public List<string> Flows { get; set; }

    [Inject]
    private EmailDeliveryDefaultFilterContext DefaultFilter { get; set; }

    [Inject]
    private EmailDeliveryBatchFilterContext BatchFilter { get; set; }

    [Parameter]
    public EventCallback<IEmailDeliveryFilterContext> FilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        FilterManager.SetFilters(DefaultFilter, BatchFilter);
        await FilterManager.SetInitialFilterFromUriAsync();
    }

    private async void OnFilterTabChanged(TabPanelTab<IEmailDeliveryFilterContext> selectedTab)
    {
        await FilterManager.UpdateCurrentFilterAsync(selectedTab.Data, initial: false, save: false);
        await FilterChanged.InvokeAsync(selectedTab.Data);
    }

    private async void OnFilterChanged(IEmailDeliveryFilterContext filter)
    {
        await FilterManager.UpdateCurrentFilterAsync(filter, initial: false, save: true);
        await FilterChanged.InvokeAsync(filter);
    }
}